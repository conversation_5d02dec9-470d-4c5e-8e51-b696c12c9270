/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooLazyInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { ARRANGE_PROFILE } from "./profile";

export const arrangeInternationalization: SudooLazyInternationalization<ARRANGE_PROFILE> =
    SudooLazyInternationalization.create<ARRANGE_PROFILE>(
        DEFAULT_LOCALE,
    );

arrangeInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/en-US").then(
        (module) => module.enUSArrangeProfile,
    ),
);

arrangeInternationalization.mergeLazyProfile(
    IETF_LOCALE.JAPANESE_JAPAN,
    import("./locale/ja-JP").then(
        (module) => module.jaJPArrangeProfile,
    ),
);

arrangeInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    import("./locale/zh-CN").then(
        (module) => module.zhCNArrangeProfile,
    ),
);
