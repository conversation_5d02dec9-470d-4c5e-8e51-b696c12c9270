/**
 * <AUTHOR>
 * @namespace Common_Components_MagicButton_Hooks
 * @description Use Magic Button
 */

import { HUMMINGBIRD_MAGIC_BUTTON_DEFAULT_ACTION_MODE, HUMMINGBIRD_PREFERENCE_ITEM, usePreferenceItem } from "@imbricate-hummingbird/configuration";
import { useCallback, useMemo, useState } from "react";
import { MagicButtonItem } from "../types/button-item";

export type UseMagicButtonResultCategory = {

    readonly categoryKey: string;
    readonly categoryTitle: string;

    readonly categoryOrder?: number;
};

export type UseMagicButtonResult = {

    readonly categories: UseMagicButtonResultCategory[];
    readonly items: MagicButtonItem[];
    readonly lastExecutedItem: MagicButtonItem | undefined;

    readonly addItem: (item: MagicButtonItem) => void;
    readonly removeItem: (itemKey: string) => void;

    readonly onExecuteItem: (itemKey: string) => void;
};

export const useMagicButton = (
    initialCategories: UseMagicButtonResultCategory[],
): UseMagicButtonResult => {

    const magicButtonDefaultActionMode = usePreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.HUMMINGBIRD_MAGIC_BUTTON_DEFAULT_ACTION_MODE,
    );

    const [items, setItems] = useState<MagicButtonItem[]>([]);
    const [lastExecutedItem, setLastExecutedItem] = useState<string | undefined>(undefined);

    const addItem = useCallback((
        item: MagicButtonItem,
    ) => {

        setItems((prev: MagicButtonItem[]) => {

            const existingItem = prev.find((
                existingItem: MagicButtonItem,
            ) => {
                return existingItem.itemKey === item.itemKey;
            });

            if (existingItem) {
                return prev;
            }
            return [...prev, item];
        });

        if (magicButtonDefaultActionMode.preference === HUMMINGBIRD_MAGIC_BUTTON_DEFAULT_ACTION_MODE.LAST_USED_OR_DEFAULT_ACTION
            && typeof lastExecutedItem === "undefined"
            && item.defaultQuickAccess) {
            setLastExecutedItem(item.itemKey);
        }
    }, [lastExecutedItem]);

    const removeItem = useCallback((
        itemKey: string,
    ) => {

        setItems((prev: MagicButtonItem[]) => prev.filter((
            item: MagicButtonItem,
        ) => {

            if (item.itemKey === itemKey) {
                return false;
            }
            return true;
        }));

        if (lastExecutedItem === itemKey) {
            setLastExecutedItem(undefined);
        }
    }, [lastExecutedItem]);

    const onExecuteItem = useCallback((
        itemKey: string,
    ) => {

        const item = items.find((
            item: MagicButtonItem,
        ) => {

            return item.itemKey === itemKey;
        });

        if (!item) {
            return;
        }

        if (item.quickAccess) {
            setLastExecutedItem(itemKey);
        }
    }, [items]);

    const fixedLastExecutedItem = useMemo(() => {

        if (magicButtonDefaultActionMode.preference === HUMMINGBIRD_MAGIC_BUTTON_DEFAULT_ACTION_MODE.DISABLED) {
            return undefined;
        }

        return items.find((item) => item.itemKey === lastExecutedItem);
    }, [items, lastExecutedItem]);

    return {
        categories: initialCategories,
        items,
        lastExecutedItem: fixedLastExecutedItem,
        addItem,
        removeItem,
        onExecuteItem,
    };
};
