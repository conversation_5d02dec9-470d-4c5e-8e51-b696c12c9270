/**
 * <AUTHOR>
 * @namespace Property_Utils
 * @description Find Property
 */

import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaProperty } from "@imbricate/core";
import { OldDataCentral } from "../../data/old-data-central";
import { createFindDatabaseForPropertyAction } from "../actions/find-database-for-property";
import { createFindDocumentForPropertyAction } from "../actions/find-document-for-property";
import { S_UsePropertyNotFound, UsePropertyResponse, UsePropertyResponseSymbol } from "../types/use-property";

export const findProperty = async (
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    propertyUniqueIdentifier: string,
): Promise<UsePropertyResponse | UsePropertyResponseSymbol> => {

    const actionCentral = ActionCentral.getInstance();
    const oldDataCentral = OldDataCentral.getInstance();

    const database = await actionCentral.executeAction(
        async (
            actionIdentifier: string,
            recordIdentifier: string,
        ) => {

            return await oldDataCentral.wideOriginGetDatabase(
                databaseUniqueIdentifier,
                actionIdentifier,
                recordIdentifier,
            );
        },
        createFindDatabaseForPropertyAction(
            databaseUniqueIdentifier,
            propertyUniqueIdentifier,
            {
                executer: import.meta.url,
            },
        ),
    );

    const document = await actionCentral.executeAction(
        async (
            actionIdentifier: string,
            recordIdentifier: string,
        ) => {

            return await oldDataCentral.wideOriginGetDocument(
                databaseUniqueIdentifier,
                documentUniqueIdentifier,
                actionIdentifier,
                recordIdentifier,
            );
        },
        createFindDocumentForPropertyAction(
            databaseUniqueIdentifier,
            documentUniqueIdentifier,
            propertyUniqueIdentifier,
            {
                executer: import.meta.url,
            },
        ),
    );

    if (typeof document === "symbol"
        || typeof database === "symbol") {
        return S_UsePropertyNotFound;
    }

    const targetProperty = database.database.schema.properties.find((
        property: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>,
    ) => {
        return property.propertyIdentifier === propertyUniqueIdentifier;
    });

    if (!targetProperty) {
        return S_UsePropertyNotFound;
    }

    const relatedProperty = document.document.getProperty(targetProperty.propertyIdentifier);

    if (typeof relatedProperty === "symbol") {
        return S_UsePropertyNotFound;
    }

    return {
        schemaProperty: targetProperty,
        documentProperty: relatedProperty.property,
    };
};
