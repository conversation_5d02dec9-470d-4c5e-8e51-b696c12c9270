/**
 * <AUTHOR>
 * @namespace Config_Panels
 * @description ImbriScript Panel
 */

import React, { FC } from "react";
import { ConfigImbriScriptLogCard } from "../components/imbriscript/log-card";
import { ConfigImbricateScriptOutlookCard } from "../components/imbriscript/outlook-card";
import { ConfigImbriScriptQuickExecuteCard } from "../components/imbriscript/quick-execute";

export type ConfigImbriScriptPanelProps = {
};

// LAZY LOAD ONLY
const ConfigImbriScriptPanel: FC<ConfigImbriScriptPanelProps> = (
    _props: ConfigImbriScriptPanelProps,
) => {

    return (<div
        className="flex flex-col gap-2 pr-2 py-2 flex-1"
    >
        <ConfigImbriScriptLogCard />
        <ConfigImbriScriptQuickExecuteCard />
        <ConfigImbricateScriptOutlookCard />
    </div>);
};
export default ConfigImbriScriptPanel;
