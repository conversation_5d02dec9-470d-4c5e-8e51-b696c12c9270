/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Contexts
 * @description Doc Cast Context
 */

import React from "react";
import { DOC_CAST_SOURCE_TYPE, DocCastSource } from "../types/source";

export type DocCastModalContextProps = {

    readonly open: boolean;
    readonly onOpenChange: (open: boolean) => void;

    readonly addSources: (sources: DocCastSource<DOC_CAST_SOURCE_TYPE>[]) => void;
    readonly clearSources: () => void;

    readonly sources: DocCastSource<DOC_CAST_SOURCE_TYPE>[];
};

export const DocCastModalContext: React.Context<DocCastModalContextProps> =
    React.createContext<DocCastModalContextProps>({
        open: false,
        onOpenChange: () => { },
        addSources: () => { },
        clearSources: () => { },
        sources: [],
    });

export const useDocCastModalContext = (): DocCastModalContextProps => {

    const context: DocCastModalContextProps =
        React.useContext(DocCastModalContext);

    return context;
};
