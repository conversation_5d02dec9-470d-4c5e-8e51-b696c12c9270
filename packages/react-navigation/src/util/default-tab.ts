/**
 * <AUTHOR>
 * @namespace Navigation_Util
 * @description Default Tab
 */

import { HUMMINGBIRD_NAVIGATION_TABS_DEFAULT_TAB_MODE, HUMMINGBIRD_PASSIVE_DATA_ITEM, HUMMINGBIRD_PREFERENCE_ITEM, PassiveDataController, PreferenceController } from "@imbricate-hummingbird/configuration";

export const getNavigationDefaultTab = (): string => {

    const defaultTabMode = PreferenceController.getPreference(
        HUMMINGBIRD_PREFERENCE_ITEM.NAVIGATION_TABS_DEFAULT_TAB_MODE,
    );

    if (defaultTabMode === HUMMINGBIRD_NAVIGATION_TABS_DEFAULT_TAB_MODE.ORIGINS) {
        return "origins";
    }

    if (defaultTabMode === HUMMINGBIRD_NAVIGATION_TABS_DEFAULT_TAB_MODE.LENSES) {
        return "lenses";
    }

    if (defaultTabMode === HUMMINGBIRD_NAVIGATION_TABS_DEFAULT_TAB_MODE.DATABASES) {
        return "databases";
    }

    if (defaultTabMode === HUMMINGBIRD_NAVIGATION_TABS_DEFAULT_TAB_MODE.LAST_USED) {

        const lastUsedTab = PassiveDataController.getPassiveData(
            HUMMINGBIRD_PASSIVE_DATA_ITEM.NAVIGATION_LAST_USED_TAB,
        );

        if (lastUsedTab) {
            return lastUsedTab;
        }

        return "lenses";
    }

    throw new Error("Invalid default tab mode");
};

export const putNavigationLastUsedTab = (tab: string): void => {

    PassiveDataController.setPassiveData(
        HUMMINGBIRD_PASSIVE_DATA_ITEM.NAVIGATION_LAST_USED_TAB,
        tab,
    );
};
