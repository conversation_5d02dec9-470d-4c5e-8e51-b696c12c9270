/**
 * <AUTHOR>
 * @namespace Origin_Components
 * @description Origin Card
 */

import { getLocalizedText } from "@/internationalization/util/get-localized-text";
import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { useLocale } from "@imbricate-hummingbird/internationalization";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { openSuccessToaster } from "@imbricate-hummingbird/react-components";
import { UIDropdown, createUIButton } from "@imbricate-hummingbird/ui";
import { IMBRICATE_ORIGIN_FEATURE, checkImbricateOriginFeatureSupported } from "@imbricate/core";
import { FC } from "react";
import { MdOutlineAttractions } from "react-icons/md";
import { createExecuteOriginOriginActionAction } from "../../actions/execute-origin-origin-action";
import { useOldOriginOriginActions } from "../../hooks/use-origin-origin-actions";

export type OriginActionDropdownProps = {

    readonly origin: ImbricateOriginObject;
};

export const OriginActionDropdown: FC<OriginActionDropdownProps> = (
    props: OriginActionDropdownProps,
) => {

    const locale = useLocale();

    const actions = useOldOriginOriginActions(props.origin);

    const supportedFeatures = props.origin.origin.supportedFeatures;

    const isSupported = checkImbricateOriginFeatureSupported(
        supportedFeatures,
        IMBRICATE_ORIGIN_FEATURE.ORIGIN_GET_ORIGIN_ACTIONS,
    );

    if (!isSupported || actions.length === 0) {
        return null;
    }

    return (<UIDropdown
        trigger={createUIButton({
            startContent: (<MdOutlineAttractions
                className="text-large"
            />),
            color: "secondary",
            variant: "flat",
            children: "Origin Actions",
        })}
        categories={[
            {
                categoryKey: "origin-actions",
                title: "Origin Actions",
                items: actions.map((action) => {

                    return {
                        itemKey: action.actionIdentifier,
                        content: getLocalizedText(locale, action.actionName, action.defaultLocale),
                        description: (<div
                            className="flex flex-col gap-0"
                        >
                            <div>
                                {getLocalizedText(locale, action.actionDescription, action.defaultLocale)}
                            </div>
                            <div
                                className="font-mono"
                            >
                                {action.actionIdentifier}
                            </div>
                        </div>),
                        onPress: async () => {

                            const result = await ActionCentral.getInstance().executeAction(
                                async () => {
                                    return await props.origin.origin.executeOriginAction({
                                        actionIdentifier: action.actionIdentifier,
                                        parameters: {},
                                    });
                                },
                                createExecuteOriginOriginActionAction(
                                    props.origin,
                                    {
                                        actionIdentifier: action.actionIdentifier,
                                        parameters: {},
                                    },
                                    {
                                        executer: import.meta.url,
                                    },
                                ),
                            );

                            openSuccessToaster(JSON.stringify(result, null, 2));
                        },
                    };
                }),
            },
        ]}
    />);
};
