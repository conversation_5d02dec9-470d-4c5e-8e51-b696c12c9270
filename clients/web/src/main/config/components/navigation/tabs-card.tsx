/**
 * <AUTHOR>
 * @namespace Config_Components_Navigation
 * @description Tabs Card
 */

import { HUMMINGBIRD_NAVIGATION_TABS_DEFAULT_TAB_MODE, HUMMINGBIRD_NAVIGATION_TABS_DISPLAY_MODE, HUMMINGBIRD_PREFERENCE_ITEM, usePreferenceItem } from "@imbricate-hummingbird/configuration";
import { FormattedRadioGroup, FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { PiTabsFill } from "react-icons/pi";
import { useConfigFormat } from "../../internationalization/hook";
import { CONFIG_PROFILE } from "../../internationalization/profile";

export type ConfigNavigationTabsCardProps = {
};

export const ConfigNavigationTabsCard: FC<ConfigNavigationTabsCardProps> = (
    _props: ConfigNavigationTabsCardProps,
) => {

    const configFormat = useConfigFormat();

    const navigationTabsDefaultTabMode = usePreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.NAVIGATION_TABS_DEFAULT_TAB_MODE,
    );

    const navigationTabsDisplayMode = usePreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.NAVIGATION_TABS_DISPLAY_MODE,
    );

    return (<StyledCard>
        <UICardHeader>
            <PiTabsFill />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.NAVIGATION_TABS)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-2"
        >
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                    className="text-sm text-gray-500"
                >
                    {configFormat.get(CONFIG_PROFILE.NAVIGATION_TABS_DISPLAY_MODE_DESCRIPTION)}
                </FormattedText>
            </div>
            <FormattedRadioGroup
                label={configFormat.get(CONFIG_PROFILE.NAVIGATION_TABS_DISPLAY_MODE)}
                selected={navigationTabsDisplayMode.preference.toString()}
                onSelectedChange={(newSelected: string) => {

                    navigationTabsDisplayMode.updatePreference(
                        newSelected as HUMMINGBIRD_NAVIGATION_TABS_DISPLAY_MODE,
                    );
                }}
                format={configFormat}
                items={[
                    {
                        itemKey: HUMMINGBIRD_NAVIGATION_TABS_DISPLAY_MODE.TEXT,
                        itemProfileItem: CONFIG_PROFILE.TEXT,
                    },
                    {
                        itemKey: HUMMINGBIRD_NAVIGATION_TABS_DISPLAY_MODE.ICON,
                        itemProfileItem: CONFIG_PROFILE.ICON,
                    },
                ]}
            />
        </UICardBody>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-2"
        >
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                    className="text-sm text-gray-500"
                >
                    {configFormat.get(CONFIG_PROFILE.NAVIGATION_TABS_DEFAULT_TAB_MODE_DESCRIPTION)}
                </FormattedText>
            </div>
            <FormattedRadioGroup
                label={configFormat.get(CONFIG_PROFILE.NAVIGATION_TABS_DEFAULT_TAB_MODE)}
                selected={navigationTabsDefaultTabMode.preference.toString()}
                onSelectedChange={(newSelected: string) => {

                    navigationTabsDefaultTabMode.updatePreference(
                        newSelected as HUMMINGBIRD_NAVIGATION_TABS_DEFAULT_TAB_MODE,
                    );
                }}
                format={configFormat}
                items={[
                    {
                        itemKey: HUMMINGBIRD_NAVIGATION_TABS_DEFAULT_TAB_MODE.ORIGINS,
                        itemProfileItem: CONFIG_PROFILE.ORIGINS,
                    },
                    {
                        itemKey: HUMMINGBIRD_NAVIGATION_TABS_DEFAULT_TAB_MODE.LENSES,
                        itemProfileItem: CONFIG_PROFILE.LENSES,
                    },
                    {
                        itemKey: HUMMINGBIRD_NAVIGATION_TABS_DEFAULT_TAB_MODE.DATABASES,
                        itemProfileItem: CONFIG_PROFILE.DATABASES,
                    },
                    {
                        itemKey: HUMMINGBIRD_NAVIGATION_TABS_DEFAULT_TAB_MODE.LAST_USED,
                        itemProfileItem: CONFIG_PROFILE.LAST_USED,
                    },
                ]}
            />
        </UICardBody>
    </StyledCard>);
};
