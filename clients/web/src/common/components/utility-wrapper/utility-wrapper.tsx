/**
 * <AUTHOR>
 * @namespace UtilityWrapper
 * @description Utility Wrapper
 */

import { StyledErrorBoundary } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

export type UtilityWrapperProps = {

    readonly children: React.ReactNode;
};

export const UtilityWrapper: FC<UtilityWrapperProps> = (
    props: UtilityWrapperProps,
) => {

    return (<StyledErrorBoundary>
        {props.children}
    </StyledErrorBoundary>);
};
