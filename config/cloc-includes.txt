# Root
.github
.vscode
docs
pnpm-workspace.yaml
scripts

# !Clients

# Desktop
clients/desktop/src
clients/desktop/test
clients/desktop/typescript
clients/desktop/babel.config.js
clients/desktop/eslint.config.mjs
clients/desktop/jest.config.ts
clients/desktop/package.json

# Mobile
clients/mobile/src
clients/mobile/test
clients/mobile/typescript
clients/mobile/babel.config.js
clients/mobile/eslint.config.mjs
clients/mobile/jest.config.ts
clients/mobile/package.json

# Web
clients/web/src
clients/web/stories
clients/web/test
clients/web/config
clients/web/public
clients/web/script
clients/web/plugin
clients/web/typescript
clients/web/babel.config.js
clients/web/eslint.config.mjs
clients/web/index.html
clients/web/jest.config.ts
clients/web/package.json
clients/web/postcss.config.mjs
clients/web/tailwind.config.mjs
clients/web/tsconfig.json
clients/web/vite.config.mjs

# !Packages

# Bridge Core
packages/bridge-core/src
packages/bridge-core/test
packages/bridge-core/typescript
packages/bridge-core/babel.config.js
packages/bridge-core/eslint.config.mjs
packages/bridge-core/jest.config.ts
packages/bridge-core/package.json

# Clipboard Util
packages/clipboard-util/src
packages/clipboard-util/test
packages/clipboard-util/typescript
packages/clipboard-util/babel.config.js
packages/clipboard-util/eslint.config.mjs
packages/clipboard-util/jest.config.ts
packages/clipboard-util/package.json

# Config
config/cloc-includes.txt
packages/config

# Configuration
packages/configuration/src
packages/configuration/test
packages/configuration/typescript
packages/configuration/babel.config.js
packages/configuration/eslint.config.mjs
packages/configuration/jest.config.ts
packages/configuration/package.json
packages/configuration/tsconfig.json

# Debug
packages/debug/src
packages/debug/test
packages/debug/typescript
packages/debug/babel.config.js
packages/debug/eslint.config.mjs
packages/debug/jest.config.ts
packages/debug/package.json
packages/debug/tsconfig.json

# Debug Environment
packages/debug-environment/src
packages/debug-environment/test
packages/debug-environment/typescript
packages/debug-environment/babel.config.js
packages/debug-environment/eslint.config.mjs
packages/debug-environment/jest.config.ts
packages/debug-environment/package.json
packages/debug-environment/tsconfig.json

# Doc Cast
packages/doc-cast/src
packages/doc-cast/test
packages/doc-cast/typescript
packages/doc-cast/babel.config.js
packages/doc-cast/eslint.config.mjs
packages/doc-cast/jest.config.ts
packages/doc-cast/package.json

# Editor Text Core
packages/editor-text-core/src
packages/editor-text-core/test
packages/editor-text-core/typescript
packages/editor-text-core/babel.config.js
packages/editor-text-core/eslint.config.mjs
packages/editor-text-core/jest.config.ts
packages/editor-text-core/package.json
packages/editor-text-core/tsconfig.json

# Editor Text Web Worker
packages/editor-text-web-worker/src
packages/editor-text-web-worker/test
packages/editor-text-web-worker/typescript
packages/editor-text-web-worker/babel.config.js
packages/editor-text-web-worker/eslint.config.mjs
packages/editor-text-web-worker/jest.config.ts
packages/editor-text-web-worker/package.json

# Flow Common
packages/flow-common/src
packages/flow-common/test
packages/flow-common/typescript
packages/flow-common/babel.config.js
packages/flow-common/eslint.config.mjs
packages/flow-common/jest.config.ts
packages/flow-common/package.json

# Global Constant
packages/global-constant/src
packages/global-constant/test
packages/global-constant/typescript
packages/global-constant/babel.config.js
packages/global-constant/eslint.config.mjs
packages/global-constant/jest.config.ts
packages/global-constant/package.json

# Global Symbol
packages/global-symbol/src
packages/global-symbol/test
packages/global-symbol/typescript
packages/global-symbol/babel.config.js
packages/global-symbol/eslint.config.mjs
packages/global-symbol/jest.config.ts
packages/global-symbol/package.json

# Global Util
packages/global-util/src
packages/global-util/test
packages/global-util/typescript
packages/global-util/babel.config.js
packages/global-util/eslint.config.mjs
packages/global-util/jest.config.ts
packages/global-util/package.json

# Interceptor Advanced
packages/interceptor-advanced/src
packages/interceptor-advanced/test
packages/interceptor-advanced/typescript
packages/interceptor-advanced/babel.config.js
packages/interceptor-advanced/eslint.config.mjs
packages/interceptor-advanced/jest.config.ts
packages/interceptor-advanced/package.json

# Interceptor Core
packages/interceptor-core/src
packages/interceptor-core/test
packages/interceptor-core/typescript
packages/interceptor-core/babel.config.js
packages/interceptor-core/eslint.config.mjs
packages/interceptor-core/jest.config.ts
packages/interceptor-core/package.json

# Internationalization
packages/internationalization/src
packages/internationalization/test
packages/internationalization/typescript
packages/internationalization/babel.config.js
packages/internationalization/eslint.config.mjs
packages/internationalization/jest.config.ts
packages/internationalization/package.json
packages/internationalization/tsconfig.json

# Logger
packages/logger/src
packages/logger/test
packages/logger/typescript
packages/logger/babel.config.js
packages/logger/eslint.config.mjs
packages/logger/jest.config.ts
packages/logger/package.json

# Mock Origin
packages/mock-origin/src
packages/mock-origin/test
packages/mock-origin/typescript
packages/mock-origin/babel.config.js
packages/mock-origin/eslint.config.mjs
packages/mock-origin/jest.config.ts
packages/mock-origin/package.json

# Monaco Bridge
packages/monaco-bridge/src
packages/monaco-bridge/test
packages/monaco-bridge/typescript
packages/monaco-bridge/babel.config.js
packages/monaco-bridge/eslint.config.mjs
packages/monaco-bridge/jest.config.ts
packages/monaco-bridge/package.json
packages/monaco-bridge/tsconfig.json

# Monaco Core
packages/monaco-core/src
packages/monaco-core/test
packages/monaco-core/typescript
packages/monaco-core/babel.config.js
packages/monaco-core/eslint.config.mjs
packages/monaco-core/jest.config.ts
packages/monaco-core/package.json
packages/monaco-core/tsconfig.json

# Monaco Log
packages/monaco-log/src
packages/monaco-log/test
packages/monaco-log/typescript
packages/monaco-log/babel.config.js
packages/monaco-log/eslint.config.mjs
packages/monaco-log/jest.config.ts
packages/monaco-log/package.json
packages/monaco-log/tsconfig.json

# Monaco ImbriScript
packages/monaco-imbriscript/src
packages/monaco-imbriscript/test
packages/monaco-imbriscript/typescript
packages/monaco-imbriscript/babel.config.js
packages/monaco-imbriscript/eslint.config.mjs
packages/monaco-imbriscript/jest.config.ts
packages/monaco-imbriscript/package.json
packages/monaco-imbriscript/tsconfig.json

# Monaco Markdown
packages/monaco-markdown/src
packages/monaco-markdown/test
packages/monaco-markdown/typescript
packages/monaco-markdown/babel.config.js
packages/monaco-markdown/eslint.config.mjs
packages/monaco-markdown/jest.config.ts
packages/monaco-markdown/package.json
packages/monaco-markdown/tsconfig.json

# Monaco React
packages/monaco-react/src
packages/monaco-react/test
packages/monaco-react/typescript
packages/monaco-react/babel.config.js
packages/monaco-react/eslint.config.mjs
packages/monaco-react/jest.config.ts
packages/monaco-react/package.json
packages/monaco-react/tsconfig.json

# Monaco Types
packages/monaco-types/src
packages/monaco-types/test
packages/monaco-types/typescript
packages/monaco-types/babel.config.js
packages/monaco-types/eslint.config.mjs
packages/monaco-types/jest.config.ts
packages/monaco-types/package.json
packages/monaco-types/tsconfig.json

# Navigation Core
packages/navigation-core/src
packages/navigation-core/test
packages/navigation-core/typescript
packages/navigation-core/babel.config.js
packages/navigation-core/eslint.config.mjs
packages/navigation-core/jest.config.ts
packages/navigation-core/package.json
packages/navigation-core/tsconfig.json

# Origin Central
packages/origin-central/src
packages/origin-central/test
packages/origin-central/typescript
packages/origin-central/babel.config.js
packages/origin-central/eslint.config.mjs
packages/origin-central/jest.config.ts
packages/origin-central/package.json

# Origin Central Web Worker
packages/origin-central-web-worker/src
packages/origin-central-web-worker/test
packages/origin-central-web-worker/typescript
packages/origin-central-web-worker/babel.config.js
packages/origin-central-web-worker/eslint.config.mjs
packages/origin-central-web-worker/jest.config.ts
packages/origin-central-web-worker/package.json

# Platformless
packages/platformless/src
packages/platformless/test
packages/platformless/typescript
packages/platformless/babel.config.js
packages/platformless/eslint.config.mjs
packages/platformless/jest.config.ts
packages/platformless/package.json

# React Common
packages/react-common/src
packages/react-common/test
packages/react-common/typescript
packages/react-common/babel.config.js
packages/react-common/eslint.config.mjs
packages/react-common/jest.config.ts
packages/react-common/package.json
packages/react-common/tsconfig.json

# React Components
packages/react-components/src
packages/react-components/test
packages/react-components/typescript
packages/react-components/babel.config.js
packages/react-components/eslint.config.mjs
packages/react-components/jest.config.ts
packages/react-components/package.json
packages/react-components/tsconfig.json

# React Core
packages/react-core/src
packages/react-core/test
packages/react-core/typescript
packages/react-core/babel.config.js
packages/react-core/eslint.config.mjs
packages/react-core/jest.config.ts
packages/react-core/package.json
packages/react-core/tsconfig.json

# React Cross Section Core
packages/react-cross-section-core/src
packages/react-cross-section-core/test
packages/react-cross-section-core/typescript
packages/react-cross-section-core/babel.config.js
packages/react-cross-section-core/eslint.config.mjs
packages/react-cross-section-core/jest.config.ts
packages/react-cross-section-core/package.json
packages/react-cross-section-core/tsconfig.json

# React Markdown
packages/react-markdown/src
packages/react-markdown/stories
packages/react-markdown/test
packages/react-markdown/typescript
packages/react-markdown/babel.config.js
packages/react-markdown/eslint.config.mjs
packages/react-markdown/jest.config.ts
packages/react-markdown/package.json
packages/react-markdown/tsconfig.json

# React Navigation
packages/react-navigation/src
packages/react-navigation/test
packages/react-navigation/typescript
packages/react-navigation/babel.config.js
packages/react-navigation/eslint.config.mjs
packages/react-navigation/jest.config.ts
packages/react-navigation/package.json
packages/react-navigation/tsconfig.json

# React Origin Central
packages/react-origin-central/src
packages/react-origin-central/test
packages/react-origin-central/typescript
packages/react-origin-central/babel.config.js
packages/react-origin-central/eslint.config.mjs
packages/react-origin-central/jest.config.ts
packages/react-origin-central/package.json
packages/react-origin-central/tsconfig.json

# React stories
packages/react-store/src
packages/react-store/test
packages/react-store/typescript
packages/react-store/babel.config.js
packages/react-store/eslint.config.mjs
packages/react-store/jest.config.ts
packages/react-store/package.json
packages/react-store/tsconfig.json

# React Views
packages/react-views/src
packages/react-views/test
packages/react-views/typescript
packages/react-views/babel.config.js
packages/react-views/eslint.config.mjs
packages/react-views/jest.config.ts
packages/react-views/package.json
packages/react-views/tsconfig.json

# Script Central Web Worker
packages/script-central-web-worker/src
packages/script-central-web-worker/test
packages/script-central-web-worker/typescript
packages/script-central-web-worker/babel.config.js
packages/script-central-web-worker/eslint.config.mjs
packages/script-central-web-worker/jest.config.ts
packages/script-central-web-worker/package.json

# Script Core
packages/script-core/src
packages/script-core/test
packages/script-core/typescript
packages/script-core/babel.config.js
packages/script-core/eslint.config.mjs
packages/script-core/jest.config.ts
packages/script-core/package.json

# Script Declaration
packages/script-declaration/src
packages/script-declaration/test
packages/script-declaration/typescript
packages/script-declaration/babel.config.js
packages/script-declaration/eslint.config.mjs
packages/script-declaration/jest.config.ts
packages/script-declaration/package.json

# Script Manager
packages/script-manager/src
packages/script-manager/test
packages/script-manager/typescript
packages/script-manager/babel.config.js
packages/script-manager/eslint.config.mjs
packages/script-manager/jest.config.ts
packages/script-manager/package.json

# Storybook Common
packages/storybook-common/src
packages/storybook-common/test
packages/storybook-common/typescript
packages/storybook-common/babel.config.js
packages/storybook-common/eslint.config.mjs
packages/storybook-common/jest.config.ts
packages/storybook-common/package.json
packages/storybook-common/tsconfig.json

# Theme Core
packages/theme-core/src
packages/theme-core/test
packages/theme-core/typescript
packages/theme-core/babel.config.js
packages/theme-core/eslint.config.mjs
packages/theme-core/jest.config.ts
packages/theme-core/package.json
packages/theme-core/tsconfig.json

# Transfer Core
packages/transfer-core/src
packages/transfer-core/test
packages/transfer-core/typescript
packages/transfer-core/babel.config.js
packages/transfer-core/eslint.config.mjs
packages/transfer-core/jest.config.ts
packages/transfer-core/package.json

# UI
packages/ui/src
packages/ui/test
packages/ui/typescript
packages/ui/babel.config.js
packages/ui/eslint.config.mjs
packages/ui/jest.config.ts
packages/ui/package.json
packages/ui/tsconfig.json

# Web Storybook
packages/web-storybook/.storybook
packages/web-storybook/package.json

# Web Worker Core
packages/web-worker-core/src
packages/web-worker-core/test
packages/web-worker-core/typescript
packages/web-worker-core/babel.config.js
packages/web-worker-core/eslint.config.mjs
packages/web-worker-core/jest.config.ts
packages/web-worker-core/package.json
