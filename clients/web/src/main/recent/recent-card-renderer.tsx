/**
 * <AUTHOR>
 * @namespace Recent
 * @description Recent View
 */

import { S_UseDatabasesLoading, useDatabases } from "@/main/database/hooks/use-databases";
import { RECENT_TYPE, RecentItem } from "@imbricate-hummingbird/configuration";
import { LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import { RecentCard } from "./components/recent-card";
import { useRecent } from "@imbricate-hummingbird/configuration";

export type RecentCardRendererProps = {
};

export const RecentCardRenderer: FC<RecentCardRendererProps> = (
    _props: RecentCardRendererProps,
) => {

    const recentDefinition = useRecent();
    const databases = useDatabases();

    if (recentDefinition === null
        || databases === S_UseDatabasesLoading
    ) {

        return (<LoadingWrapper
            debugDescription="Recent Card Renderer"
            fullHeight
        />);
    }

    return (<React.Fragment>
        {recentDefinition.items.map((
            each: RecentItem<RECENT_TYPE>,
        ) => {

            return (<RecentCard
                key={each.hash}
                item={each}
            />);
        })}
    </React.Fragment>);
};
