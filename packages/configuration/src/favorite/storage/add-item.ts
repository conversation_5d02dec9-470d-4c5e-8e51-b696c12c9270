/**
 * <AUTHOR>
 * @namespace Favorite
 * @description Add Item
 */

import { ManagedConfigController } from "../../managed-config-controller";
import { HUMMINGBIRD_MANAGED_CONFIG_ITEM } from "../../types/managed-config/enum";
import { FAVORITE_TYPE, FavoriteDefinition, FavoriteItem, FavoritePayloadSwitch } from "../types/favorite-definition";
import { hashFavoriteItem } from "../types/favorite-hash";

export const S_AddFavoriteItem_AlreadyExists: unique symbol = Symbol("S_AddFavoriteItem_AlreadyExists");

export type AddFavoriteItemResult =
    | {
        success: true;
    }
    | typeof S_AddFavoriteItem_AlreadyExists;

export const addFavoriteItem = <T extends FAVORITE_TYPE>(
    type: T,
    payload: FavoritePayloadSwitch<T>,
): AddFavoriteItemResult => {

    const favorite: FavoriteDefinition = ManagedConfigController.getInstance()
        .getManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.FAVORITE);

    const hash: string = hashFavoriteItem(type, payload);

    const existingItem: FavoriteItem<FAVORITE_TYPE> | undefined = favorite.items.find((
        item: FavoriteItem<FAVORITE_TYPE>,
    ) => {
        return item.hash === hash;
    });

    if (existingItem) {
        return S_AddFavoriteItem_AlreadyExists;
    }

    const newItem: FavoriteItem<T> = {
        type,
        payload,
        hash,
    };

    const newItems: FavoriteItem<FAVORITE_TYPE>[] = [
        newItem,
        ...favorite.items,
    ];

    ManagedConfigController.getInstance()
        .setManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.FAVORITE, {
            items: newItems,
        });

    return {
        success: true,
    };
};
