/**
 * <AUTHOR>
 * @namespace Origin_Components_OriginNewTypes
 * @description Mock Origin
 */

import { OriginInstanceMockOrigin } from "@imbricate-hummingbird/transfer-core";
import { UIInput } from "@imbricate-hummingbird/ui";
import { FC } from "react";

export type NewOriginMockOriginProps = {

    readonly originInstance: OriginInstanceMockOrigin;
    readonly onOriginChange: (originInstance: OriginInstanceMockOrigin) => void;
};

export const NewOriginMockOrigin: FC<NewOriginMockOriginProps> = (
    props: NewOriginMockOriginProps,
) => {

    return (<div
        className="flex flex-col gap-2"
    >
        <UIInput
            label="Origin Name"
            value={props.originInstance.originName}
            onValueChange={(
                newValue: string,
            ) => {
                props.onOriginChange({
                    ...props.originInstance,
                    originName: newValue,
                });
            }}
        />
    </div>);
};
