/**
 * <AUTHOR>
 * @namespace Debug_ResultManager
 * @description Result Manager Quick Access
 */

import { StyledCard } from "@imbricate-hummingbird/react-components";
import { ImbriScriptResult, ScriptResultManager } from "@imbricate-hummingbird/script-core";
import { UIButton, UICardBody, UIDivider, UIPopover, createUIButton } from "@imbricate-hummingbird/ui";
import React, { useEffect, useState } from "react";
import { FaDoorOpen } from "react-icons/fa";

export const ResultManagerQuickAccess: React.FC = () => {

    const [blockedResults, setBlockedResults] =
        useState<ImbriScriptResult[]>([]);
    const [inlineResults, setInlineResults] =
        useState<ImbriScriptResult[]>([]);

    useEffect(() => {

        const onChange = () => {

            const blockedResults = ScriptResultManager.getInstance()
                .getBlockedScriptResults();
            const inlineResults = ScriptResultManager.getInstance()
                .getInlineScriptResults();

            setBlockedResults(blockedResults);
            setInlineResults(inlineResults);
        };

        ScriptResultManager.getInstance().addListener(onChange);
        onChange();

        return () => {
            ScriptResultManager.getInstance().removeListener(onChange);
        };
    }, []);

    return (<StyledCard>
        <UICardBody
            className="flex flex-col gap-1 p-1"
        >
            <UIButton
                size="sm"
                radius="sm"
                color="secondary"
                variant="flat"
                startContent={<FaDoorOpen />}
                onPress={() => {
                    ScriptResultManager.getInstance()
                        .setModelOpenState(true);
                }}
            >
                Open Model
            </UIButton>
        </UICardBody>
        <UIDivider />
        <UICardBody
            className="p-1"
        >
            <div
                className="flex flex-col gap-2 font-mono"
            >
                {blockedResults.length > 0 ? blockedResults.map((result) => (
                    <div
                        key={result.identifier}
                    >
                        <div
                            className="text-tiny text-foreground-500"
                        >
                            {result.executionName}
                        </div>
                        <div
                            className="text-tiny text-foreground-500"
                        >
                            {result.identifier}
                        </div>
                        <div
                            className="text-tiny text-foreground-500"
                        >
                            <UIPopover
                                placement="left"
                                trigger={createUIButton({
                                    isFullWidth: true,
                                    color: "primary",
                                    variant: "ghost",
                                    size: "sm",
                                    radius: "sm",
                                    children: result.meta.creator,
                                })}
                            >
                                {result.meta.executer}
                            </UIPopover>
                        </div>
                        <div
                            className="text-tiny font-bold"
                        >
                            {result.status} - {result.mode}
                        </div>
                        {result.isGathering() && <div
                            className="text-tiny font-bold text-warning-500"
                        >
                            Gathering
                        </div>}
                    </div>
                )) : (
                    <div
                        className="text-tiny text-foreground-500"
                    >
                        Currently No Blocked Script Results
                    </div>
                )}
            </div>
        </UICardBody>
        <UIDivider />
        <UICardBody
            className="p-1"
        >
            <div
                className="flex flex-col gap-2 font-mono"
            >
                {inlineResults.length > 0 ? inlineResults.map((result) => (
                    <div
                        key={result.identifier}
                    >
                        <div
                            className="text-tiny text-foreground-500"
                        >
                            {result.executionName}
                        </div>
                        <div
                            className="text-tiny text-foreground-500"
                        >
                            {result.identifier}
                        </div>
                        <div
                            className="text-tiny text-foreground-500"
                        >
                            <UIPopover
                                placement="left"
                                trigger={createUIButton({
                                    isFullWidth: true,
                                    color: "primary",
                                    variant: "ghost",
                                    size: "sm",
                                    radius: "sm",
                                    children: result.meta.creator,
                                })}
                            >
                                {result.meta.executer}
                            </UIPopover>
                        </div>
                        <div
                            className="text-tiny font-bold"
                        >
                            {result.status} - {result.mode}
                        </div>
                        {result.isGathering() && <div
                            className="text-tiny font-bold text-warning-500"
                        >
                            Gathering
                        </div>}
                    </div>
                )) : (
                    <div
                        className="text-tiny text-foreground-500"
                    >
                        Currently No Inline Script Results
                    </div>
                )}
            </div>
        </UICardBody>
    </StyledCard>);
};
