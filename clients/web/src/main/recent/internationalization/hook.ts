/**
 * <AUTHOR>
 * @namespace Recent_Internationalization
 * @description Hook
 */

import { defaultEmptyFormat, useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { useEffect, useState } from "react";
import { recentInternationalization } from "./intl";
import { RECENT_PROFILE } from "./profile";

export const useRecentFormat = (): SudooFormat<RECENT_PROFILE> => {

    const locale = useLocale();

    const [format, setFormat] = useState<SudooFormat<RECENT_PROFILE>>(defaultEmptyFormat);

    useEffect(() => {

        const execute = async () => {

            const format = await recentInternationalization.format(locale);
            setFormat(format);
        };

        execute();
    }, [locale]);

    return format;
};
