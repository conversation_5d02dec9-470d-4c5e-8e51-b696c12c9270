/**
 * <AUTHOR>
 * @namespace Database_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooLazyInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { DATABASE_PROFILE } from "./profile";

export const databaseInternationalization: SudooLazyInternationalization<DATABASE_PROFILE> =
    SudooLazyInternationalization.create<DATABASE_PROFILE>(
        DEFAULT_LOCALE,
    );

databaseInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/en-US").then(
        (module) => module.enUSDatabaseProfile,
    ),
);

databaseInternationalization.mergeLazyProfile(
    IETF_LOCALE.JAPANESE_JAPAN,
    import("./locale/ja-JP").then(
        (module) => module.jaJPDatabaseProfile,
    ),
);

databaseInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    import("./locale/zh-CN").then(
        (module) => module.zhCNDatabaseProfile,
    ),
);
