/**
 * <AUTHOR>
 * @namespace Document_Actions
 * @description Delete Document
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createDeleteDocumentAction = (
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Delete Document",
        actionDescription: `Delete the document from [${databaseUniqueIdentifier}] with identifier [${documentUniqueIdentifier}]`,
        executerMetadata: metadata,
        actionPayload: {
            databaseUniqueIdentifier,
            documentUniqueIdentifier,
        },
    };
};
