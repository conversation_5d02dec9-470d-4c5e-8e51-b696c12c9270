/**
 * <AUTHOR>
 * @namespace Debug_Components_RecentActionTracker
 * @description Recent Action Tracker
 */

import { RECENT_ACTION_STATUS, RecentActionItem } from "@imbricate-hummingbird/interceptor-core";
import { UIBadge, UIButton, UIDivider, UIModal, UIModalBody, UIModalFooter, UIModalHeader, UIRadioGroup, UISwitch, useUIDisclosure } from "@imbricate-hummingbird/ui";
import { WORKER_WEB_WORKER } from "@imbricate-hummingbird/web-worker-core";
import React, { FC, useMemo, useState } from "react";
import { FaBug } from "react-icons/fa";
import { useRecentActions } from "../../../data/hooks/use-recent-actions";
import { DisplayRecentAction } from "../../types/display-recent-action";
import { concludeRecentActions } from "../../util/conclude-recent-actions";
import { RecentActionDetails } from "./recent-action-details";

export enum TRACKER_OPTIONS {

    NO_DUPLICATED = "NO_DUPLICATED",
    ALL = "ALL",
    PENDING_AND_FAILED = "PENDING_AND_FAILED",
    FAILED = "FAILED",
}

// LAZY LOAD ONLY
const RecentActionTracker: FC = () => {

    const [showExtraInformation, setShowExtraInformation] = useState<boolean>(false);

    const { isOpen, onOpen, onOpenChange } = useUIDisclosure();
    const recentActions = useRecentActions(true);

    const [selectedLevel, setSelectedLevel] = useState<TRACKER_OPTIONS>(
        TRACKER_OPTIONS.NO_DUPLICATED,
    );

    const badgeNumber = recentActions.actions.length;

    const hasErrored = useMemo(() => {
        return recentActions.actions.some((
            action: RecentActionItem<WORKER_WEB_WORKER>,
        ) => {
            return action.status === RECENT_ACTION_STATUS.FAILED;
        });
    }, [recentActions.actions]);

    const isPending = useMemo(() => {
        return recentActions.actions.some((
            action: RecentActionItem<WORKER_WEB_WORKER>,
        ) => {
            return action.status === RECENT_ACTION_STATUS.PENDING;
        });
    }, [recentActions.actions]);

    const displayRecentActions = concludeRecentActions(recentActions.actions);

    const filteredDisplayRecentActions = useMemo(() => {
        return displayRecentActions.filter((
            displayRecentAction: DisplayRecentAction,
        ) => {

            switch (selectedLevel) {

                case TRACKER_OPTIONS.NO_DUPLICATED:
                    return !displayRecentAction.recentAction.isCached && !displayRecentAction.recentAction.isMerged;
                case TRACKER_OPTIONS.ALL:
                    return true;
                case TRACKER_OPTIONS.PENDING_AND_FAILED:
                    return displayRecentAction.recentAction.status === RECENT_ACTION_STATUS.PENDING || displayRecentAction.recentAction.status === RECENT_ACTION_STATUS.FAILED;
                case TRACKER_OPTIONS.FAILED:
                    return displayRecentAction.recentAction.status === RECENT_ACTION_STATUS.FAILED;
            }
        });
    }, [recentActions.actions, selectedLevel]);

    return (<React.Fragment>
        <UIBadge
            placement="top-left"
            shape="rectangle"
            color={hasErrored ? "danger"
                : isPending ? "warning" : "success"}
            content={badgeNumber > 0 ? badgeNumber : ""}
            size="lg"
        >
            <UIButton
                size="lg"
                isIconOnly
                variant="flat"
                color="warning"
                onPress={onOpen}
            >
                <FaBug
                    className="text-xl"
                />
            </UIButton>
        </UIBadge>
        <UIModal
            isOpen={isOpen}
            onOpenChange={onOpenChange}
            size="3xl"
            isHideCloseButton
            isDismissable={false}
            isKeyboardDismissDisabled={true}
        >
            {(onClose: () => void) => {
                return (<React.Fragment>
                    <UIModalHeader
                        className="items-center gap-5"
                    >
                        <FaBug
                            className="text-2xl"
                        />
                        <div
                            className="w-full"
                        >
                            <div
                                className="font-mono text-2xl"
                            >
                                Actions
                            </div>
                            <div
                                className="font-normal"
                            >
                                <UIRadioGroup
                                    selected={selectedLevel}
                                    onSelectedChange={(newSelected: string) => {

                                        setSelectedLevel(newSelected as TRACKER_OPTIONS);
                                    }}
                                    orientation="horizontal"
                                    items={[
                                        {
                                            itemKey: TRACKER_OPTIONS.NO_DUPLICATED,
                                            itemContent: "No Duplicated",
                                        },
                                        {
                                            itemKey: TRACKER_OPTIONS.ALL,
                                            itemContent: "All Actions",
                                        },
                                        {
                                            itemKey: TRACKER_OPTIONS.PENDING_AND_FAILED,
                                            itemContent: "Pending and Failed",
                                        },
                                        {
                                            itemKey: TRACKER_OPTIONS.FAILED,
                                            itemContent: "Failed",
                                        },
                                    ]}
                                />
                            </div>
                            <div
                                className="flex items-center gap-2 justify-end w-full"
                            >
                                <div
                                    className="font-normal text-sm"
                                >
                                    Show Extra Information
                                </div>
                                <UISwitch
                                    aria-label="Show Extra Information"
                                    size="sm"
                                    isSelected={showExtraInformation}
                                    onSelectedChange={setShowExtraInformation}
                                />
                            </div>
                        </div>
                    </UIModalHeader>
                    <UIDivider />
                    <UIModalBody>
                        <RecentActionDetails
                            showExtraInformation={showExtraInformation}
                            displayRecentActions={filteredDisplayRecentActions}
                        />
                    </UIModalBody>
                    <UIDivider />
                    <UIModalFooter>
                        <UIButton
                            color="danger"
                            variant="light"
                            onPress={onClose}
                        >
                            Close
                        </UIButton>
                    </UIModalFooter>
                </React.Fragment>);
            }}
        </UIModal>
    </React.Fragment>);
};
export default RecentActionTracker;
