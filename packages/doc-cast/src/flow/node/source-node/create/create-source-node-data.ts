/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Flow_Node
 * @description Create Source Node Data
 */

import { DOC_CAST_SOURCE_TYPE, DocCastSource } from "../../../../types/source";
import { DocCastSourceNodeData } from "../source-node-data";

export const createDocCastSourceNodeData = async (
    source: DocCastSource<DOC_CAST_SOURCE_TYPE>,
): Promise<DocCastSourceNodeData> => {

    // DOC_CAST_SOURCE_TYPE SWITCH
    switch (source.type) {

        case DOC_CAST_SOURCE_TYPE.DOCUMENT_STACK_IDENTIFIER: {

            return {
                type: source.type,
                payload: source.payload,
            };
        }
    }

    return {};
};
