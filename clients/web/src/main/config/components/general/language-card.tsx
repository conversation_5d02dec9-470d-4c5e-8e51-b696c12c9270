/**
 * <AUTHOR>
 * @namespace Config_Components_General
 * @description Language Card
 */

import { useInternationalizationFormat } from "@/internationalization/internationalization/hooks/internationalization";
import { useLanguageFormat } from "@/internationalization/internationalization/hooks/language";
import { getLocaleFlag } from "@/internationalization/util/get-locale-flag";
import { HUMMINGBIRD_PREFERENCE_ITEM, PreferenceController } from "@imbricate-hummingbird/configuration";
import { LocaleTrigger, useLocale } from "@imbricate-hummingbird/internationalization";
import { StyledCard, openSuccessToaster } from "@imbricate-hummingbird/react-components";
import { UICardBody, UICardHeader, UIDivider, UIDropdown, UISpacer, createUIButton } from "@imbricate-hummingbird/ui";
import { IETF_LOCALE } from "@sudoo/locale";
import React, { FC } from "react";
import { FaGlobe } from "react-icons/fa6";
import { useConfigFormat } from "../../internationalization/hook";
import { CONFIG_PROFILE } from "../../internationalization/profile";

export type ConfigLanguageCardProps = {
};

export const ConfigLanguageCard: FC<ConfigLanguageCardProps> = (
    _props: ConfigLanguageCardProps,
) => {

    const locale = useLocale();

    const configFormat = useConfigFormat();
    const languageFormat = useLanguageFormat();
    const internationalizationFormat = useInternationalizationFormat();

    const [selectedKeys, setSelectedKeys] = React.useState<IETF_LOCALE>(locale);

    return (<StyledCard>
        <UICardHeader>
            <FaGlobe />
            <UISpacer />
            {configFormat.get(CONFIG_PROFILE.APPLICATION_LANGUAGE)}
        </UICardHeader>
        <UIDivider />
        <UICardBody>
            <UIDropdown
                variant="faded"
                trigger={createUIButton({
                    startContent: (<span className="text-xl">
                        {getLocaleFlag(selectedKeys)}
                    </span>),
                    variant: "flat",
                    color: "primary",
                    children: languageFormat.get(selectedKeys),
                })}
                selectionMode="single"
                categories={[
                    {
                        categoryKey: "language",
                        items: [
                            IETF_LOCALE.CHINESE_SIMPLIFIED,
                            IETF_LOCALE.CHINESE_TRADITIONAL,
                            IETF_LOCALE.ENGLISH_CANADA,
                            IETF_LOCALE.ENGLISH_UNITED_STATES,
                            IETF_LOCALE.ENGLISH_UNITED_KINGDOM,
                            IETF_LOCALE.FRENCH_FRANCE,
                            IETF_LOCALE.JAPANESE_JAPAN,
                            IETF_LOCALE.KOREAN_KOREA,
                            IETF_LOCALE.RUSSIAN_RUSSIA,
                            IETF_LOCALE.SPANISH_MEXICO,
                            IETF_LOCALE.SPANISH_SPAIN,
                        ].map((
                            item: IETF_LOCALE,
                        ) => {

                            return {
                                itemKey: item,
                                textValue: item,
                                isSelected: item === selectedKeys,
                                content: languageFormat.get(item),
                                description: internationalizationFormat.get(item),
                                descriptionClassName: "text-default-500",
                                startContent: (<span className="text-2xl">
                                    {getLocaleFlag(item)}
                                </span>),
                                onPress: () => {
                                    setSelectedKeys(item);

                                    PreferenceController.setPreference(
                                        HUMMINGBIRD_PREFERENCE_ITEM.APPLICATION_LOCALE_LANGUAGE,
                                        item,
                                    );

                                    LocaleTrigger.getInstance().trigger();

                                    openSuccessToaster(
                                        "Language Changed",
                                        {
                                            description: languageFormat.get(item),
                                        },
                                    );
                                },
                            };
                        }),
                    },
                ]}
            />
        </UICardBody>
    </StyledCard>);
};
