/**
 * <AUTHOR>
 * @namespace Debug_Components_DebugActionButton
 * @description Debug Action Button
 */

import { debugLog } from "@imbricate-hummingbird/debug";
import { OriginWorkerDataCentral } from "@imbricate-hummingbird/origin-central-web-worker";
import { executeDatabasesRecursive, executeQueryDocumentsRecursive } from "@imbricate-hummingbird/react-origin-central";
import { UIDropdown, createUIButton } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { MdLocalPlay } from "react-icons/md";

export type DebugActionButtonProps = {
};

export const DebugActionButton: FC<DebugActionButtonProps> = (
    _props: DebugActionButtonProps,
) => {

    return (<UIDropdown
        trigger={createUIButton({
            isIconOnly: true,
            size: "lg",
            variant: "flat",
            color: "warning",
            children: (<MdLocalPlay
                className="text-xl"
            />),
        })}
        categories={[
            {
                categoryKey: "origin-action",
                title: "Origin Action",
                items: [
                    {
                        itemKey: "log-list-of-databases",
                        content: "Log List of Databases",
                        description: "Log the list of databases in console",
                        onPress: async () => {

                            const databases = await executeDatabasesRecursive(
                                OriginWorkerDataCentral.getInstance(),
                                {
                                    actionName: "Log List of Databases",
                                    actionDescription: "Log the list of databases in console",
                                    executerMetadata: {
                                        executer: import.meta.url,
                                    },
                                },
                            );

                            debugLog(databases);
                        },
                    },
                    {
                        itemKey: "log-list-of-documents",
                        content: "Log List of Documents",
                        description: "Log the list of documents in console",
                        onPress: async () => {

                            const databaseUniqueIdentifier = prompt("Database Unique Identifier");

                            if (!databaseUniqueIdentifier) {
                                return;
                            }

                            const documents = await executeQueryDocumentsRecursive(
                                OriginWorkerDataCentral.getInstance(),
                                databaseUniqueIdentifier,
                                {},
                                {
                                    actionName: "Log List of Documents",
                                    actionDescription: "Log the list of documents in console",
                                    executerMetadata: {
                                        executer: import.meta.url,
                                    },
                                },
                            );

                            debugLog(documents);
                        },
                    },
                ],
            },
        ]}
    />);
};
