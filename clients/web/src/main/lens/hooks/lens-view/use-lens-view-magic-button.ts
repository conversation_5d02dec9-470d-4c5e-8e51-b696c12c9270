/**
 * <AUTHOR>
 * @namespace Main_Lens_Hooks_LensView_UseEditEditorsMagicButton
 * @description Use Lens View Magic Button
 */

import { useMagicButtonInitialItems } from "@/common/components/magic-button/hooks/use-initial-items";
import { UseMagicButtonResult, useMagicButton } from "@/common/components/magic-button/hooks/use-magic-button";
import { LENS_CONFIG_SOURCE, LensConfigItem } from "@imbricate-hummingbird/configuration";
import { isDefaultEmptyFormat } from "@imbricate-hummingbird/internationalization";
import { useNavigateLensEditView } from "@imbricate-hummingbird/react-navigation";
import { HiPencil, HiRefresh } from "react-icons/hi";
import { useLensFormat } from "../../internationalization/hook";
import { LENS_PROFILE } from "../../internationalization/profile";

export type UseLensViewMagicButtonProps = {

    readonly targetLens: LensConfigItem<LENS_CONFIG_SOURCE>;
    readonly updateVersion: () => void;
};

export const useLensViewMagicButton = (
    props: UseLensViewMagicButtonProps,
): UseMagicButtonResult => {

    const navigateToEditLens = useNavigateLensEditView();

    const lensFormat = useLensFormat();

    const magicButtonResult = useMagicButton([
        {
            categoryKey: "lens",
            categoryTitle: lensFormat.get(LENS_PROFILE.LENS),
            categoryOrder: 1,
        },
        {
            categoryKey: "imbriscript",
            categoryTitle: lensFormat.get(LENS_PROFILE.IMBRISCRIPT),
            categoryOrder: 2,
        },
    ]);

    useMagicButtonInitialItems(
        magicButtonResult,
        !isDefaultEmptyFormat(lensFormat),
        [
            {
                categoryKey: "lens",
                itemKey: "reload-lens",
                icon: HiRefresh,
                title: lensFormat.get(LENS_PROFILE.RELOAD_LENS),
                description: lensFormat.get(LENS_PROFILE.RELOAD_LENS_DESCRIPTION),
                color: "primary",
                quickAccess: true,
                defaultQuickAccess: true,
                onPress: () => {
                    props.updateVersion();
                },
            },
            {
                categoryKey: "lens",
                itemKey: "edit-lens",
                icon: HiPencil,
                title: lensFormat.get(LENS_PROFILE.EDIT_LENS),
                description: lensFormat.get(LENS_PROFILE.EDIT_LENS_DESCRIPTION),
                onPress: () => {
                    navigateToEditLens(
                        props.targetLens.lensIdentifier,
                        {
                            replace: true,
                        },
                    );
                },
            },
        ],
    );

    return magicButtonResult;
};
