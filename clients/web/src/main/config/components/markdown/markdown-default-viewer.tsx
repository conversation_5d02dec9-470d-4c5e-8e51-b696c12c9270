/**
 * <AUTHOR>
 * @namespace Config_Components
 * @description Markdown Default Viewer
 */

import { HUMMINGBIRD_MARKDOWN_VIEWER_VIEWER, HUMMINGBIRD_PREFERENCE_ITEM, usePreferenceItem } from "@imbricate-hummingbird/configuration";
import { FormattedRadioGroup, FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { FaEye } from "react-icons/fa";
import { useConfigFormat } from "../../internationalization/hook";
import { CONFIG_PROFILE } from "../../internationalization/profile";

export type ConfigMarkdownDefaultViewerCardProps = {
};

export const ConfigMarkdownDefaultViewerCard: FC<ConfigMarkdownDefaultViewerCardProps> = (
    _props: ConfigMarkdownDefaultViewerCardProps,
) => {

    const configFormat = useConfigFormat();

    const markdownViewerDefaultViewer = usePreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.MARKDOWN_VIEWER_DEFAULT_VIEWER,
    );

    return (<StyledCard>
        <UICardHeader>
            <FaEye />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.MARKDOWN_VIEWER)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-2"
        >
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                    className="text-sm text-gray-500"
                >
                    {configFormat.get(CONFIG_PROFILE.MARKDOWN_VIEWER_DEFAULT_VIEWER_DESCRIPTION)}
                </FormattedText>
            </div>
            <FormattedRadioGroup
                label={configFormat.get(CONFIG_PROFILE.MARKDOWN_VIEWER_DEFAULT_VIEWER)}
                selected={markdownViewerDefaultViewer.preference}
                onSelectedChange={(newSelected: string) => {

                    markdownViewerDefaultViewer.updatePreference(
                        newSelected as HUMMINGBIRD_MARKDOWN_VIEWER_VIEWER,
                    );
                }}
                format={configFormat}
                items={[
                    {
                        itemKey: HUMMINGBIRD_MARKDOWN_VIEWER_VIEWER.PRINT_FRIENDLY,
                        itemProfileItem: CONFIG_PROFILE.PRINT_FRIENDLY,
                    },
                    {
                        itemKey: HUMMINGBIRD_MARKDOWN_VIEWER_VIEWER.REVEAL_JS,
                        itemProfileItem: CONFIG_PROFILE.REVEAL_JS,
                    },
                ]}
            />
        </UICardBody>
    </StyledCard>);
};
