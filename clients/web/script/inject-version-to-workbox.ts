/**
 * <AUTHOR>
 * @namespace Script
 * @description Inject Version to Workbox
 */

import * as Path from "node:path";
import * as Fs from "node:fs";

const putVersion = (filePath: string) => {

    const version = process.env.npm_package_version;

    const fileContent = Fs.readFileSync(filePath, "utf-8");

    const newFileContent = fileContent.concat(`\nconst VERSION = "${version}";`);

    Fs.writeFileSync(filePath, newFileContent);
};

(() => {

    putVersion(Path.join(__dirname, "..", "app", "service-worker.js"));
})();
