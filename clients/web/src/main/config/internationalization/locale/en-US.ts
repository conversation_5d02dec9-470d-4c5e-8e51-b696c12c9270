/**
 * <AUTHOR>
 * @namespace Config_Internationalization_Locale
 * @description En-US
 */

import { CONFIG_PROFILE } from "../profile";

export const enUSConfigProfile: Record<CONFIG_PROFILE, string> = {

    [CONFIG_PROFILE.ABOUT]: "About",
    [CONFIG_PROFILE.APPEARANCE]: "Appearance",
    [CONFIG_PROFILE.APPLICATION_ANIMATION]: "Application Animation",
    [CONFIG_PROFILE.APPLICATION_ANIMATION_LEVEL]: "Application Animation Level",
    [CONFIG_PROFILE.APPLICATION_CONFIGURATION]: "Application Configuration",
    [CONFIG_PROFILE.APPLICATION_LANGUAGE]: "Application Language",
    [CONFIG_PROFILE.APPLICATION_THEME]: "Application Theme",
    [CONFIG_PROFILE.BOTTOM_BUTTONS]: "Bottom Buttons",
    [CONFIG_PROFILE.BOTTOM_BUTTONS_DISPLAY_MODE]: "Bottom Buttons Display Mode",
    [CONFIG_PROFILE.BOTTOM_BUTTONS_DISPLAY_MODE_DESCRIPTION]: "Configure the display mode of the bottom buttons.",
    [CONFIG_PROFILE.CANCEL]: "Cancel",
    [CONFIG_PROFILE.CLEAN_RECENT_USAGE]: "Clean Recent Usage",
    [CONFIG_PROFILE.CLEAN_RECENT_USAGE_DESCRIPTION]: "Clean up recent usage, this will not delete your own data, such as data sources or focus.",
    [CONFIG_PROFILE.CLEAN_RECENT_USAGE_SUCCESS]: "Recent usage cleaned up successfully",
    [CONFIG_PROFILE.CONFIG]: "Config",
    [CONFIG_PROFILE.CONFIRM_IN_DRAWER]: "Confirm in Drawer",
    [CONFIG_PROFILE.DARK]: "Dark",
    [CONFIG_PROFILE.DATABASES]: "Databases",
    [CONFIG_PROFILE.DEBUG]: "Debug",
    [CONFIG_PROFILE.DISABLED]: "Disabled",
    [CONFIG_PROFILE.DISMISSED_GUIDANCE]: "Dismissed Guidance",
    [CONFIG_PROFILE.DISMISSED_GUIDANCE_DESCRIPTION]: "View and manual turn on or off for guidance messages.",
    [CONFIG_PROFILE.DISPLAY_RECENT_$1_ITEMS]: "Display Recent {} Items",
    [CONFIG_PROFILE.DRAWER]: "Drawer",
    [CONFIG_PROFILE.EDITOR]: "Editor",
    [CONFIG_PROFILE.EDITOR_FONT_SIZE_DESCRIPTION]: "Configure the font size of the Monaco editor.",
    [CONFIG_PROFILE.ENABLED]: "Enabled",
    [CONFIG_PROFILE.EXPORT_CONFIGURATION]: "Export Configuration",
    [CONFIG_PROFILE.FILL]: "Fill",
    [CONFIG_PROFILE.FIT]: "Fit",
    [CONFIG_PROFILE.FONT_SIZE]: "Font Size",
    [CONFIG_PROFILE.FONT_SIZE_MARKDOWN_$1]: "Markdown Editor Font Size - {}",
    [CONFIG_PROFILE.FULL]: "Full",
    [CONFIG_PROFILE.GENERAL]: "General",
    [CONFIG_PROFILE.GUIDANCE]: "Guidance",
    [CONFIG_PROFILE.ICON]: "Icon",
    [CONFIG_PROFILE.IMBRISCRIPT]: "ImbriScript",
    [CONFIG_PROFILE.IMBRISCRIPT_LOG_VIEWER]: "ImbriScript Log Viewer",
    [CONFIG_PROFILE.IMBRISCRIPT_LOG_VIEWER_DEFAULT_OPTION]: "ImbriScript Log Viewer Default Option",
    [CONFIG_PROFILE.IMBRISCRIPT_LOG_VIEWER_DEFAULT_OPTION_DESCRIPTION]: "Configure the default option for the ImbriScript log viewer. You can always change the option in the ImbriScript log viewer in real-time.",
    [CONFIG_PROFILE.IMBRISCRIPT_LOG_VIEWER_PURE_TEXT]: "Pure Text",
    [CONFIG_PROFILE.IMBRISCRIPT_LOG_VIEWER_MONACO]: "Monaco",
    [CONFIG_PROFILE.IMBRISCRIPT_OUTLOOK]: "ImbriScript Outlook",
    [CONFIG_PROFILE.IMBRISCRIPT_OUTLOOK_DESCRIPTION]: "ImbriScript Outlook is the view generated by ImbriScript.",
    [CONFIG_PROFILE.IMBRISCRIPT_OUTLOOK_ACTION_BUTTON]: "ImbriScript Outlook Action Button",
    [CONFIG_PROFILE.IMBRISCRIPT_OUTLOOK_ACTION_BUTTON_REDIRECT_BUTTON_MODE]: "ImbriScript Outlook Action Button Redirect Button Mode",
    [CONFIG_PROFILE.IMBRISCRIPT_OUTLOOK_ACTION_BUTTON_REDIRECT_BUTTON_MODE_DESCRIPTION]: "Configure the redirect button mode of the ImbriScript Outlook action button.",
    [CONFIG_PROFILE.IMBRISCRIPT_QUICK_EXECUTE]: "ImbriScript Quick Execute",
    [CONFIG_PROFILE.IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE]: "ImbriScript Quick Execute Confirm Mode",
    [CONFIG_PROFILE.IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE_DESCRIPTION]: "Configure the confirm mode of the ImbriScript quick execute.",
    [CONFIG_PROFILE.IMPORT_CONFIGURATION]: "Import Configuration",
    [CONFIG_PROFILE.IMPORT_EXPORT]: "Import & Export",
    [CONFIG_PROFILE.IN_PLACE]: "In Place",
    [CONFIG_PROFILE.LAST_USED]: "Last Used",
    [CONFIG_PROFILE.LAST_USED_ACTION]: "Last Used Action",
    [CONFIG_PROFILE.LAST_USED_OR_DEFAULT_ACTION]: "Last Used or Default Action",
    [CONFIG_PROFILE.LAUNCH_IMBRISCRIPT_OUTLOOK]: "Launch ImbriScript Outlook Configuration",
    [CONFIG_PROFILE.LAUNCH_MONACO_EDITOR_CONFIGURATION]: "Launch Monaco Editor Configuration",
    [CONFIG_PROFILE.LENSES]: "Lenses",
    [CONFIG_PROFILE.LIGHT]: "Light",
    [CONFIG_PROFILE.LINE_NUMBER]: "Line Number",
    [CONFIG_PROFILE.MAGIC_BUTTON]: "Magic Button",
    [CONFIG_PROFILE.MAGIC_BUTTON_DEFAULT_ACTION_MODE]: "Magic Button Default Action Mode",
    [CONFIG_PROFILE.MAGIC_BUTTON_DEFAULT_ACTION_MODE_DESCRIPTION]: "Configure the default action mode of the magic button.",
    [CONFIG_PROFILE.MARKDOWN]: "Markdown",
    [CONFIG_PROFILE.MARKDOWN_VIEWER]: "Markdown Viewer",
    [CONFIG_PROFILE.MARKDOWN_VIEWER_DEFAULT_VIEWER]: "Markdown Viewer Default Viewer",
    [CONFIG_PROFILE.MARKDOWN_VIEWER_DEFAULT_VIEWER_DESCRIPTION]: "Configure the default viewer for the markdown viewer.",
    [CONFIG_PROFILE.MINIMAL]: "Minimal",
    [CONFIG_PROFILE.MINIMAP_SIZE]: "Minimap Size",
    [CONFIG_PROFILE.MINIMAP_SIZE_DESCRIPTION]: "Configure the size of the minimap.",
    [CONFIG_PROFILE.MONACO_EDITOR]: "Monaco Editor",
    [CONFIG_PROFILE.MONACO_EDITOR_DESCRIPTION]: "Monaco editor is the code editor for markdown, ImbriScript and JSON object files in Imbricate.",
    [CONFIG_PROFILE.NAVIGATION]: "Navigation",
    [CONFIG_PROFILE.NAVIGATION_SUB_ACTION_ENABLEMENT]: "Navigation Sub Action Enablement",
    [CONFIG_PROFILE.NAVIGATION_SUB_ACTION_ENABLEMENT_DESCRIPTION]: "Configure the enablement of the navigation sub action.",
    [CONFIG_PROFILE.NAVIGATION_TABS]: "Navigation Tabs",
    [CONFIG_PROFILE.NAVIGATION_TABS_DEFAULT_TAB_MODE]: "Navigation Tabs Default Tab Mode",
    [CONFIG_PROFILE.NAVIGATION_TABS_DEFAULT_TAB_MODE_DESCRIPTION]: "Configure the default tab mode of the navigation tabs.",
    [CONFIG_PROFILE.NAVIGATION_TABS_DISPLAY_MODE]: "Navigation Tabs Display Mode",
    [CONFIG_PROFILE.NAVIGATION_TABS_DISPLAY_MODE_DESCRIPTION]: "Configure the display mode of the navigation tabs.",
    [CONFIG_PROFILE.NEW_TAB]: "New Tab",
    [CONFIG_PROFILE.NO_CONFIRM]: "No Confirm",
    [CONFIG_PROFILE.ORIGINS]: "Origins",
    [CONFIG_PROFILE.PRINT_FRIENDLY]: "Print Friendly",
    [CONFIG_PROFILE.PROPORTIONAL]: "Proportional",
    [CONFIG_PROFILE.RECENT_ITEMS]: "Recent Items",
    [CONFIG_PROFILE.RECENT_ITEMS_MAXIMUM_LENGTH]: "Recent Items Maximum Length",
    [CONFIG_PROFILE.RECENT_ITEMS_MAXIMUM_LENGTH_DESCRIPTION]: "Configure the maximum length of recent items, newly visited items will replace the oldest item if the maximum length is reached.",
    [CONFIG_PROFILE.REDUCED]: "Reduced",
    [CONFIG_PROFILE.RESET_ALERT_TITLE]: "This action cannot be undone",
    [CONFIG_PROFILE.RESET_ALERT_DESCRIPTION]: "Please make sure your data is exported before proceeding.",
    [CONFIG_PROFILE.RESET_ALERT_DESCRIPTION_POINT_1]: "All linked origins and created lenses will be removed.",
    [CONFIG_PROFILE.RESET_ALERT_DESCRIPTION_POINT_2]: "All current configurations will be lost.",
    [CONFIG_PROFILE.RESET_ALERT_DESCRIPTION_POINT_3]: "Data from linked origins will remain unchanged.",
    [CONFIG_PROFILE.RESET_APPLICATION]: "Reset Application",
    [CONFIG_PROFILE.RESET_APPLICATION_CONFIRM]: "Reset Application",
    [CONFIG_PROFILE.RESET_APPLICATION_DESCRIPTION]: "Resetting the application to its initial state will result in the following:",
    [CONFIG_PROFILE.RESET_APPLICATION_MESSAGE]: "Reset the application to its initial state.",
    [CONFIG_PROFILE.RESET_TUTORIAL]: "Reset Tutorial",
    [CONFIG_PROFILE.RESET_TUTORIAL_ACTION]: "Reset Tutorial",
    [CONFIG_PROFILE.RESET_TUTORIAL_DESCRIPTION]: "Reset the tutorial to its initial state, display dismissed message and navigation guidances.",
    [CONFIG_PROFILE.RESET_TUTORIAL_SUCCESS]: "Tutorial reset successfully",
    [CONFIG_PROFILE.RESOURCE]: "Resources",
    [CONFIG_PROFILE.REVEAL_JS]: "Reveal.js",
    [CONFIG_PROFILE.SCROLLBAR]: "Scrollbar",
    [CONFIG_PROFILE.SHOW_LINE_NUMBER_DESCRIPTION]: "Config enablement of line number display for monaco editors",
    [CONFIG_PROFILE.SHOW_LINE_NUMBER_FOR_MARKDOWN]: "Show Line Number for Markdown Editor",
    [CONFIG_PROFILE.SUB_ACTION]: "Sub Action",
    [CONFIG_PROFILE.SYSTEM_THEME]: "System Theme",
    [CONFIG_PROFILE.TEXT]: "Text",
    [CONFIG_PROFILE.THEME]: "Theme",
    [CONFIG_PROFILE.USAGE_HISTORY]: "Usage History",
    [CONFIG_PROFILE.USE_MINIMAP_AS_SCROLLBAR]: "Use Minimap as Scrollbar",
    [CONFIG_PROFILE.USE_MINIMAP_AS_SCROLLBAR_DESCRIPTION]: "Use the minimap as the scrollbar.",
};
