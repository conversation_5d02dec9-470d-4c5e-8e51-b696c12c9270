/**
 * <AUTHOR>
 * @package UI
 * @namespace Modal
 * @description Modal
 */

import { Modal, ModalContent } from "@heroui/modal";
import clsx from "clsx";
import type { FC, ReactNode } from "react";
import { UIModalScrollPlacement, UIModalSize } from "./types";

export type UIModalProps = {

    readonly className?: string;
    readonly closeButtonClassName?: string;

    readonly size?: UIModalSize;

    readonly isHideCloseButton?: boolean;
    readonly isDismissable?: boolean;
    readonly isKeyboardDismissDisabled?: boolean;

    readonly scrollPlacement?: UIModalScrollPlacement;

    readonly isOpen: boolean;
    readonly onOpenChange: (isOpen: boolean) => void;

    readonly children: (onClose: () => void) => ReactNode;
};

export const UIModal: FC<UIModalProps> = (
    props: UIModalProps,
) => {

    return (<Modal
        className={props.className}
        classNames={{
            closeButton: clsx(
                "cursor-pointer",
                props.closeButtonClassName,
            ),
        }}

        size={props.size}

        hideCloseButton={props.isHideCloseButton}
        isDismissable={props.isDismissable}
        isKeyboardDismissDisabled={props.isKeyboardDismissDisabled}

        scrollBehavior={props.scrollPlacement ?? "inside"}

        isOpen={props.isOpen}
        onOpenChange={props.onOpenChange}
    >
        <ModalContent>
            {typeof props.children === "function"
                ? props.children
                : null}
        </ModalContent>
    </Modal>);
};
