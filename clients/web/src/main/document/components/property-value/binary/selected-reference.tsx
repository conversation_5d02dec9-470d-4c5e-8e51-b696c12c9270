/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyValue_Binary
 * @description Selected Reference
 */

import { StyledSnippet } from "@/common/components/snippet/styled-snippet";
import { OldDataCentral } from "@/main/data/old-data-central";
import { createGetStaticUriForDownloadAction } from "@/main/document/actions/get-static-uri-for-download";
import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { openStaticWindowForDownload } from "@imbricate-hummingbird/navigation-core";
import { UIButton, UIRadius, UISize } from "@imbricate-hummingbird/ui";
import { ImbricateStaticManagerGetStaticUriOutcome } from "@imbricate/core";
import { FC } from "react";
import { FaDownload } from "react-icons/fa6";
import { MdDelete } from "react-icons/md";

export type DocumentBinaryValueSelectedReferenceProps = {

    readonly originUniqueIdentifier: string;

    readonly reference: string;
    readonly onDelete?: () => void;

    readonly size?: UISize;
    readonly radius?: UIRadius;
};

export const DocumentBinaryValueSelectedReference: FC<DocumentBinaryValueSelectedReferenceProps> = (
    props: DocumentBinaryValueSelectedReferenceProps,
) => {

    return (<div
        className="flex items-center gap-1 w-full"
    >
        <div
            className="flex-1"
        >
            <div
                className="flex gap-2 items-center"
            >
                <StyledSnippet
                    size="sm"
                    className="w-full"
                >
                    {props.reference}
                </StyledSnippet>
                <UIButton
                    className="hover:cursor-pointer"
                    variant="flat"
                    color="primary"
                    isIconOnly
                    size={props.size}
                    radius={props.radius}
                    onPress={async () => {

                        const staticUri: ImbricateStaticManagerGetStaticUriOutcome =
                            await ActionCentral.getInstance().executeAction(
                                async (
                                    actionIdentifier: string,
                                    recordIdentifier: string,
                                ) => {
                                    return OldDataCentral.getInstance().getStaticUri(
                                        props.originUniqueIdentifier,
                                        props.reference,
                                        actionIdentifier,
                                        recordIdentifier,
                                    );
                                },
                                createGetStaticUriForDownloadAction(
                                    {
                                        executer: import.meta.url,
                                    },
                                    props.reference,
                                ),
                            );

                        openStaticWindowForDownload(
                            props.reference,
                            staticUri.uri,
                        );
                    }}
                >
                    <FaDownload
                        className="text-small"
                    />
                </UIButton>
            </div>
        </div>
        {typeof props.onDelete === "function" && <UIButton
            color="danger"
            variant="flat"
            isIconOnly
            size={props.size}
            radius={props.radius}
            onPress={props.onDelete}
        >
            <MdDelete
                className="text-large"
            />
        </UIButton>}
    </div>);
};
