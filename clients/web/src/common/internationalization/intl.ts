/**
 * <AUTHOR>
 * @namespace Common_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooLazyInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { COMMON_PROFILE } from "./profile";

export const commonInternationalization: SudooLazyInternationalization<COMMON_PROFILE> =
    SudooLazyInternationalization.create<COMMON_PROFILE>(
        DEFAULT_LOCALE,
    );

commonInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/en-US").then(
        (module) => module.enUSCommonProfile,
    ),
);

commonInternationalization.mergeLazyProfile(
    IETF_LOCALE.JAPANESE_JAPAN,
    import("./locale/ja-JP").then(
        (module) => module.jaJPCommonProfile,
    ),
);

commonInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    import("./locale/zh-CN").then(
        (module) => module.zhCNCommonProfile,
    ),
);
