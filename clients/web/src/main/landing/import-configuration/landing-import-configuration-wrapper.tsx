/**
 * <AUTHOR>
 * @namespace Landing_ImportConfiguration
 * @description Landing Import Configuration Wrapper
 */

import { getRouteRootView } from "@imbricate-hummingbird/navigation-core";
import { FullLoadingWrapper, LazyLoadComponent } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import { Navigate } from "react-router-dom";
import { useOldOrigins } from "../../origin/hooks/use-origins";

const LandingImportConfigurationView = LazyLoadComponent(
    () => import("./landing-import-configuration"),
    "Landing Import Configuration View",
);

export const LandingImportConfigurationWrapper: FC = () => {

    const origins = useOldOrigins();

    if (origins.length > 0) {

        return (<Navigate
            to={getRouteRootView()}
            replace={true}
        />);
    }

    return (<React.Suspense
        fallback={<FullLoadingWrapper
            debugDescription="Landing Import Configuration View"
        />}
    >
        <LandingImportConfigurationView />
    </React.Suspense>);
};
