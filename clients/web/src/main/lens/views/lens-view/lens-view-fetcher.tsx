/**
 * <AUTHOR>
 * @namespace Lens_Views_LensView_Fetcher
 * @description Lens View Fetcher
 */

import { LENS_CONFIG_SOURCE, LensConfig, LensConfigItem } from "@imbricate-hummingbird/configuration";
import { isFormatLoading, useAsyncTitleWithAsyncFormat } from "@imbricate-hummingbird/internationalization";
import { useLensConfig } from "@imbricate-hummingbird/react-store";
import { FC } from "react";
import { useParams } from "react-router-dom";
import { useVersion } from "../../../common/hooks/use-version";
import { useLensFormat } from "../../internationalization/hook";
import { LENS_PROFILE } from "../../internationalization/profile";
import { LensView } from "./lens-view";

export type LensViewFetcherProps = {
};

// LAZY LOAD ONLY
const LensViewFetcher: FC<LensViewFetcherProps> = (
    _props: LensViewFetcherProps,
) => {

    const lensFormat = useLensFormat();

    const params = useParams();
    const lensIdentifier: string =
        params["lens-identifier"] as string;

    const [version, updateVersion] = useVersion();

    const lensConfig: LensConfig = useLensConfig();

    const targetLens: LensConfigItem<LENS_CONFIG_SOURCE> | undefined =
        lensConfig.items.find((lens: LensConfigItem<LENS_CONFIG_SOURCE>) => {
            return lens.lensIdentifier === lensIdentifier;
        });

    useAsyncTitleWithAsyncFormat(
        () => Boolean(targetLens),
        lensFormat,
        () => {
            return [
                targetLens!.lensName,
                lensFormat.get(LENS_PROFILE.LENS),
            ];
        },
        [targetLens?.lensIdentifier, isFormatLoading(lensFormat)],
    );

    if (!targetLens) {
        return null;
    }

    return (<LensView
        targetLens={targetLens}
        version={version}
        updateVersion={updateVersion}
    />);
};
export default LensViewFetcher;
