/**
 * <AUTHOR>
 * @namespace Origin_Views_NewOriginView
 * @description New Origin View Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const NewOriginView = LazyLoadComponent(
    () => import("./new-origin-view"),
    "New Origin View",
);

export const NewOriginViewWrapper: FC = () => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="New Origin View"
            fullHeight
        />}
    >
        <NewOriginView />
    </React.Suspense>);
};
