/**
 * <AUTHOR>
 * @namespace Database_Utils
 * @description Label Color
 */

import { LABEL_COLOR } from "@/common/database/label/label-color";
import React from "react";

export const getLabelColorClassName = (
    color: LABEL_COLOR,
): string | null => {

    switch (color) {

        case LABEL_COLOR.BLACK: return "bg-black";
        case LABEL_COLOR.BLUE: return "bg-blue-600";
        case LABEL_COLOR.BROWN: return "bg-amber-600";
        case LABEL_COLOR.CYAN: return "bg-cyan-600";
        case LABEL_COLOR.GREEN: return "bg-green-600";
        case LABEL_COLOR.ORANGE: return "bg-orange-600";
        case LABEL_COLOR.PINK: return "bg-pink-600";
        case LABEL_COLOR.PURPLE: return "bg-purple-600";
        case LABEL_COLOR.RED: return "bg-red-600";
        case LABEL_COLOR.VIOLET: return "bg-violet-600";
        case LABEL_COLOR.YELLOW: return "bg-yellow-600";
    }

    return null;
};

export const getLabelColorTextClassNameReverse = (
    color: LABEL_COLOR,
): string | null => {

    switch (color) {

        case LABEL_COLOR.BLACK: return "text-white";
        case LABEL_COLOR.BLUE: return "text-white";
        case LABEL_COLOR.BROWN: return "text-white";
        case LABEL_COLOR.CYAN: return "text-white";
        case LABEL_COLOR.GREEN: return "text-white";
        case LABEL_COLOR.ORANGE: return "text-white";
        case LABEL_COLOR.PINK: return "text-white";
        case LABEL_COLOR.PURPLE: return "text-white";
        case LABEL_COLOR.RED: return "text-white";
        case LABEL_COLOR.VIOLET: return "text-white";
        case LABEL_COLOR.YELLOW: return "text-white";
    }

    return null;
};

export const getLabelColorDot = (
    color: LABEL_COLOR,
): React.ReactNode => {

    const className: string | null = getLabelColorClassName(color);

    if (!className) {
        return null;
    }

    return (<div
        className={`w-4 h-4 rounded-full ${className}`}
    />);
};
