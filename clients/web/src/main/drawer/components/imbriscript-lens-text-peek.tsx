/**
 * <AUTHOR>
 * @namespace Drawer_Components
 * @description Imbriscript Lens Text Peek
 */

import { ImbriScriptExecuteContext } from "@imbricate-hummingbird/script-core";
import { UIDrawerBody } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { RenderImbriscriptText } from "../../lens/components/renders/render-imbriscript-text";

export type DrawerImbriscriptLensTextPeekProps = {

    readonly imbriscriptText: string;

    readonly context: ImbriScriptExecuteContext;
};

// LAZY LOAD ONLY
const DrawerImbriscriptLensTextPeek: FC<DrawerImbriscriptLensTextPeekProps> = (
    props: DrawerImbriscriptLensTextPeekProps,
) => {

    const summary = props.imbriscriptText.length;

    return (<UIDrawerBody
        className="relative"
    >
        <RenderImbriscriptText
            imbriscriptText={props.imbriscriptText}
            context={props.context}
            deps={[summary]}
        />
    </UIDrawerBody>);
};
export default DrawerImbriscriptLensTextPeek;
