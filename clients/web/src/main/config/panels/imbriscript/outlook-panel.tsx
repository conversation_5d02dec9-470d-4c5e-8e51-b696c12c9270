/**
 * <AUTHOR>
 * @namespace Config_Panels_ImbricateScript_OutlookPanel
 * @description Outlook Panel
 */

import React, { FC } from "react";
import { ConfigImbricateScriptOutlookActionButtonCard } from "../../components/imbriscript/outlook/action-button-card";

export type ConfigImbricateScriptOutlookPanelProps = {
};

export const ConfigImbricateScriptOutlookPanel: FC<ConfigImbricateScriptOutlookPanelProps> = (
    _props: ConfigImbricateScriptOutlookPanelProps,
) => {

    return (<div
        className="flex flex-col gap-2 pr-2 py-2 flex-1"
    >
        <ConfigImbricateScriptOutlookActionButtonCard />
    </div>);
};
