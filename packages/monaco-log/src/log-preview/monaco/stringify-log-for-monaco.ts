/**
 * <AUTHOR>
 * @namespace Monaco_Log_Preview_Monaco
 * @description Stringify Log For Monaco
 */

import { ImbriScriptContainerLog } from "@imbricate-hummingbird/script-core";

export const stringifyLogForMonaco = (
    logs: ImbriScriptContainerLog[],
    progressing?: boolean,
): string => {

    const logList = logs.flatMap((log: ImbriScriptContainerLog) => {
        return log.message.split("\n");
    });

    if (progressing) {
        return [
            ...logList,
            "...",
        ].join("\n");
    }

    return logList.join("\n");
};
