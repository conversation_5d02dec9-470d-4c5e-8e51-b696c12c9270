/**
 * <AUTHOR>
 * @namespace Lens
 * @description Lens Edit View
 */

import { LENS_CONFIG_SOURCE, LensConfig, LensConfigItem } from "@imbricate-hummingbird/configuration";
import { LoadingWrapper } from "@imbricate-hummingbird/react-components";
import { useLensConfig } from "@imbricate-hummingbird/react-store";
import { FC } from "react";
import { useParams } from "react-router-dom";
import { LensEditView } from "./lens-edit-view";

export type LensEditViewFetcherProps = {
};

// LAZY LOAD ONLY
const LensEditViewFetcher: FC<LensEditViewFetcherProps> = (
    _props: LensEditViewFetcherProps,
) => {

    const params = useParams();
    const lensIdentifier: string =
        params["lens-identifier"] as string;

    const lensConfig: LensConfig = useLensConfig();

    const targetLens: LensConfigItem<LENS_CONFIG_SOURCE> | undefined =
        lensConfig.items.find((lens: LensConfigItem<LENS_CONFIG_SOURCE>) => {
            return lens.lensIdentifier === lensIdentifier;
        });

    if (!targetLens) {
        return (<LoadingWrapper
            debugDescription="Lens Edit View Fetcher"
            fullHeight
        />);
    }

    return (<LensEditView
        targetLens={targetLens}
    />);
};
export default LensEditViewFetcher;
