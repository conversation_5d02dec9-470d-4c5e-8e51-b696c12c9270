/**
 * <AUTHOR>
 * @namespace Main_Config_Components_ImbriScript
 * @description Quick Execute
 */

import { HUMMINGBIRD_IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE, HUMMINGBIRD_PREFERENCE_ITEM, usePreferenceItem } from "@imbricate-hummingbird/configuration";
import { FormattedRadioGroup, FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { FaPersonRunning } from "react-icons/fa6";
import { useConfigFormat } from "../../internationalization/hook";
import { CONFIG_PROFILE } from "../../internationalization/profile";

export const ConfigImbriScriptQuickExecuteCard = () => {

    const configFormat = useConfigFormat();

    const imbriScriptQuickExecuteConfirmMode = usePreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE,
    );

    return (<StyledCard>
        <UICardHeader>
            <FaPersonRunning />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.IMBRISCRIPT_QUICK_EXECUTE)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-2"
        >
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                    className="text-sm text-gray-500"
                >
                    {configFormat.get(CONFIG_PROFILE.IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE_DESCRIPTION)}
                </FormattedText>
            </div>
            <FormattedRadioGroup
                label={configFormat.get(CONFIG_PROFILE.IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE)}
                selected={imbriScriptQuickExecuteConfirmMode.preference}
                onSelectedChange={(newSelected: string) => {

                    imbriScriptQuickExecuteConfirmMode.updatePreference(
                        newSelected as HUMMINGBIRD_IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE,
                    );
                }}
                format={configFormat}
                items={[
                    {
                        itemKey: HUMMINGBIRD_IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE.CONFIRM_IN_DRAWER,
                        itemProfileItem: CONFIG_PROFILE.CONFIRM_IN_DRAWER,
                    },
                    {
                        itemKey: HUMMINGBIRD_IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE.NO_CONFIRM,
                        itemProfileItem: CONFIG_PROFILE.NO_CONFIRM,
                    },
                ]}
            />
        </UICardBody>
    </StyledCard>);
};
