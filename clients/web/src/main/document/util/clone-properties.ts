/**
 * <AUTHOR>
 * @namespace Document_Utils
 * @description Clone Properties
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchema, getImbricateDefaultValueOfProperty } from "@imbricate/core";

export const cloneAndFillDocumentProperties = (
    properties: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[],
    schema: ImbricateDatabaseSchema,
): TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[] => {

    const clonedProperties: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[] = cloneDocumentProperties(properties);

    for (const property of schema.properties) {

        if (clonedProperties.find((draft) => {
            return draft.key === property.propertyIdentifier;
        })) {
            continue;
        }

        clonedProperties.push({
            key: property.propertyIdentifier,
            type: property.propertyType,
            value: getImbricateDefaultValueOfProperty(property.propertyType),
            variant: property.propertyVariant,
        });
    }

    return clonedProperties;
};

export const cloneDocumentProperties = (
    properties: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[],
): TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[] => {

    const newProperties: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[] = [];

    for (const property of properties) {

        newProperties.push({
            key: property.key,
            type: property.type,
            value: property.value,
            variant: property.variant,
        });
    }

    return newProperties;
};
