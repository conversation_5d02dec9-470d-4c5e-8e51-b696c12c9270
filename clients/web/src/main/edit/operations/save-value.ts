/**
 * <AUTHOR>
 * @namespace Edit_Actions
 * @description Save Value
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createSaveValueAction = (
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Save Value",
        actionDescription: "Save the value of the property",
        executerMetadata: metadata,
    };
};
