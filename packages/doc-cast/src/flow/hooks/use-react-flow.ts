/**
 * <AUTHOR>
 * @namespace Flow_Hooks
 * @description Use React Flow
 */

import { useFlowLayout } from "@imbricate-hummingbird/flow-common";
import { Connection, Edge, EdgeChange, Node, NodeChange, ReactFlowInstance } from "@xyflow/react";
import { useRef, useState } from "react";
import { DOC_CAST_SOURCE_TYPE, DocCastSource } from "../../types/source";
import { createDocCastFlowNodes } from "../util/create-nodes";
import { useDocCastFlowConnection } from "./use-connect";
import { useDocCastFlowEdgesChange } from "./use-edges-change";
import { useDocCastFlowNodesChange } from "./use-nodes-change";
import { useDocCastFlowReconnect } from "./use-reconnect";
import { useDocCastFlowReconnectEnd } from "./use-reconnect-end";
import { useDocCastFlowReconnectStart } from "./use-reconnect-start";

export type UseDocCastFlowReactFlowResult = {

    readonly nodes: Node[];
    readonly edges: Edge[];

    readonly onNodesChange: (changes: NodeChange[]) => void;
    readonly onEdgesChange: (changes: EdgeChange[]) => void;

    readonly onConnect: (connection: Connection) => void;

    readonly onReconnectStart: (event: React.MouseEvent, edge: Edge) => void;
    readonly onReconnectEnd: (event: MouseEvent | TouchEvent, edge: Edge) => void;
    readonly onReconnect: (oldEdge: Edge, newConnection: Connection) => void;

    readonly onLayout: (direction: string) => void;
};

export const useDocCastFlowReactFlow = (
    reactFlow: ReactFlowInstance,
    source: DocCastSource<DOC_CAST_SOURCE_TYPE>,
) => {

    const edgeReconnectSuccessful = useRef(true);

    const [parsedNodes, setParsedNodes] = useState(() => {
        return createDocCastFlowNodes(source);
    });

    const [parsedEdges, setParsedEdges] = useState(() => {
        return [] as Edge[];
    });

    const onNodesChange = useDocCastFlowNodesChange(setParsedNodes);
    const onEdgesChange = useDocCastFlowEdgesChange(setParsedEdges);

    const onConnect = useDocCastFlowConnection(setParsedEdges);

    const onReconnectStart = useDocCastFlowReconnectStart(
        edgeReconnectSuccessful,
    );
    const onReconnectEnd = useDocCastFlowReconnectEnd(
        setParsedEdges,
        edgeReconnectSuccessful,
    );
    const onReconnect = useDocCastFlowReconnect(
        setParsedEdges,
        edgeReconnectSuccessful,
    );

    const onLayout = useFlowLayout(
        parsedNodes,
        parsedEdges,
        setParsedNodes,
        reactFlow,
    );

    return {

        nodes: parsedNodes,
        edges: parsedEdges,
        onNodesChange,
        onEdgesChange,
        onConnect,
        onReconnectStart,
        onReconnectEnd,
        onReconnect,
        onLayout,
    };
};
