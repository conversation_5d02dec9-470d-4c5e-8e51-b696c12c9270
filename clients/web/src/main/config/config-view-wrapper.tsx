/**
 * <AUTHOR>
 * @namespace Config
 * @description Config View Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const ConfigViewApplication = LazyLoadComponent(
    () => import("./config-view"),
    "Config View Application",
);

export const ConfigViewWrapper: FC = () => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Config View Application"
            fullHeight
        />}
    >
        <ConfigViewApplication />
    </React.Suspense>);
};
