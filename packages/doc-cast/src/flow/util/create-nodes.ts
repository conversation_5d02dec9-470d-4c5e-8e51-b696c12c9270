/**
 * <AUTHOR>
 * @namespace Flow_Util
 * @description Create Nodes
 */

import { Node } from "@xyflow/react";
import { DOC_CAST_SOURCE_TYPE, DocCastSource } from "../../types/source";
import { DOC_CAST_NODE_TYPE } from "../node/node-types";
import { DocCastSourceNodeData } from "../node/source-node/source-node-data";

export const createDocCastFlowNodes = (
    source: DocCastSource<DOC_CAST_SOURCE_TYPE>,
): Node[] => {

    // DOC_CAST_SOURCE_TYPE SWITCH
    switch (source.type) {
        case DOC_CAST_SOURCE_TYPE.DOCUMENT_STACK_IDENTIFIER: {

            return [
                {
                    id: "1",
                    position: {
                        x: 0,
                        y: 0,
                    },
                    data: {
                        label: "Document Stack Identifier",
                    } satisfies DocCastSourceNodeData,
                    type: DOC_CAST_NODE_TYPE.SOURCE_NODE,
                },
            ];
        }
    }

    return [];
};
