/**
 * <AUTHOR>
 * @namespace Origin_Components
 * @description Origin Information View
 */

import { StyledSnippet } from "@/common/components/snippet/styled-snippet";
import { getOriginStorageInstance, putOriginStorageInstance } from "@/main/origin/origin-storage";
import { OriginStorageInstance } from "@imbricate-hummingbird/origin-central";
import { StyledCard } from "@imbricate-hummingbird/react-components";
import { useReloadApplication } from "@imbricate-hummingbird/react-navigation";
import { OriginInstance } from "@imbricate-hummingbird/transfer-core";
import { UIButton, UICardBody, UICardFooter, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import React, { FC, useEffect } from "react";
import { NewOriginSwitch } from "./origin-new/origin-new-switch";

export type OriginInformationViewProps = {

    readonly originUniqueIdentifier: string;
    readonly originStorageInstance: OriginInstance;
};

export const OriginInformationView: FC<OriginInformationViewProps> = (
    props: OriginInformationViewProps,
) => {

    const reloadApplication = useReloadApplication();

    const [updatedOriginInstance, setUpdatedOriginInstance] =
        React.useState<OriginInstance | null>(null);

    const [originUpdated, setOriginUpdated] = React.useState<boolean>(false);
    const [loading, setLoading] = React.useState<boolean>(false);

    useEffect(() => {
        setUpdatedOriginInstance(props.originStorageInstance);
    }, [props.originStorageInstance]);

    if (!updatedOriginInstance) {
        return null;
    }

    return (<div
        className="flex flex-col gap-2"
    >
        <StyledCard>
            <UICardHeader>
                Origin Information
            </UICardHeader>
            <UIDivider />
            <UICardBody
                className="whitespace-pre-wrap"
            >
                <StyledSnippet
                    aria-label="Origin Identifier"
                    symbol="#"
                >
                    {props.originUniqueIdentifier}
                </StyledSnippet>
                <UISpacer />
                <NewOriginSwitch
                    originType={updatedOriginInstance.type}
                    originInstance={updatedOriginInstance}
                    onOriginChange={(
                        instance: OriginInstance,
                    ) => {
                        setUpdatedOriginInstance(instance);
                        setOriginUpdated(true);
                    }}
                />
            </UICardBody>
            {originUpdated && <React.Fragment>
                <UIDivider />
                <UICardFooter>
                    <UIButton
                        variant="flat"
                        isLoading={loading}
                        color="primary"
                        onPress={async () => {

                            setLoading(true);

                            const originInstances = getOriginStorageInstance();

                            const updatedInstances: OriginStorageInstance = {
                                origins: originInstances.origins.map((
                                    origin: OriginInstance,
                                ) => {

                                    if (origin.originStorageInstanceIdentifier === props.originStorageInstance.originStorageInstanceIdentifier) {
                                        return updatedOriginInstance;
                                    }
                                    return origin;
                                }),
                            };

                            putOriginStorageInstance(updatedInstances);

                            reloadApplication();
                        }}
                    >
                        Update Origin
                    </UIButton>
                </UICardFooter>
            </React.Fragment>}
        </StyledCard>
    </div>);
};
