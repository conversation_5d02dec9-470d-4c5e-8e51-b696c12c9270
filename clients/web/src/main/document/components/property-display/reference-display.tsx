/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyDisplay
 * @description Reference Display
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { IMBRICATE_PROPERTY_TYPE, ImbricatePropertyValueObjectReference } from "@imbricate/core";
import { FC } from "react";
import { DocumentReferenceValueSelectedReference } from "../property-value/reference/selected-reference";

export type DocumentReferenceDisplayProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.REFERENCE>;
};

export const DocumentReferenceDisplay: FC<DocumentReferenceDisplayProps> = (
    props: DocumentReferenceDisplayProps,
) => {

    const fixedValue: ImbricatePropertyValueObjectReference[] =
        Array.isArray(props.property.value)
            ? props.property.value
            : [];

    return (<div>
        {fixedValue.map((
            value: ImbricatePropertyValueObjectReference,
        ) => {

            const fixedKey: string = [
                value.originUniqueIdentifier,
                value.databaseUniqueIdentifier,
                value.documentUniqueIdentifier,
            ].join("/");

            return (<div
                key={fixedKey}
            >
                <DocumentReferenceValueSelectedReference
                    reference={value}
                />
            </div>);
        })}
    </div>);
};
