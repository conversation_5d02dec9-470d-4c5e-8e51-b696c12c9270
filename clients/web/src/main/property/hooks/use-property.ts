/**
 * <AUTHOR>
 * @namespace Property_Hooks
 * @description Use Property
 */

import { ReactDependency } from "@imbricate-hummingbird/react-common";
import { IImbricateDatabase, IImbricateDocument, IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaProperty } from "@imbricate/core";
import { S_UseDatabaseErrored, S_UseDatabaseLoading, S_UseDatabaseNotFound, UseDatabaseResponseSymbol, useDatabase } from "../../database/hooks/use-database";
import { S_UseDocumentErrored, S_UseDocumentLoading, S_UseDocumentNotFound, UseDocumentResponseSymbol, useOldDocument } from "../../document/hooks/use-document";
import { S_UsePropertyErrored, S_UsePropertyLoading, S_UsePropertyNotFound, UsePropertyResponse, UsePropertyResponseSymbol } from "../types/use-property";

export type UsePropertyDependencies = {

    databaseDependencies?: ReactDependency[];
    documentDependencies?: ReactDependency[];
};

export const useProperty = (
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    propertyUniqueIdentifier: string,
    dependencies: UsePropertyDependencies = {},
): UsePropertyResponse | UsePropertyResponseSymbol => {

    const document: IImbricateDocument | UseDocumentResponseSymbol =
        useOldDocument(
            databaseUniqueIdentifier,
            documentUniqueIdentifier,
            ...(dependencies.documentDependencies ?? []),
        );

    const database: IImbricateDatabase | UseDatabaseResponseSymbol =
        useDatabase(databaseUniqueIdentifier);

    if (document === S_UseDocumentLoading || database === S_UseDatabaseLoading) {
        return S_UsePropertyLoading;
    }

    if (document === S_UseDocumentNotFound || database === S_UseDatabaseNotFound) {
        return S_UsePropertyNotFound;
    }

    if (document === S_UseDocumentErrored || database === S_UseDatabaseErrored) {
        return S_UsePropertyErrored;
    }

    const targetProperty = database.schema.properties.find((
        property: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>,
    ) => {
        return property.propertyIdentifier === propertyUniqueIdentifier;
    });

    if (!targetProperty) {
        return S_UsePropertyNotFound;
    }

    const relatedProperty = document.getProperty(targetProperty.propertyIdentifier);

    if (typeof relatedProperty === "symbol") {
        return {
            schemaProperty: targetProperty,
        };
    }

    return {
        schemaProperty: targetProperty,
        documentProperty: relatedProperty.property,
    };
};
