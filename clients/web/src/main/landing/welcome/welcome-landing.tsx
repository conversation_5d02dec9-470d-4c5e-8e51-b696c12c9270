/**
 * <AUTHOR>
 * @namespace Landing_Welcome
 * @description Welcome Landing
 */

import { getRouteWelcomeAddOriginView, getRouteWelcomeImportConfigurationView } from "@imbricate-hummingbird/navigation-core";
import { useTitle } from "@imbricate-hummingbird/react-common";
import { UIDivider, UILink, UIListbox } from "@imbricate-hummingbird/ui";
import { TbCloud, TbFileImport, TbWorld } from "react-icons/tb";
import { POLICY_HUMMINGBIRD_PRIVACY_POLICY, POLICY_HUMMINGBIRD_TERMS_OF_SERVICE } from "../../common/static/policy";
import { LandingMainWrapper } from "../components/landing-wrapper";

// LAZY LOAD ONLY
const WelcomeLanding = () => {

    useTitle([
        "Welcome",
    ], []);

    return (<LandingMainWrapper>
        <div
            className="p-3"
        >
            <h2 className="text-5xl font-mono font-bold text-center">
                Welcome to Imbricate
            </h2>
            <p className="text-xl font-mono text-center">
                Notebook for Engineers
            </p>
            <UIDivider
                className="mt-5"
            />
            <div className="my-2">
                <p
                    className="text-small text-gray-500"
                >
                    By continue, you agree to the <UILink
                        className="text-small text-blue-500"
                        href={POLICY_HUMMINGBIRD_TERMS_OF_SERVICE}
                        isExternal
                        showAnchorIcon
                    >
                        Imbricate Hummingbird Terms of Service
                    </UILink>
                    and <UILink
                        className="text-small text-blue-500"
                        href={POLICY_HUMMINGBIRD_PRIVACY_POLICY}
                        isExternal
                        showAnchorIcon
                    >
                        Imbricate Hummingbird Privacy Policy
                    </UILink>.
                </p>
            </div>
            <p className="text-xl font-mono">
                Get started by
            </p>
            <UIListbox
                ariaLabel="Listbox menu with add origin options"
                variant="flat"
                items={[
                    {
                        key: "customized-origin",
                        titleContent: "Add a customized Origin",
                        descriptionContent: "Create a new origin with your own configuration.",
                        titleClassName: "font-mono",
                        href: getRouteWelcomeAddOriginView(),
                        startContent: (<TbWorld
                            className="text-2xl"
                        />),
                        color: "primary",
                    },
                    {
                        key: "import-configuration",
                        titleContent: "Import a Configuration",
                        descriptionContent: "Import a configuration imbricate hummingbird configuration.",
                        titleClassName: "font-mono",
                        href: getRouteWelcomeImportConfigurationView(),
                        startContent: (<TbFileImport
                            className="text-2xl"
                        />),
                        color: "primary",
                    },
                    {
                        key: "imbricate-cloud",
                        titleContent: "Connect to Imbricate Cloud",
                        descriptionContent: "Connect to Imbricate Cloud to access your origins and configurations.",
                        titleClassName: "font-mono",
                        startContent: (<TbCloud
                            className="text-2xl"
                        />),
                        color: "primary",
                        isDisabled: true,
                    },
                ]}
            />
        </div>
    </LandingMainWrapper>);
};

export default WelcomeLanding;
