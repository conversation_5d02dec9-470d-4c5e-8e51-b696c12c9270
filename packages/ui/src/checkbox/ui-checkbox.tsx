/**
 * <AUTHOR>
 * @package UI
 * @namespace Checkbox
 * @description Checkbox
 */

import { Checkbox } from "@heroui/checkbox";
import type { FC, ReactNode } from "react";
import { UIColor, UISize } from "../common/types";

export type UICheckboxProps = {

    readonly className?: string;

    readonly isDisabled?: boolean;
    readonly isReadOnly?: boolean;
    readonly isRequired?: boolean;
    readonly isInvalid?: boolean;
    readonly isIndeterminate?: boolean;

    readonly size?: UISize;
    readonly color?: UIColor;

    readonly defaultSelected?: boolean;
    readonly isSelected?: boolean;
    readonly onValueChange?: (newValue: boolean) => void;

    readonly children?: ReactNode;
};

export const UICheckbox: FC<UICheckboxProps> = (
    props: UICheckboxProps,
) => {

    return (<Checkbox
        className={props.className}

        isDisabled={props.isDisabled}
        isReadOnly={props.isReadOnly}
        isRequired={props.isRequired}
        isInvalid={props.isInvalid}
        isIndeterminate={props.isIndeterminate}

        size={props.size}
        color={props.color}

        defaultSelected={props.defaultSelected}
        isSelected={props.isSelected}
        onValueChange={props.onValueChange}
    >
        {props.children}
    </Checkbox>);
};
