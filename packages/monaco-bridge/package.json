{"name": "@imbricate-hummingbird/monaco-bridge", "description": "Bridge implementation for Monaco", "private": true, "main": "src/index.ts", "scripts": {"test": "jest", "coverage": "jest --coverage", "compile": "tsc --project ./typescript/tsconfig.compile.json", "lint": "eslint src"}, "dependencies": {"monaco-editor": "catalog:"}, "devDependencies": {"@imbricate-hummingbird/config": "workspace:*", "eslint": "catalog:"}}