/**
 * <AUTHOR>
 * @namespace Drawer_Component_Wrappers
 * @description Markdown Text Peek Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import { DrawerMarkdownTextPeekProps } from "../components/markdown-text-peek";

const DrawerMarkdownTextPeek = LazyLoadComponent(
    () => import("../components/markdown-text-peek"),
    "Drawer Markdown Text Peek",
);

export const DrawerMarkdownTextPeekWrapper: FC<DrawerMarkdownTextPeekProps> = (
    props: DrawerMarkdownTextPeekProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Drawer Markdown Text Peek"
            label={["markdown"]}
            fullHeight
        />}
    >
        <DrawerMarkdownTextPeek
            {...props}
        />
    </React.Suspense>);
};
