/**
 * <AUTHOR>
 * @namespace Document_Operation_InvalidQueryDocumentsCache
 * @description Invalid Query Documents Cache
 */

import { ActionCentral, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";
import { OriginWorkerDataCentral } from "@imbricate-hummingbird/origin-central-web-worker";
import { ImbricateDocumentQuery } from "@imbricate/core";
import { createInvalidQueryDocumentsCacheAction } from "../actions/invalid-query-documents-cache";

export const documentInvalidateQueryDocumentsCache = async (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
    query: ImbricateDocumentQuery,
    metadata: ActionDescriptionExecuterMetadata,
) => {

    await ActionCentral.getInstance().executeAction(
        async (
            actionIdentifier: string,
            recordIdentifier: string,
        ) => {

            return await OriginWorkerDataCentral.getInstance().invalidateQueryDocumentsCache(
                actionIdentifier,
                recordIdentifier,
                originUniqueIdentifier,
                databaseUniqueIdentifier,
                query,
            );
        },
        createInvalidQueryDocumentsCacheAction(
            originUniqueIdentifier,
            databaseUniqueIdentifier,
            query,
            metadata,
        ),
    );
};
