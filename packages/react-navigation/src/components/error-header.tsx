/**
 * <AUTHOR>
 * @namespace Navigation_Components
 * @description Error Header
 */

import { UIAlert } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { useDisconnectSlice } from "../../../react-store/src/disconnect/feature";
import { DisconnectedItem } from "../../../react-store/src/disconnect/types/disconnect";

export const NavigationErrorHeader: FC = () => {

    const disconnectSlice = useDisconnectSlice();

    return (<div
        className="flex flex-col gap-2"
    >
        {disconnectSlice.disconnectConfig.items.map((item: DisconnectedItem) => {

            return (<UIAlert
                key={`disconnect-${item.originUniqueIdentifier}`}
                title={`${item.originInstance.originName} Disconnected`}
                description=""
                color="danger"
            >
                <div
                    className="text-small"
                >
                    Origin "{item.originInstance.originName}" has been disconnected due to {item.type}.
                </div>
            </UIAlert>);
        })}
    </div>);
};
