/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyValue_Markdown
 * @description Edit Full Mode
 */

import { openEditWindow, openViewWindow } from "@imbricate-hummingbird/navigation-core";
import { getPropertyIcon } from "@imbricate-hummingbird/react-components";
import { DRAWER_TYPE, useDrawerAction } from "@imbricate-hummingbird/react-store";
import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIButton, UIButtonGroup, UIInput, UIPopover, createUIButton } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import React, { FC } from "react";
import { FaEye, FaLink } from "react-icons/fa6";
import { MdEdit } from "react-icons/md";
import { PiKeyholeFill } from "react-icons/pi";
import { TbLayoutSidebarRightExpandFilled } from "react-icons/tb";
import { CommonCopyItem } from "../../../../common/components/copy-item";
import { useDocumentFormat } from "../../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../../internationalization/profile";

export type DocumentMarkdownValueEditFullModeProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.MARKDOWN>;
    readonly updateProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.MARKDOWN>) => void;
};

export const DocumentMarkdownValueEditFullMode: FC<DocumentMarkdownValueEditFullModeProps> = (
    props: DocumentMarkdownValueEditFullModeProps,
) => {

    const documentFormat = useDocumentFormat();

    const [relinkValue, setRelinkValue] = React.useState<string>("");

    const drawerActions = useDrawerAction();

    return (<div
        className="flex flex-col gap-1 w-full"
    >
        <div className="flex gap-1 items-center">
            <CommonCopyItem
                className="flex-1"
                startContent="Markdown Document Identifier"
                prefix={<PiKeyholeFill />}
                content={props.property.value}
            />
            <UIPopover
                onOpenChange={(isOpen) => {
                    if (!isOpen) {
                        setRelinkValue("");
                    }
                }}
                trigger={createUIButton({
                    startContent: (<FaLink
                        className="text-large"
                    />),
                    variant: "flat",
                    color: "danger",
                    children: "Relink",
                })}
                contentClassName="min-w-[426px]"
            >
                <div
                    className="px-1 py-2 w-full"
                >
                    <p
                        className="text-small font-bold text-foreground flex gap-2 items-center"
                    >
                        <FaLink
                            className="text-large"
                        /> Markdown Text Object Relink
                    </p>
                    <div
                        className="mt-2 flex flex-col gap-2 w-full"
                    >
                        <UIInput
                            isAutofocus
                            startContent={getPropertyIcon(
                                IMBRICATE_PROPERTY_TYPE.MARKDOWN,
                                "text-large",
                            )}
                            label="Text Object Identifier"
                            variant="bordered"
                            value={relinkValue}
                            onValueChange={(
                                newValue: string,
                            ) => setRelinkValue(newValue)}
                        />
                        {relinkValue.length > 0 && <UIButton
                            size="sm"
                            variant="flat"
                            color="danger"
                            className="self-start"
                            onPress={() => {

                                props.updateProperty({
                                    key: props.propertyKey,
                                    type: IMBRICATE_PROPERTY_TYPE.MARKDOWN,
                                    value: relinkValue,
                                    variant: props.property.variant,
                                });
                            }}
                        >
                            Relink Markdown Document
                        </UIButton>}
                    </div>
                </div>
            </UIPopover>
        </div>
        <UIButton
            isFullWidth
            startContent={<MdEdit />}
            color="secondary"
            variant="flat"
            onPress={() => {

                if (!props.originUniqueIdentifier
                    || !props.databaseUniqueIdentifier
                    || !props.documentUniqueIdentifier) {
                    throw new Error("[Imbricate] Database or document unique identifier not found");
                }

                openEditWindow(
                    props.originUniqueIdentifier,
                    props.databaseUniqueIdentifier,
                    props.documentUniqueIdentifier,
                    props.propertyKey,
                );
            }}
        >
            {documentFormat.get(DOCUMENT_PROFILE.EDIT_MARKDOWN_DOCUMENT)}
        </UIButton>
        <UIButtonGroup
            isFullWidth
            variant="faded"
        >
            <UIButton
                startContent={<FaEye />}
                onPress={() => {

                    if (!props.originUniqueIdentifier
                        || !props.databaseUniqueIdentifier
                        || !props.documentUniqueIdentifier) {
                        throw new Error("[Imbricate] Database or document unique identifier not found");
                    }

                    openViewWindow(
                        props.originUniqueIdentifier,
                        props.databaseUniqueIdentifier,
                        props.documentUniqueIdentifier,
                        props.propertyKey,
                    );
                }}
            >
                {documentFormat.get(DOCUMENT_PROFILE.VIEW_MARKDOWN_DOCUMENT)}
            </UIButton>
            <UIButton
                startContent={<TbLayoutSidebarRightExpandFilled
                    className="text-large"
                />}
                onPress={() => {

                    drawerActions.openDrawer({
                        type: DRAWER_TYPE.TEXT_RAW_PEEK,
                        title: "Markdown Document Preview",

                        payload: {
                            originUniqueIdentifier: props.originUniqueIdentifier,
                            textUniqueIdentifier: props.property.value,
                        },
                    });
                }}
            >
                Preview Markdown Document
            </UIButton>
        </UIButtonGroup>
    </div>);
};
