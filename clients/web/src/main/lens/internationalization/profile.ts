/**
 * <AUTHOR>
 * @namespace Lens_Internationalization
 * @description Profile
 */

export enum LENS_PROFILE {

    COMPLETED_AT = "COMPLETED_AT",
    CREATE_LENS = "CREATE_LENS",
    CREATE_NEW_LENS = "CREATE_NEW_LENS",
    DANGER_ZONE = "DANGER_ZONE",
    DELETE_LENS = "DELETE_LENS",
    DELETE_LENS_DESCRIPTION = "DELETE_LENS_DESCRIPTION",
    EDIT_IMBRISCRIPT = "EDIT_IMBRISCRIPT",
    EDIT_IMBRISCRIPT_DESCRIPTION = "EDIT_IMBRISCRIPT_DESCRIPTION",
    EDIT_LENS = "EDIT_LENS",
    EDIT_LENS_DESCRIPTION = "EDIT_LENS_DESCRIPTION",
    EDITING_LENS = "EDITING_LENS",
    HIDE_EXTRA_DETAILS = "HIDE_EXTRA_DETAILS",
    IMBRISCRIPT = "IMBRISCRIPT",
    IMBRISCRIPT_EXECUTION = "IMBRISCRIPT_EXECUTION",
    IN = "IN",
    LENS = "LENS",
    LENS_IDENTIFIER = "LENS_IDENTIFIER",
    LENS_SOURCE = "LENS_SOURCE",
    LENS_NAME = "LENS_NAME",
    LENS_TARGET = "LENS_TARGET",
    MS = "MS",
    NAVIGATE_TO_LENS_SOURCE = "NAVIGATE_TO_LENS_SOURCE",
    OPEN_IMBRISCRIPT_DOCUMENT_IN_DRAWER = "OPEN_IMBRISCRIPT_DOCUMENT_IN_DRAWER",
    OPEN_IMBRISCRIPT_DOCUMENT_IN_DRAWER_DESCRIPTION = "OPEN_IMBRISCRIPT_DOCUMENT_IN_DRAWER_DESCRIPTION",
    RELOAD_LENS = "RELOAD_LENS",
    RELOAD_LENS_DESCRIPTION = "RELOAD_LENS_DESCRIPTION",
    RENDER_LENS = "RENDER_LENS",
    RENDER_LENS_DESCRIPTION = "RENDER_LENS_DESCRIPTION",
    SHOW_EXTRA_DETAILS = "SHOW_EXTRA_DETAILS",
    UPDATE_LENS = "UPDATE_LENS",
    VIEW_LENS = "VIEW_LENS",
    VIEW_LENS_DESCRIPTION = "VIEW_LENS_DESCRIPTION",
}
