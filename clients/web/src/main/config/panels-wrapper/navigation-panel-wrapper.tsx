/**
 * <AUTHOR>
 * @namespace Config_Panels_Wrapper
 * @description Navigation Panel Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const ConfigNavigationPanel = LazyLoadComponent(
    () => import("../panels/navigation-panel"),
    "Config Navigation Panel",
);

export type ConfigNavigationPanelWrapperProps = {
};

export const ConfigNavigationPanelWrapper: FC<ConfigNavigationPanelWrapperProps> = (
    _props: ConfigNavigationPanelWrapperProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Config Navigation Panel"
            fullHeight
        />}
    >
        <ConfigNavigationPanel />
    </React.Suspense>);
};
