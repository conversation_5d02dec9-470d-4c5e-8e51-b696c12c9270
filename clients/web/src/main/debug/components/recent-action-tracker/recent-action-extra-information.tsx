/**
 * <AUTHOR>
 * @namespace Debug_Components_RecentActionTracker
 * @description Recent Action Extra Information
 */

import { RecentActionItem } from "@imbricate-hummingbird/interceptor-core";
import { UIAccordion } from "@imbricate-hummingbird/ui";
import { WORKER_WEB_WORKER } from "@imbricate-hummingbird/web-worker-core";
import { FC } from "react";

export type RecentActionExtraInformationProps = {

    readonly showExtraInformation: boolean;

    readonly action: RecentActionItem<WORKER_WEB_WORKER>;
};

export const RecentActionExtraInformation: FC<RecentActionExtraInformationProps> = (
    props: RecentActionExtraInformationProps,
) => {

    if (!props.showExtraInformation) {
        return null;
    }

    const action: RecentActionItem<WORKER_WEB_WORKER> = props.action;

    return (<UIAccordion
        isCompact
        variant="bordered"
        itemKey="extra-information"
        itemTitle="Extra Information"
    >
        <div
            className="text-tiny font-mono w-full"
        >
            Action Item Identifier: {action.actionItemIdentifier}
        </div>
        <div
            className="text-tiny font-mono w-full"
        >
            Recent Action Identifier: {action.recentActionIdentifier}
        </div>
        <div
            className="text-tiny font-mono w-full"
        >
            Parent Recent Action Identifier: {action.parentRecentActionIdentifier}
        </div>
        <div
            className="text-tiny font-mono w-full"
        >
            Handled By: {action.handledBy.join(", ")}
        </div>
    </UIAccordion>);
};