/**
 * <AUTHOR>
 * @namespace Lens_Components_Renders
 * @description Render Imbriscript Text
 */

import { DebugInformation } from "@/debug/components/debug-information";
import { OutlookBlocks } from "@/script/components/outlook/outlook-blocks";
import { useInlineScriptResult } from "@/script/hooks/use-inline-script-result";
import { ReactDependency } from "@imbricate-hummingbird/react-common";
import { LoadingWrapper } from "@imbricate-hummingbird/react-components";
import { ImbriScriptExecuteContext } from "@imbricate-hummingbird/script-core";
import { END_SIGNAL } from "@sudoo/marked";
import { FC } from "react";
import { RenderImbriscriptTextSignature } from "./imbriscript/signature";
import { RenderImbriscriptError } from "./render-imbriscript-error";
import { RenderPending } from "./render-pending";

export type RenderImbriscriptTextProps = {

    readonly imbriscriptText: string;
    readonly context: ImbriScriptExecuteContext;

    readonly deps: ReactDependency[];
};

export const RenderImbriscriptText: FC<RenderImbriscriptTextProps> = (
    props: RenderImbriscriptTextProps,
) => {

    const executeResult = useInlineScriptResult(
        `Render Imbriscript Text: ${props.imbriscriptText.length}`,
        props.imbriscriptText,
        props.context,
        props.deps,
    );

    if (!executeResult) {

        return (<LoadingWrapper
            debugDescription="Render Imbriscript Text"
            fullHeight
        />);
    }

    if (executeResult.result
        && executeResult.result.signal === END_SIGNAL.SUCCEED
    ) {

        const defaultExport = executeResult.result.exports.default;

        if (typeof defaultExport !== "object") {

            return (<RenderImbriscriptError
                result={executeResult}
            />);
        }

        return (<div
            className="flex flex-col gap-2 pb-3"
        >
            <OutlookBlocks
                outlookDefinition={defaultExport}
            />
            <RenderImbriscriptTextSignature
                result={executeResult}
            />
            <DebugInformation
                information={JSON.stringify(executeResult.logs)}
            />
        </div>);
    } else {

        return (<RenderPending
            result={executeResult}
        />);
    }
};
