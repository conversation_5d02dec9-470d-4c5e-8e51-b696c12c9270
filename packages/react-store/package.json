{"name": "@imbricate-hummingbird/react-store", "description": "Store implementation for React", "private": true, "main": "src/index.ts", "scripts": {"test": "jest", "coverage": "jest --coverage", "compile": "tsc --project ./typescript/tsconfig.compile.json", "lint": "eslint src"}, "dependencies": {"@imbricate-hummingbird/configuration": "workspace:*", "@imbricate-hummingbird/logger": "workspace:*", "@imbricate-hummingbird/script-core": "workspace:*", "@imbricate-hummingbird/transfer-core": "workspace:*", "@reduxjs/toolkit": "2.8.2", "react": "catalog:"}, "devDependencies": {"@imbricate-hummingbird/config": "workspace:*", "@types/react": "catalog:", "eslint": "catalog:"}}