/**
 * <AUTHOR>
 * @namespace Origin_Views_OriginView
 * @description Origin View Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const OriginViewApplication = LazyLoadComponent(
    () => import("./origin-view"),
    "Origin View Application",
);

export const OriginViewWrapper: FC = () => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Origin View Application"
            fullHeight
        />}
    >
        <OriginViewApplication />
    </React.Suspense>);
};
