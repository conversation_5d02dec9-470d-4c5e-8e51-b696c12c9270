/**
 * <AUTHOR>
 * @package Monaco Core
 * @namespace Util
 * @description Simple Config
 */

import type * as monaco from "monaco-editor";
import { MONACO_REGULAR_SCROLL_BAR_CONFIG } from "../types/minimap";

export type GetMonacoSimpleConfigProps = {

    readonly initialValue: string;
    readonly language: string;
    readonly darkMode: boolean;

    readonly readonly?: boolean;
};

export const getMonacoSimpleConfig = (
    props: GetMonacoSimpleConfigProps,
): monaco.editor.IStandaloneEditorConstructionOptions => {

    return {
        value: props.initialValue,
        language: props.language,
        automaticLayout: true,
        wordWrap: "on",
        inlayHints: {
            enabled: "on",
            padding: true,
        },
        readOnly: props.readonly,
        wrappingIndent: "same",
        wordBasedSuggestions: "off",
        theme: props.darkMode ? "vs-dark" : "vs",
        ...MONACO_REGULAR_SCROLL_BAR_CONFIG,
    };
};
