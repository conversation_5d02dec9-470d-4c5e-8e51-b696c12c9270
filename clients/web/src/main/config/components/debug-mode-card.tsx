/**
 * <AUTHOR>
 * @namespace Config
 * @description Debug Mode Card
 */

import { StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody, UICardHeader, UIDivider, UISpacer, UISwitch } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { FaBug } from "react-icons/fa6";

export type DebugModeCardProps = {

    readonly isDebugMode: boolean;
    readonly setDebugMode: (isDebugMode: boolean) => void;
    readonly isDevelopmentMode: boolean;
};

export const DebugModeCard: FC<DebugModeCardProps> = (
    props: DebugModeCardProps,
) => {

    return (<StyledCard>
        <UICardHeader>
            <FaBug />
            <UISpacer />
            Debug Mode
        </UICardHeader>
        <UIDivider />
        <UICardBody>
            <UISwitch
                aria-label="Debug Mode"
                isDisabled={props.isDevelopmentMode}
                isSelected={props.isDebugMode}
                onSelectedChange={(newSelected: boolean) => {
                    props.setDebugMode(newSelected);
                }}
            >
                Debug Mode
            </UISwitch>
            {props.isDevelopmentMode && (
                <p className="text-small text-default-500">
                    Debug mode is always enabled in development mode.
                </p>
            )}
        </UICardBody>
    </StyledCard>);
};
