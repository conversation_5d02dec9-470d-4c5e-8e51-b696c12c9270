/**
 * <AUTHOR>
 * @namespace Recent_Components_RecentCard
 * @description Unknown Card
 */

import { DebugInformation } from "@/debug/components/debug-information";
import { RECENT_TYPE, RecentItem } from "@imbricate-hummingbird/configuration";
import { UICardBody } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { useRecentFormat } from "../../internationalization/hook";
import { RECENT_PROFILE } from "../../internationalization/profile";

export type RecentCardUnknownProps = {

    readonly item: RecentItem<RECENT_TYPE>;
};

export const RecentCardUnknown: FC<RecentCardUnknownProps> = (
    props: RecentCardUnknownProps,
) => {

    const recentFormat = useRecentFormat();

    return (<UICardBody>
        {recentFormat.get(RECENT_PROFILE.NOT_FOUND)}
        <DebugInformation
            information={JSON.stringify(props.item)}
        />
    </UICardBody>);
};
