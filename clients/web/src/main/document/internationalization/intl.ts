/**
 * <AUTHOR>
 * @namespace Document_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooLazyInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { DOCUMENT_PROFILE } from "./profile";

export const documentInternationalization: SudooLazyInternationalization<DOCUMENT_PROFILE> =
    SudooLazyInternationalization.create<DOCUMENT_PROFILE>(
        DEFAULT_LOCALE,
    );

documentInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/en-US").then(
        (module) => module.enUSDocumentProfile,
    ),
);

documentInternationalization.mergeLazyProfile(
    IETF_LOCALE.JAPANESE_JAPAN,
    import("./locale/ja-JP").then(
        (module) => module.jaJPDocumentProfile,
    ),
);

documentInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    import("./locale/zh-CN").then(
        (module) => module.zhCNDocumentProfile,
    ),
);
