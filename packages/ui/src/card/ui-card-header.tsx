/**
 * <AUTHOR>
 * @package UI
 * @namespace Card
 * @description Card Header
 */

import { CardHeader } from "@heroui/card";
import type { FC, ReactNode } from "react";

export type UICardHeaderProps = {

    readonly className?: string;

    readonly children?: ReactNode;
};

export const UICardHeader: FC<UICardHeaderProps> = (
    props: UICardHeaderProps,
) => {

    return (<CardHeader
        className={props.className}
    >
        {props.children}
    </CardHeader>);
};
