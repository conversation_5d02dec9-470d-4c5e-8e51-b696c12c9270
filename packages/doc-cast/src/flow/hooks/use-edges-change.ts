/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Flow_Hooks
 * @description Use Edges Change
 */

import { Edge, EdgeChange } from "@xyflow/react";
import { useCallback } from "react";
import { applyDocCastFlowEdgeChanges } from "../util/apply-edge-changes";

export const useDocCastFlowEdgesChange = (
    setParsedEdges: React.Dispatch<React.SetStateAction<Edge[]>>,
): (changes: EdgeChange[]) => void => {

    const onEdgesChange = useCallback((
        changes: EdgeChange[],
    ) => {

        setParsedEdges((
            previousEdges: Edge[],
        ) => {

            return applyDocCastFlowEdgeChanges(
                previousEdges,
                changes,
            );
        });
    }, []);

    return onEdgesChange;
};
