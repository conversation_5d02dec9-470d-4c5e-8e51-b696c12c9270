/**
 * <AUTHOR>
 * @namespace Origin_Components
 * @description Origin Selector
 */

import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { openErrorToaster } from "@imbricate-hummingbird/react-components";
import { UISingleSelect } from "@imbricate-hummingbird/ui";
import { IMBRICATE_ORIGIN_FEATURE, checkImbricateOriginFeatureSupported } from "@imbricate/core";
import { FC } from "react";
import { useOldOrigins } from "../hooks/use-origins";

export type OriginSelectorProps = {

    readonly selectedOrigin: string | null;
    readonly onSelectOrigin: (origin: ImbricateOriginObject) => void;
};

export const OriginSelector: FC<OriginSelectorProps> = (
    props: OriginSelectorProps,
) => {

    const origins = useOldOrigins();

    return (<UISingleSelect
        ariaLabel="origin-selector"
        selectedKey={props.selectedOrigin ?? undefined}
        onSelectedKeyChange={(
            newSelectKey: string,
        ) => {

            const targetOrigin = origins.find((
                origin: ImbricateOriginObject,
            ) => {

                return origin.origin.uniqueIdentifier === newSelectKey;
            });

            if (!targetOrigin) {

                openErrorToaster("Origin not found");
                return;
            }

            props.onSelectOrigin(targetOrigin);
        }}
        items={origins.map((origin: ImbricateOriginObject) => {

            const isSupported = checkImbricateOriginFeatureSupported(
                origin.origin.supportedFeatures,
                IMBRICATE_ORIGIN_FEATURE.ORIGIN_STATIC_MANAGER,
            );

            return {
                itemKey: origin.origin.uniqueIdentifier,
                isDisabled: !isSupported,
                content: origin.originName,
            };
        })}
    />);
};
