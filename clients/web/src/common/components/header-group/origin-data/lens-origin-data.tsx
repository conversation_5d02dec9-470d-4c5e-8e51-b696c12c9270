/**
 * <AUTHOR>
 * @namespace Common_Components_HeaderGroup_OriginData
 * @description Lens Origin Data
 */

import { COMMON_PROFILE } from "@/common/internationalization/profile";
import { FormattedText } from "@imbricate-hummingbird/react-components";
import { <PERSON><PERSON><PERSON>, UICardBody, UICardHeader, UIDivider } from "@imbricate-hummingbird/ui";
import React from "react";
import { FaCopy } from "react-icons/fa";
import { getHeaderGroupTitle } from "../group";
import { HEADER_GROUP_GROUP, HeaderGroupPropsDataOriginProps } from "../types";
import { OriginDataSnippet } from "./origin-snippets";

export const LensGroupOriginData: React.FC<HeaderGroupPropsDataOriginProps<HEADER_GROUP_GROUP.LENS>> = (
    props: HeaderGroupPropsDataOriginProps<HEADER_GROUP_GROUP.LENS>,
) => {

    return (<UICard>
        <UICardHeader
            className="p-2 gap-2"
        >
            <FaCopy />
            <FormattedText
                usedFormats={[props.commonFormat]}
            >
                {getHeaderGroupTitle(props.group, props.commonFormat)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="p-2"
        >
            <OriginDataSnippet
                commonFormat={props.commonFormat}
                combinations={{
                    [COMMON_PROFILE.LENS_IDENTIFIER]: {
                        content: props.payload.lensIdentifier,
                        color: "primary",
                    },
                }}
            />
        </UICardBody>
    </UICard>);
};
