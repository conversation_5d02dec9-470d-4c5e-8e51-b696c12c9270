/**
 * <AUTHOR>
 * @namespace Origin_Components_OriginNewTypes
 * @description Stack API Origin
 */

import { OriginInstanceStackAPIOrigin } from "@imbricate-hummingbird/transfer-core";
import { UIInput, UISingleSelect } from "@imbricate-hummingbird/ui";
import { FC } from "react";

export type NewOriginStackAPIOriginProps = {

    readonly originInstance: OriginInstanceStackAPIOrigin;
    readonly onOriginChange: (originInstance: OriginInstanceStackAPIOrigin) => void;
};

export const NewOriginStackAPIOrigin: FC<NewOriginStackAPIOriginProps> = (
    props: NewOriginStackAPIOriginProps,
) => {

    return (<div
        className="flex flex-col gap-2"
    >
        <UIInput
            label="Origin Name"
            value={props.originInstance.originName}
            onValueChange={(
                newValue: string,
            ) => {
                props.onOriginChange({
                    ...props.originInstance,
                    originName: newValue,
                });
            }}
        />
        <UIInput
            label="Base Path"
            value={props.originInstance.basePath}
            onValueChange={(
                newValue: string,
            ) => {
                props.onOriginChange({
                    ...props.originInstance,
                    basePath: newValue,
                });
            }}
        />
        <UISingleSelect
            ariaLabel="authorization-type-selector"
            label="Authorization Type"
            defaultSelectedKey={props.originInstance.authentication.type}
            onSelectedKeyChange={(
                newSelectedKey: string,
            ) => {

                props.onOriginChange({
                    ...props.originInstance,
                    authentication: {
                        ...props.originInstance.authentication,
                        type: newSelectedKey as any,
                    },
                });
            }}
            items={[
                {
                    itemKey: "Basic",
                    content: "Basic",
                },
                {
                    itemKey: "Bearer",
                    content: "Bearer",
                },
            ]}
        />
        <UIInput
            label="Authorization Value"
            value={props.originInstance.authentication.value}
            onValueChange={(
                newValue: string,
            ) => {
                props.onOriginChange({
                    ...props.originInstance,
                    authentication: {
                        ...props.originInstance.authentication,
                        value: newValue,
                    },
                });
            }}
        />
    </div>);
};
