/**
 * <AUTHOR>
 * @namespace Database_Internationalization_Locale
 * @description En-US
 */

import { DATABASE_PROFILE } from "../profile";

export const enUSDatabaseProfile: Record<DATABASE_PROFILE, string> = {

    [DATABASE_PROFILE.ACTIONS]: "Actions",
    [DATABASE_PROFILE.ADD_PROPERTY]: "Add Property",
    [DATABASE_PROFILE.ALL]: "All",
    [DATABASE_PROFILE.ALLOW_MULTIPLE]: "Allow Multiple",
    [DATABASE_PROFILE.BINARY]: "Binary",
    [DATABASE_PROFILE.BOOLEAN]: "Boolean",
    [DATABASE_PROFILE.CREATE_DOCUMENT]: "Create Document",
    [DATABASE_PROFILE.CREATE_DOCUMENT_DESCRIPTION]: "Create a new document",
    [DATABASE_PROFILE.DATABASE]: "Database",
    [DATABASE_PROFILE.DATABASE_SCHEMA]: "Database Schema",
    [DATABASE_PROFILE.DATE]: "Date",
    [DATABASE_PROFILE.DISPLAY_ALL]: "Display All",
    [DATABASE_PROFILE.DISPLAY_ALL_DESCRIPTION]: "Display all properties",
    [DATABASE_PROFILE.DOCUMENT]: "Document",
    [DATABASE_PROFILE.DOCUMENT_LIST]: "Document List",
    [DATABASE_PROFILE.EDIT_DATABASE_SCHEMA]: "Edit Database Schema",
    [DATABASE_PROFILE.EDIT_DATABASE_SCHEMA_DESCRIPTION]: "Navigate to the database schema editor",
    [DATABASE_PROFILE.FLOATING_PROPERTIES]: "Floating Properties",
    [DATABASE_PROFILE.FLOATING_PROPERTY_DESCRIPTION]: "Floating properties are not defined in the database schema",
    [DATABASE_PROFILE.JSON]: "JSON",
    [DATABASE_PROFILE.IMBRISCRIPT]: "ImbriScript",
    [DATABASE_PROFILE.ITEM_$1_PER_PAGE]: "{} Documents / Page",
    [DATABASE_PROFILE.ITEM_PER_PAGE_FOR_THIS_DATABASE]: "Items per page for this database",
    [DATABASE_PROFILE.LABEL]: "Label",
    [DATABASE_PROFILE.LABEL_COLOR]: "Label Color",
    [DATABASE_PROFILE.LABEL_NAME]: "Label Name",
    [DATABASE_PROFILE.MARKDOWN]: "Markdown",
    [DATABASE_PROFILE.NUMBER]: "Number",
    [DATABASE_PROFILE.REFRESH_DOCUMENT_LIST]: "Refresh Document List",
    [DATABASE_PROFILE.REFRESH_DOCUMENT_LIST_DESCRIPTION]: "Invalid cache and refresh the document list",
    [DATABASE_PROFILE.PRIMARY_KEY]: "Primary Key",
    [DATABASE_PROFILE.PROPERTIES_FOR_THIS_DATABASE]: "Properties for this database",
    [DATABASE_PROFILE.PROPERTY_NAME]: "Property Name",
    [DATABASE_PROFILE.PROPERTY_TYPE]: "Property Type",
    [DATABASE_PROFILE.PROPERTY_UNIQUE_IDENTIFIER]: "Property Unique Identifier",
    [DATABASE_PROFILE.REFERENCE]: "Reference",
    [DATABASE_PROFILE.SAVE]: "Save",
    [DATABASE_PROFILE.SELECT_DATABASE]: "Select Database",
    [DATABASE_PROFILE.STRING]: "String",
    [DATABASE_PROFILE.VIEW_DOCUMENTS]: "View Documents",
    [DATABASE_PROFILE.VIEW_DOCUMENTS_DESCRIPTION]: "Navigate to the document list view",
};
