/**
 * <AUTHOR>
 * @namespace Config_Panels_Wrapper
 * @description ImbriScript Panel Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const ConfigImbriScriptPanel = LazyLoadComponent(
    () => import("../panels/imbriscript-panel"),
    "Config ImbriScript Panel",
);

export type ConfigImbriScriptPanelWrapperProps = {
};

export const ConfigImbriScriptPanelWrapper: FC<ConfigImbriScriptPanelWrapperProps> = (
    _props: ConfigImbriScriptPanelWrapperProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Config ImbriScript Panel"
            fullHeight
        />}
    >
        <ConfigImbriScriptPanel />
    </React.Suspense>);
};
