/**
 * <AUTHOR>
 * @namespace Internationalization_Internationalization_Locale
 * @description Ja-JP
 */

import { IETF_LOCALE } from "@sudoo/locale";

// spell-checker: disable   
export const jaJPInternationalizationProfile: Record<IETF_LOCALE, string> = {

    [IETF_LOCALE.CHINESE_SIMPLIFIED]: "簡体中文",
    [IETF_LOCALE.CHINESE_TRADITIONAL]: "繁體中文",
    [IETF_LOCALE.ENGLISH_CANADA]: "英語 (カナダ)",
    [IETF_LOCALE.ENGLISH_UNITED_KINGDOM]: "英語 (英国)",
    [IETF_LOCALE.ENGLISH_UNITED_STATES]: "英語 (米国)",
    [IETF_LOCALE.FRENCH_CANADA]: "フランス語 (カナダ)",
    [IETF_LOCALE.FRENCH_FRANCE]: "フランス語 (フランス)",
    [IETF_LOCALE.JAPANESE_JAPAN]: "日本語",
    [IETF_LOCALE.KOREAN_KOREA]: "韓国語",
    [IETF_LOCALE.MAORI_NEW_ZEALAND]: "マオリ語",
    [IETF_LOCALE.RUSSIAN_RUSSIA]: "ロシア語",
    [IETF_LOCALE.SPANISH_MEXICO]: "スペイン語 (メキシコ)",
    [IETF_LOCALE.SPANISH_SPAIN]: "スペイン語 (スペイン)",
};
// spell-checker: enable
