/**
 * <AUTHOR>
 * @namespace Database_Internationalization_Locale
 * @description Zh-CN
 */

import { DATABASE_PROFILE } from "../profile";

export const zhCNDatabaseProfile: Record<DATABASE_PROFILE, string> = {

    [DATABASE_PROFILE.ACTIONS]: "操作",
    [DATABASE_PROFILE.ADD_PROPERTY]: "添加属性",
    [DATABASE_PROFILE.ALL]: "全部",
    [DATABASE_PROFILE.ALLOW_MULTIPLE]: "允许多个",
    [DATABASE_PROFILE.BINARY]: "二进制",
    [DATABASE_PROFILE.BOOLEAN]: "布尔值",
    [DATABASE_PROFILE.CREATE_DOCUMENT]: "创建文档",
    [DATABASE_PROFILE.CREATE_DOCUMENT_DESCRIPTION]: "创建一个新的文档",
    [DATABASE_PROFILE.DATABASE]: "数据库",
    [DATABASE_PROFILE.DATABASE_SCHEMA]: "数据类型",
    [DATABASE_PROFILE.DATE]: "日期",
    [DATABASE_PROFILE.DISPLAY_ALL]: "全部显示",
    [DATABASE_PROFILE.DISPLAY_ALL_DESCRIPTION]: "显示所有属性",
    [DATABASE_PROFILE.DOCUMENT]: "文档",
    [DATABASE_PROFILE.DOCUMENT_LIST]: "文档列表",
    [DATABASE_PROFILE.EDIT_DATABASE_SCHEMA]: "编辑数据类型",
    [DATABASE_PROFILE.EDIT_DATABASE_SCHEMA_DESCRIPTION]: "导航到数据类型编辑器",
    [DATABASE_PROFILE.FLOATING_PROPERTIES]: "浮动属性",
    [DATABASE_PROFILE.FLOATING_PROPERTY_DESCRIPTION]: "浮动属性未在数据类型中定义",
    [DATABASE_PROFILE.JSON]: "JSON",
    [DATABASE_PROFILE.IMBRISCRIPT]: "ImbriScript",
    [DATABASE_PROFILE.ITEM_$1_PER_PAGE]: "每页 {} 个文档",
    [DATABASE_PROFILE.ITEM_PER_PAGE_FOR_THIS_DATABASE]: "此数据库每页的文档数量",
    [DATABASE_PROFILE.LABEL]: "标签",
    [DATABASE_PROFILE.LABEL_COLOR]: "标签颜色",
    [DATABASE_PROFILE.LABEL_NAME]: "标签名称",
    [DATABASE_PROFILE.MARKDOWN]: "Markdown",
    [DATABASE_PROFILE.NUMBER]: "数字",
    [DATABASE_PROFILE.REFRESH_DOCUMENT_LIST]: "刷新文档列表",
    [DATABASE_PROFILE.REFRESH_DOCUMENT_LIST_DESCRIPTION]: "清除缓存并刷新文档列表",
    [DATABASE_PROFILE.PRIMARY_KEY]: "主键",
    [DATABASE_PROFILE.PROPERTIES_FOR_THIS_DATABASE]: "此数据库的属性",
    [DATABASE_PROFILE.PROPERTY_NAME]: "属性名称",
    [DATABASE_PROFILE.PROPERTY_TYPE]: "属性类型",
    [DATABASE_PROFILE.PROPERTY_UNIQUE_IDENTIFIER]: "属性唯一标识符",
    [DATABASE_PROFILE.REFERENCE]: "参考",
    [DATABASE_PROFILE.SAVE]: "保存",
    [DATABASE_PROFILE.SELECT_DATABASE]: "选择数据库",
    [DATABASE_PROFILE.STRING]: "字符串",
    [DATABASE_PROFILE.VIEW_DOCUMENTS]: "查看文档",
    [DATABASE_PROFILE.VIEW_DOCUMENTS_DESCRIPTION]: "导航到文档列表视图",
};
