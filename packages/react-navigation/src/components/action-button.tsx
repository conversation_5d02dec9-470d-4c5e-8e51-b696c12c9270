/**
 * <AUTHOR>
 * @namespace Navigation_Components
 * @description Action Button
 */

import { HUMMINGBIRD_PREFERENCE_ITEM, useDynamicPreferenceItem } from "@imbricate-hummingbird/configuration";
import { UIButtonDiv, UIButtonGroup, UIButtonLink, UIColor, UISize } from "@imbricate-hummingbird/ui";
import clsx from "clsx";
import React, { FC } from "react";
import { IconType } from "react-icons/lib";
import { useLocation } from "react-router-dom";

export type NavigationActionButtonProps = {

    readonly Icon?: IconType;

    readonly title: string;
    readonly description?: React.ReactNode;

    readonly onPress?: () => void;

    readonly href?: string;
    readonly hrefInNewTab?: boolean;

    readonly color?: UIColor;
    readonly size?: UISize;

    readonly endingButtonIcon?: IconType;
    readonly endingButtonColor?: UIColor;
    readonly onEndingButtonPress?: () => void;
};

export const NavigationActionButton: FC<NavigationActionButtonProps> = (
    props: NavigationActionButtonProps,
) => {

    const location = useLocation();

    const active = location.pathname === props.href;

    const navigationSubActionEnablement = useDynamicPreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.NAVIGATION_SUB_ACTION_ENABLEMENT,
    );

    return (<UIButtonGroup>
        <UIButtonLink
            href={props.href}
            hrefInNewTab={props.hrefInNewTab}
            isFullWidth
            variant={active ? "flat" : "light"}
            color={active ? "primary" : props.color}
            size={props.size}
            startContent={props.Icon ? <props.Icon
                className="text-large"
            /> : undefined}
            onPress={props.onPress}
            className="flex h-14 items-center justify-start px-3"
        >
            <div
                className="text-start flex-1"
            >
                <div>
                    {props.title}
                </div>
                {props.description && <div
                    className={clsx(
                        "text-tiny",
                        active && "text-primary-500 dark:text-primary-500",
                        !active && "text-default-500 dark:text-default-500",
                    )}
                >
                    {props.description}
                </div>}
            </div>
        </UIButtonLink>
        {props.endingButtonIcon && navigationSubActionEnablement.preference && <UIButtonDiv
            aria-label={`Open ${props.title} in drawer`}
            isIconOnly
            className="h-14"
            variant="light"
            color={props.endingButtonColor}
            onPress={props.onEndingButtonPress}
        >
            <props.endingButtonIcon
                className="text-large"
            />
        </UIButtonDiv>}
    </UIButtonGroup>);
};
