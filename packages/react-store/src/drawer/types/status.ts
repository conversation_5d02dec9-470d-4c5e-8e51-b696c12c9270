/**
 * <AUTHOR>
 * @package React Store
 * @namespace Drawer_Types
 * @description Status
 */

import { ImbriScriptExecuteContext } from "@imbricate-hummingbird/script-core";

export enum DRAWER_TYPE {

    DOCUMENT_VIEW = "DOCUMENT_VIEW",
    IMBRISCRIPT_EXECUTE = "IMBRISCRIPT_EXECUTE",
    IMBRISCRIPT_LENS_TEXT_PEEK = "IMBRISCRIPT_LENS_TEXT_PEEK",
    LENS_VIEW = "LENS_VIEW",
    MARKDOWN_TEXT_PEEK = "MARKDOWN_TEXT_PEEK",
    TEXT_RAW_PEEK = "TEXT_RAW_PEEK",
}

export type DrawerStatusPayload<T extends DRAWER_TYPE> =
    T extends DRAWER_TYPE.DOCUMENT_VIEW ? DrawerStatusPayloadDocumentView :
    T extends DRAWER_TYPE.IMBRISCRIPT_LENS_TEXT_PEEK ? DrawerStatusPayloadImbriscriptLensTextPeek :
    T extends DRAWER_TYPE.IMBRISCRIPT_EXECUTE ? DrawerStatusPayloadImbriscriptExecute :
    T extends DRAWER_TYPE.LENS_VIEW ? DrawerStatusPayloadLensView :
    T extends DRAWER_TYPE.MARKDOWN_TEXT_PEEK ? DrawerStatusPayloadMarkdownTextPeek :
    T extends DRAWER_TYPE.TEXT_RAW_PEEK ? DrawerStatusPayloadTextRawPeek :
    never;

export type DrawerStatusPayloadDocumentView = {

    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;
};

export type DrawerStatusPayloadImbriscriptLensTextPeek = {

    readonly imbriscriptText: string;

    readonly context: ImbriScriptExecuteContext;
};

export type DrawerStatusPayloadImbriscriptExecute = {

    readonly imbriscriptText: string;

    readonly context: ImbriScriptExecuteContext;
};

export type DrawerStatusPayloadLensView = {

    readonly lensIdentifier: string;
};

export type DrawerStatusPayloadMarkdownTextPeek = {

    readonly markdownText: string;
};

export type DrawerStatusPayloadTextRawPeek = {

    readonly originUniqueIdentifier: string;
    readonly textUniqueIdentifier: string;
};

export type DrawerStatus<T extends DRAWER_TYPE> = {

    readonly title: string;
    readonly fullScreenLink?: string;

    readonly type: T;
    readonly payload: DrawerStatusPayload<T>;

    readonly zIndex?: number;
};
