/**
 * <AUTHOR>
 * @package React Origin Central
 * @namespace Actions
 * @description Query Documents Recursive
 */

import { ActionCentral, ActionDescription } from "@imbricate-hummingbird/interceptor-core";
import { CentralQueryDocumentsResponse, IOriginCentral } from "@imbricate-hummingbird/origin-central";
import { ImbricateDocumentQuery } from "@imbricate/core";

export const executeQueryDocumentsRecursive = async (
    originCentral: IOriginCentral,
    databaseUniqueIdentifier: string,
    query: ImbricateDocumentQuery,
    actionDescription: ActionDescription,
): Promise<CentralQueryDocumentsResponse> => {

    const documents: CentralQueryDocumentsResponse =
        await ActionCentral.getInstance().executeAction(
            async (
                actionIdentifier: string,
                recordIdentifier: string,
            ) => {
                return originCentral.queryDocumentsRecursive(
                    actionIdentifier,
                    recordIdentifier,
                    databaseUniqueIdentifier,
                    query,
                );
            },
            actionDescription,
        );

    return documents;
};
