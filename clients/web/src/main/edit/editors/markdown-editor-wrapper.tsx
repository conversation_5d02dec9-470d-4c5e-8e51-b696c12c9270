/**
 * <AUTHOR>
 * @namespace Edit_Editors
 * @description Markdown Editor Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import type { EditMarkdownEditorProps } from "./markdown-editor";

const MarkdownEditorApplication = LazyLoadComponent(
    () => import("./markdown-editor"),
    "Markdown Editor Application",
);

export const MarkdownEditorWrapper: FC<EditMarkdownEditorProps> = (
    props: EditMarkdownEditorProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Markdown Editor Application"
            fullHeight
        />}
    >
        <MarkdownEditorApplication
            {...props}
        />
    </React.Suspense>);
};
