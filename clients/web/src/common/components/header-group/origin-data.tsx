/**
 * <AUTHOR>
 * @namespace Common_Components_HeaderGroup
 * @description Origin Data
 */

import { DatabaseGroupOriginData } from "./origin-data/database-origin-data";
import { DocumentGroupOriginData } from "./origin-data/document-origin-data";
import { LensGroupOriginData } from "./origin-data/lens-origin-data";
import { PropertyGroupOriginData } from "./origin-data/property-origin-data";
import { HEADER_GROUP_GROUP, HeaderGroupPropsDataOriginProps } from "./types";

export const getHeaderGroupOriginData = (
    props: HeaderGroupPropsDataOriginProps<HEADER_GROUP_GROUP>,
) => {

    // HEADER_GROUP_GROUP SWITCH
    switch (props.group) {

        case HEADER_GROUP_GROUP.DATABASE: {

            const fixedProps =
                props as HeaderGroupPropsDataOriginProps<HEADER_GROUP_GROUP.DATABASE>;

            return (<DatabaseGroupOriginData
                {...fixedProps}
            />);
        }
        case HEADER_GROUP_GROUP.DOCUMENT: {

            const fixedProps =
                props as HeaderGroupPropsDataOriginProps<HEADER_GROUP_GROUP.DOCUMENT>;

            return (<DocumentGroupOriginData
                {...fixedProps}
            />);
        }
        case HEADER_GROUP_GROUP.PROPERTY: {

            const fixedProps =
                props as HeaderGroupPropsDataOriginProps<HEADER_GROUP_GROUP.PROPERTY>;

            return (<PropertyGroupOriginData
                {...fixedProps}
            />);
        }
        case HEADER_GROUP_GROUP.LENS: {

            const fixedProps =
                props as HeaderGroupPropsDataOriginProps<HEADER_GROUP_GROUP.LENS>;

            return (<LensGroupOriginData
                {...fixedProps}
            />);
        }
    }
};
