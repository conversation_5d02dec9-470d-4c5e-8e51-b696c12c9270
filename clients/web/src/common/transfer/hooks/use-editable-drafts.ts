/**
 * <AUTHOR>
 * @namespace Common_Transfer_Hooks
 * @description Use Editable Drafts
 */

import { rootLogger } from "@/main/log/logger";
import { TransferDocument, TransferDocumentProperty, TransferPropertyDraft, mergePropertyDrafts } from "@imbricate-hummingbird/transfer-core";
import { CommonOutcomeSymbol, IMBRICATE_PROPERTY_TYPE, ImbricateDocumentGetPropertiesOutcomeSymbol, ImbricatePropertyKey } from "@imbricate/core";
import { useCallback, useMemo, useState } from "react";

const logger = rootLogger.fork({
    scopes: [
        "Transfer",
        "Hooks",
        "UseEditableDrafts",
    ],
});

export type UseEditableDraftsResult = {

    originalDrafts: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[];
    editedDrafts: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[];
    deletedDrafts: ImbricatePropertyKey[];

    mergedDrafts: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[];
    mergedDraftRecord: Record<ImbricatePropertyKey, TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>>;

    setEditedDrafts: (drafts: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[]) => void;
    addEditedDraft: (draft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>) => void;
    deleteDraft: (key: ImbricatePropertyKey) => void;

    resetEdits: () => void;
} | CommonOutcomeSymbol | ImbricateDocumentGetPropertiesOutcomeSymbol;

export const useEditableDrafts = (
    document: TransferDocument,
): UseEditableDraftsResult => {

    const documentProperties = useMemo(() => {

        const properties: TransferDocumentProperty[] = document.properties;

        if (typeof properties === "symbol") {
            logger.error(`No properties found for [${document.documentUniqueIdentifier}]`);
        }

        return properties;
    }, [document]);

    const [originalPropertyDrafts] =
        useState<TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[]>(() => {

            return documentProperties.map((
                property: TransferDocumentProperty,
            ) => {

                return {
                    key: property.propertyKey,
                    type: property.propertyType,
                    value: property.propertyValue,
                    variant: property.propertyVariant,
                };
            });
        });

    const [editedPropertyDrafts, setEditedPropertyDrafts] =
        useState<TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[]>([]);

    const [deletedDrafts, setDeletedDrafts] = useState<ImbricatePropertyKey[]>([]);

    const deleteDraft = useCallback((
        key: ImbricatePropertyKey,
    ) => {

        setDeletedDrafts([...deletedDrafts, key]);
    }, [deletedDrafts]);

    const addEditedDraft = useCallback((
        draft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>,
    ) => {

        const existingDraft = editedPropertyDrafts.find((
            existingDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>,
        ) => {
            return existingDraft.key === draft.key;
        });

        if (!existingDraft) {
            setEditedPropertyDrafts([...editedPropertyDrafts, draft]);
            return;
        }

        setEditedPropertyDrafts(editedPropertyDrafts.map((
            existingDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>,
        ) => {
            return existingDraft.key === draft.key
                ? draft
                : existingDraft;
        }));
    }, [editedPropertyDrafts]);

    const resetEdits = useCallback(() => {
        setEditedPropertyDrafts([]);
        setDeletedDrafts([]);
    }, []);

    if (typeof documentProperties === "symbol") {
        return documentProperties;
    }

    const mergedPropertyDrafts: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[] = mergePropertyDrafts(
        originalPropertyDrafts,
        editedPropertyDrafts,
        deletedDrafts,
    );

    const mergedDraftRecord: Record<ImbricatePropertyKey, TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>>
        = {};
    for (const draft of mergedPropertyDrafts) {
        mergedDraftRecord[draft.key] = draft;
    }

    return {

        originalDrafts: originalPropertyDrafts,
        editedDrafts: editedPropertyDrafts,
        deletedDrafts: deletedDrafts,

        mergedDrafts: mergedPropertyDrafts,
        mergedDraftRecord,

        setEditedDrafts: setEditedPropertyDrafts,
        addEditedDraft,
        deleteDraft,

        resetEdits,
    };
};
