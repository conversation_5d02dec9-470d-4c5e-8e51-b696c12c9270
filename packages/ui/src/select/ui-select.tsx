/**
 * <AUTHOR>
 * @package UI
 * @namespace Select
 * @description Select
 */

import { Select, SelectItem, SelectedItems } from "@heroui/select";
import { SharedSelection } from "@heroui/system";
import type { FC } from "react";
import { UISelectCommonProps, UISelectItemData, UISelectSelectionMode } from "./types";
import { parseUISelectSelectedItems } from "./util";

export type UISelectProps = {

    readonly selectionMode?: UISelectSelectionMode;

    readonly defaultSelectedKeys?: string[];
    readonly selectedKeys?: string[];
    readonly onSelectedKeysChange?: (newSelectedKeys: string[]) => void;
} & UISelectCommonProps;

export const UISelect: FC<UISelectProps> = (
    props: UISelectProps,
) => {

    return (
        <Select
            aria-label={props.ariaLabel}
            selectionMode={props.selectionMode ?? "single"}

            label={props.label}
            startContent={props.startContent}
            endContent={props.endContent}
            errorMessage={props.errorMessage}

            className={props.className}

            size={props.size}
            color={props.color}

            fullWidth={props.isFullWidth}
            isDisabled={props.isDisabled}
            isLoading={props.isLoading}
            isInvalid={props.isInvalid}

            renderValue={typeof props.renderValue === "function"
                ? (value: SelectedItems) => {

                    return parseUISelectSelectedItems(
                        value,
                        props.renderValue!,
                    );
                }
                : undefined}

            defaultSelectedKeys={Array.isArray(props.defaultSelectedKeys)
                ? new Set(props.defaultSelectedKeys)
                : undefined}
            selectedKeys={Array.isArray(props.selectedKeys)
                ? new Set(props.selectedKeys)
                : undefined}
            onSelectionChange={typeof props.onSelectedKeysChange === "function"
                ? (newSelection: SharedSelection) => {
                    const newValue: string[] = Array.from(newSelection) as string[];
                    props.onSelectedKeysChange!(newValue);
                }
                : undefined}
        >
            {props.items.map((
                item: UISelectItemData,
            ) => {

                return (<SelectItem
                    key={item.itemKey}
                    className={item.className}
                    isDisabled={item.isDisabled}
                    textValue={item.textValue}
                    color={item.color}
                    variant={item.variant}
                    startContent={item.startContent}
                    endContent={item.endContent}
                >
                    {item.content}
                </SelectItem>);
            })}
        </Select>
    );
};
