/**
 * <AUTHOR>
 * @namespace Config_Configuration_Import
 * @description Import Card
 */

import { StyledCard, openErrorToaster } from "@imbricate-hummingbird/react-components";
import { useReloadApplication } from "@imbricate-hummingbird/react-navigation";
import { UIButton, UICardBody, UICardFooter, UICardHeader, UICheckboxGroup, UIDivider } from "@imbricate-hummingbird/ui";
import React, { FC, useCallback } from "react";
import { FaCheck, FaFileImport } from "react-icons/fa";
import { ExportableConfiguration } from "../../types/configuration";
import { writeConfigurationStorage } from "../../util/write-configuration";
import { ConfigConfigurationModalImport } from "./modal-import";

export type ConfigConfigurationImportCardProps = {
};

export const ConfigConfigurationImportCard: FC<ConfigConfigurationImportCardProps> = (
    _props: ConfigConfigurationImportCardProps,
) => {

    const reloadApplication = useReloadApplication();

    const [importObjects, setImportObjects] = React.useState<string[]>([]);
    const [importConfiguration, setImportConfiguration] = React.useState<ExportableConfiguration | null>(null);

    const onImportConfiguration = useCallback((
        configuration: ExportableConfiguration,
    ) => {
        setImportConfiguration(configuration);
    }, []);

    return (<React.Fragment>
        <StyledCard>
            <UICardHeader>
                Import Configuration
            </UICardHeader>
            <UIDivider />
            <UICardBody>
                <UICheckboxGroup
                    className="mt-2"
                    label="Import..."
                    selectedValues={importObjects}
                    onSelectedValuesChange={(
                        newValues: string[],
                    ) => {
                        setImportObjects(newValues);
                    }}
                    items={[
                        {
                            itemKey: "lenses",
                            itemContent: "Lenses",
                        },
                        {
                            itemKey: "origins",
                            itemContent: "Origins",
                        },
                    ]}
                />
            </UICardBody>
            {importObjects.length > 0 && (<React.Fragment>
                <UIDivider />
                <UICardFooter
                    className="flex gap-2 flex-wrap"
                >
                    <ConfigConfigurationModalImport
                        onImportConfiguration={onImportConfiguration}
                    />
                    <UIButton
                        startContent={<FaFileImport />}
                        isDisabled
                        variant="flat"
                        color="primary"
                    >
                        Import from Configuration File
                    </UIButton>
                </UICardFooter>
            </React.Fragment>)}
        </StyledCard>
        {importConfiguration && (<StyledCard>
            <UICardHeader>
                Configuration To Import
            </UICardHeader>
            <UIDivider />
            <UICardBody>
                <p>
                    Identifier: {importConfiguration.identifier}
                </p>
                <p>
                    Exported At: {new Date(importConfiguration.exportedAt).toLocaleString()}
                </p>
            </UICardBody>
            <UIDivider />
            <UICardFooter>
                <UIButton
                    startContent={<FaCheck />}
                    variant="flat"
                    color="primary"
                    onPress={async () => {
                        const importResult = await writeConfigurationStorage(importConfiguration);

                        if (!importResult) {

                            openErrorToaster("Failed to import configuration");
                            return;
                        }
                        reloadApplication();
                    }}
                >
                    Import Configuration
                </UIButton>
            </UICardFooter>
        </StyledCard>)}
    </React.Fragment>);
};
