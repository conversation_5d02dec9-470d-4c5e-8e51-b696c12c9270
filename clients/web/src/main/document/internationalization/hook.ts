/**
 * <AUTHOR>
 * @namespace Document_Internationalization
 * @description Hook
 */

import { defaultEmptyFormat, useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { useEffect, useState } from "react";
import { documentInternationalization } from "./intl";
import { DOCUMENT_PROFILE } from "./profile";

export const useDocumentFormat = (): SudooFormat<DOCUMENT_PROFILE> => {

    const locale = useLocale();

    const [format, setFormat] = useState<SudooFormat<DOCUMENT_PROFILE>>(defaultEmptyFormat);

    useEffect(() => {

        const execute = async () => {

            const format = await documentInternationalization.format(locale);
            setFormat(format);
        };

        execute();
    }, [locale]);

    return format;
};
