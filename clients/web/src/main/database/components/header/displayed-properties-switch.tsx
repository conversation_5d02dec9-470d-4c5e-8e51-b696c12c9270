/**
 * <AUTHOR>
 * @namespace Database_Components_Header
 * @description Displayed Properties Switch
 */

import { isDefaultEmptyFormat } from "@imbricate-hummingbird/internationalization";
import { getPropertyIcon, getUnknownPropertyIcon } from "@imbricate-hummingbird/react-components";
import { UIDropdown, createUIButton } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchema, ImbricateDatabaseSchemaProperty } from "@imbricate/core";
import clsx from "clsx";
import { FC, useMemo } from "react";
import { FaTableColumns } from "react-icons/fa6";
import { IoIosDoneAll } from "react-icons/io";
import { useDatabaseFormat } from "../../internationalization/hook";
import { DATABASE_PROFILE } from "../../internationalization/profile";

const ACTION_DISPLAY_ALL_KEY: string = "$_action_display-all";

export type DatabaseHeaderItemDisplayedPropertiesSwitchProps = {

    readonly databaseSchema: ImbricateDatabaseSchema;

    readonly displayedProperties: string[];
    readonly onDisplayedPropertiesChange: (
        displayedProperties: string[],
    ) => void;
};

export const DatabaseHeaderItemDisplayedPropertiesSwitch: FC<DatabaseHeaderItemDisplayedPropertiesSwitchProps> = (
    props: DatabaseHeaderItemDisplayedPropertiesSwitchProps,
) => {

    const databaseFormat = useDatabaseFormat();

    const selectedKeys: Set<string> = useMemo(() => {

        return new Set(props.displayedProperties);
    }, [props.displayedProperties]);

    const floatingKeys: string[] = useMemo(() => {

        return props.displayedProperties.filter((
            displayedProperty: string,
        ) => {

            const targetProperty: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE> | undefined =
                props.databaseSchema.properties.find((
                    property: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>,
                ) => {
                    return property.propertyIdentifier === displayedProperty;
                });

            return typeof targetProperty === "undefined";
        });
    }, [props.displayedProperties, props.databaseSchema]);

    const displayedPropertiesCount = useMemo(() => {

        if (props.displayedProperties.length === props.databaseSchema.properties.length
            || props.displayedProperties.length === 0
        ) {
            return databaseFormat.get(DATABASE_PROFILE.ALL);
        }

        return `${props.displayedProperties.length} / ${props.databaseSchema.properties.length}`;
    }, [props.displayedProperties, props.databaseSchema, isDefaultEmptyFormat(databaseFormat)]);

    return (<UIDropdown
        ariaLabel="displayed-properties-switch"
        variant="faded"
        selectionMode="multiple"
        isCloseOnSelect={false}
        trigger={createUIButton({
            className: "min-w-15 px-3",
            variant: "light",
            color: "primary",
            startContent: <FaTableColumns />,
            children: displayedPropertiesCount,
        })}
        categories={[
            {
                categoryKey: "displayed-properties",
                title: databaseFormat.get(DATABASE_PROFILE.PROPERTIES_FOR_THIS_DATABASE),
                items: props.databaseSchema.properties.map((
                    property: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>,
                ) => {
                    return {
                        itemKey: property.propertyIdentifier,
                        variant: "faded",
                        textValue: property.propertyName,
                        isSelected: props.displayedProperties.includes(property.propertyIdentifier),
                        content: property.propertyName,
                        startContent: getPropertyIcon(
                            property.propertyType,
                            clsx(
                                property.isPrimaryKey && "text-xl",
                            ),
                        ),
                        description: property.isPrimaryKey
                            ? databaseFormat.get(DATABASE_PROFILE.PRIMARY_KEY)
                            : undefined,
                        onPress: () => {

                            if (props.displayedProperties.includes(property.propertyIdentifier)) {
                                props.onDisplayedPropertiesChange(
                                    props.displayedProperties.filter((each) => each !== property.propertyIdentifier),
                                );
                            } else {
                                props.onDisplayedPropertiesChange([
                                    ...props.displayedProperties,
                                    property.propertyIdentifier,
                                ]);
                            }
                        },
                    };
                }),
            },
            floatingKeys.length !== 0 ? {
                categoryKey: "floating-properties",
                title: databaseFormat.get(DATABASE_PROFILE.FLOATING_PROPERTIES),
                items: floatingKeys.map((
                    displayedProperty: string,
                ) => {
                    return {
                        itemKey: displayedProperty,
                        variant: "faded",
                        textValue: displayedProperty,
                        isSelected: props.displayedProperties.includes(displayedProperty),
                        content: displayedProperty,
                        startContent: getUnknownPropertyIcon(),
                        description: databaseFormat.get(DATABASE_PROFILE.FLOATING_PROPERTY_DESCRIPTION),
                        onPress: () => {

                            if (props.displayedProperties.includes(displayedProperty)) {
                                props.onDisplayedPropertiesChange(
                                    props.displayedProperties.filter((each) => each !== displayedProperty),
                                );
                            } else {
                                props.onDisplayedPropertiesChange([
                                    ...props.displayedProperties,
                                    displayedProperty,
                                ]);
                            }
                        },
                    };
                }),
            } : null,
            selectedKeys.size !== 0 ? {
                categoryKey: "actions",
                title: databaseFormat.get(DATABASE_PROFILE.ACTIONS),
                items: [
                    {
                        itemKey: ACTION_DISPLAY_ALL_KEY,
                        variant: "faded",
                        textValue: databaseFormat.get(DATABASE_PROFILE.DISPLAY_ALL),
                        isSelected: false,
                        content: databaseFormat.get(DATABASE_PROFILE.DISPLAY_ALL),
                        startContent: <IoIosDoneAll
                            className="text-large"
                        />,
                        description: databaseFormat.get(DATABASE_PROFILE.DISPLAY_ALL_DESCRIPTION),
                        onPress: () => {

                            if (props.displayedProperties.length === props.databaseSchema.properties.length) {
                                props.onDisplayedPropertiesChange([]);
                            } else {
                                props.onDisplayedPropertiesChange(
                                    props.databaseSchema.properties.map((each) => each.propertyIdentifier),
                                );
                            }
                        },
                    },
                ],
            } : null,
        ]}
    />);
};
