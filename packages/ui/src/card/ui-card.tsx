/**
 * <AUTHOR>
 * @package UI
 * @namespace Card
 * @description Card
 */

import { Card } from "@heroui/card";
import type { FC, ReactNode } from "react";
import { UIShadow } from "../common/types";

export type UICardProps = {

    readonly className?: string;

    readonly shadow?: UIShadow;

    readonly children?: ReactNode;

    readonly onPress?: () => void;
};

export const UICard: FC<UICardProps> = (
    props: UICardProps,
) => {

    return (<Card
        shadow={props.shadow}
        className={props.className}
        isPressable={typeof props.onPress === "function"}
        onPress={props.onPress}
    >
        {props.children}
    </Card>);
};
