/**
 * <AUTHOR>
 * @namespace Internationalization_Util
 * @description Get Localized Text
 */

import { IETF_LOCALE } from "@sudoo/locale";

const UNABLE_TO_GET_TEXT = "[Unable to get text]";

export const getLocalizedText = (
    locale: IETF_LOCALE,
    texts: Partial<Record<IETF_LOCALE, string>>,
    defaultLocale: IETF_LOCALE,
) => {

    return texts[locale] ?? texts[defaultLocale] ?? UNABLE_TO_GET_TEXT;
};
