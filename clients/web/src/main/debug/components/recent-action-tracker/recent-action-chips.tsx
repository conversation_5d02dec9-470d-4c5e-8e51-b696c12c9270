/**
 * <AUTHOR>
 * @namespace Debug_Components_RecentActionTracker
 * @description Recent Action Chips
 */

import { RecentActionItem } from "@imbricate-hummingbird/interceptor-core";
import { UIChip } from "@imbricate-hummingbird/ui";
import { WORKER_WEB_WORKER } from "@imbricate-hummingbird/web-worker-core";
import { FC } from "react";
import { FaPersonRunning } from "react-icons/fa6";

export type RecentActionChipsProps = {

    readonly action: RecentActionItem<WORKER_WEB_WORKER>;
};

export const RecentActionChips: FC<RecentActionChipsProps> = (
    props: RecentActionChipsProps,
) => {

    const action: RecentActionItem<WORKER_WEB_WORKER> = props.action;

    return (<div
        className="flex gap-0.5"
    >
        {action.handledBy.map((worker: WORKER_WEB_WORKER) => {
            return (<UIChip
                key={worker}
                className="font-mono"
                color="primary"
                size="sm"
                radius="sm"
                startContent={<FaPersonRunning />}
            >
                {worker}
            </UIChip>);
        })}
        {action.isRetry ?
            (<UIChip
                className="font-mono"
                color="primary"
                size="sm"
                radius="sm"
            >
                Retry
            </UIChip>)
            : null}
        {action.isAborted ?
            (<UIChip
                className="font-mono"
                color="warning"
                size="sm"
                radius="sm"
            >
                Aborted
            </UIChip>)
            : null}
        {action.isCached ?
            (<UIChip
                className="font-mono"
                color="success"
                size="sm"
                radius="sm"
            >
                Cached
            </UIChip>)
            : null}
        {action.isMerged ?
            (<UIChip
                className="font-mono"
                color="secondary"
                size="sm"
                radius="sm"
            >
                Merged
            </UIChip>)
            : null}
    </div>);
};