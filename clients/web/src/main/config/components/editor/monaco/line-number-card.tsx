/**
 * <AUTHOR>
 * @namespace Main_Config_Components_Editor_Monaco_MinimapCard
 * @description Line Number Card
 */

import { useConfigFormat } from "@/main/config/internationalization/hook";
import { CONFIG_PROFILE } from "@/main/config/internationalization/profile";
import { HUMMINGBIRD_PREFERENCE_ITEM, PreferenceController } from "@imbricate-hummingbird/configuration";
import { FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody, UICardHeader, UIDivider, UISpacer, UISwitch } from "@imbricate-hummingbird/ui";
import { FC, useState } from "react";
import { HiOutlineNumberedList } from "react-icons/hi2";

export type ConfigEditorMonacoLineNumberCardProps = {
};

export const ConfigEditorMonacoLineNumberCard: FC<ConfigEditorMonacoLineNumberCardProps> = (
    _props: ConfigEditorMonacoLineNumberCardProps,
) => {

    const [markdownLineNumberEnablement, setMarkdownLineNumberEnablement] = useState(() => {

        return PreferenceController.getPreference(
            HUMMINGBIRD_PREFERENCE_ITEM.EDITOR_MONACO_LINE_NUMBER_MARKDOWN_ENABLEMENT,
        );
    });

    const configFormat = useConfigFormat();

    return (<StyledCard>
        <UICardHeader>
            <HiOutlineNumberedList />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.LINE_NUMBER)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody>
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                >
                    {configFormat.get(CONFIG_PROFILE.SHOW_LINE_NUMBER_DESCRIPTION)}
                </FormattedText>
            </div>
            <UISwitch
                aria-label="Line Number Enablement for Markdown"
                isSelected={markdownLineNumberEnablement}
                onSelectedChange={(newSelected: boolean) => {

                    setMarkdownLineNumberEnablement(newSelected);

                    PreferenceController.setPreference(
                        HUMMINGBIRD_PREFERENCE_ITEM.EDITOR_MONACO_LINE_NUMBER_MARKDOWN_ENABLEMENT,
                        newSelected,
                    );
                }}
            >
                {configFormat.get(CONFIG_PROFILE.SHOW_LINE_NUMBER_FOR_MARKDOWN)}
            </UISwitch>
        </UICardBody>
    </StyledCard>);
};
