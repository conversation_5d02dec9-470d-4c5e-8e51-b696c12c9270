/**
 * <AUTHOR>
 * @namespace Common_Components
 * @description Copy Item
 */

import { UISnippet } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";

export type CommonCopyItemProps = {

    readonly className?: string;

    readonly startContent?: React.ReactNode;
    readonly prefix?: React.ReactNode;
    readonly content: string;
};

export const CommonCopyItem: FC<CommonCopyItemProps> = (props: CommonCopyItemProps) => {

    const fixedClassNames: string[] = [
        "flex flex-col",
    ];

    if (props.className) {
        fixedClassNames.push(props.className);
    }

    return (<div className={fixedClassNames.join(" ")}>
        {props.startContent && <div
            className="text-medium text-gray-500"
        >
            {props.startContent}
        </div>}
        <UISnippet
            leadingSymbol={props.prefix}
            isInlineSymbol
            copyButtonTooltipContent="Copy to Clipboard"
            copyButtonTooltipPlacement="right"
        >
            {props.content}
        </UISnippet>
    </div>);
};
