/**
 * <AUTHOR>
 * @namespace Common_Components_MagicButton
 * @description Magic Button End Content
 */

import React, { FC } from "react";
import { FaExternalLinkAlt } from "react-icons/fa";
import { TbDirectionsFilled } from "react-icons/tb";
import type { MagicButtonItem } from "./types/button-item";

export type MagicButtonEndContentProps = {

    readonly item: MagicButtonItem;
};

export const MagicButtonEndContent: FC<MagicButtonEndContentProps> = (
    props: MagicButtonEndContentProps,
) => {

    return (<div
        className="flex flex-row gap-2"
    >
        {props.item.externalIndicator && <FaExternalLinkAlt
            className="text-medium self-start"
        />}
        {props.item.redirectIndicator && <TbDirectionsFilled
            className="text-medium self-start"
        />}
    </div>);
};
