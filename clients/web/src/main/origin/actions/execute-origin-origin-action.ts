/**
 * <AUTHOR>
 * @namespace Origin_Actions
 * @description Execute Origin Origin Action
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { ImbricateOriginActionInput } from "@imbricate/core";

export const createExecuteOriginOriginActionAction = (
    origin: ImbricateOriginObject,
    input: ImbricateOriginActionInput,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Execute Origin Origin Action",
        actionDescription: `Execute the origin origin action [${input.actionIdentifier}] in [${origin.origin.uniqueIdentifier}]`,
        executerMetadata: metadata,
        actionPayload: {
            originUniqueIdentifier: origin.origin.uniqueIdentifier,
            originSupportedFeatures: origin.origin.supportedFeatures,
            actionIdentifier: input.actionIdentifier,
            actionParameters: input.parameters,
        },
    };
};
