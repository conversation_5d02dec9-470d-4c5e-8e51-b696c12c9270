{"name": "@imbricate-hummingbird/doc-cast", "description": "Doc Cast implementation for Imbricate Hummingbird", "private": true, "main": "src/index.ts", "scripts": {"test": "jest", "coverage": "jest --coverage", "compile": "tsc --project ./typescript/tsconfig.compile.json", "lint": "eslint src"}, "dependencies": {"@imbricate-hummingbird/debug": "workspace:*", "@imbricate-hummingbird/flow-common": "workspace:*", "@imbricate-hummingbird/internationalization": "workspace:*", "@imbricate-hummingbird/logger": "workspace:*", "@imbricate-hummingbird/origin-central": "workspace:*", "@imbricate-hummingbird/react-components": "workspace:*", "@imbricate-hummingbird/theme-core": "workspace:*", "@imbricate-hummingbird/ui": "workspace:*", "@sudoo/internationalization": "2.1.0", "@sudoo/internationalization-react": "1.6.0", "@xyflow/react": "catalog:", "clsx": "catalog:", "react": "catalog:", "react-dom": "catalog:", "react-icons": "catalog:", "react-router-dom": "catalog:"}, "devDependencies": {"@imbricate-hummingbird/config": "workspace:*", "@types/react": "catalog:", "@types/react-dom": "catalog:", "eslint": "catalog:"}}