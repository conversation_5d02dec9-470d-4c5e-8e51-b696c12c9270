/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyDisplay
 * @description String Display
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIChip } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaProperty } from "@imbricate/core";
import { FC } from "react";
import { useDocumentFormat } from "../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../internationalization/profile";

export type DocumentStringDisplayProps = {

    readonly propertySchema: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE.STRING>;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.STRING>;
};

export const DocumentStringDisplay: FC<DocumentStringDisplayProps> = (
    props: DocumentStringDisplayProps,
) => {

    const documentFormat = useDocumentFormat();

    if (typeof props.property.value !== "string") {
        return (<UIChip>
            {documentFormat.get(DOCUMENT_PROFILE.NO_DATA)}
        </UIChip>);
    }

    return (<div>
        {props.property.value}
    </div>);
};
