{"name": "@imbricate-hummingbird/flow-common", "description": "Common implementation for Flow", "private": true, "main": "src/index.ts", "scripts": {"test": "jest", "coverage": "jest --coverage", "compile": "tsc --project ./typescript/tsconfig.compile.json", "lint": "eslint src"}, "dependencies": {"@dagrejs/dagre": "1.1.5", "@xyflow/react": "catalog:", "clsx": "catalog:", "react": "catalog:"}, "devDependencies": {"@imbricate-hummingbird/config": "workspace:*", "@types/react": "catalog:", "eslint": "catalog:"}}