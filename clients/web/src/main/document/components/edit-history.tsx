/**
 * <AUTHOR>
 * @namespace Document_Components
 * @description Edit History
 */

import { useOldOrigin } from "@/main/origin/hooks/use-origin";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { LoadingWrapper, StyledCard } from "@imbricate-hummingbird/react-components";
import { useNavigateDocumentView } from "@imbricate-hummingbird/react-navigation";
import { UIButton, UICardBody, UIDivider, UIImbricateAuthorUser, UISpacer } from "@imbricate-hummingbird/ui";
import { DocumentEditOperation, DocumentEditRecord, IImbricateDatabase, IImbricateDocument, IMBRICATE_DOCUMENT_EDIT_TYPE, ImbricateDocumentGetEditRecordsOutcome } from "@imbricate/core";
import React, { FC, useEffect } from "react";
import { FaCircleDot } from "react-icons/fa6";
import { ErrorScreen } from "../../common/components/error-screen";
import { UseDatabaseResponseSymbol, useDatabase } from "../../database/hooks/use-database";
import { UseDocumentResponseSymbol, useOldDocument } from "../hooks/use-document";
import { buildHourMinuteSecond, buildYearMonthDate } from "../util/format-date";
import { getDocumentEditTypeIcon } from "../util/get-document-edit-type-icon";
import { DocumentEditRecordsOperation } from "./edit-history-operation/edit-history-operation";

export type DocumentsEditHistoryProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;
};

export const DocumentsEditHistory: FC<DocumentsEditHistoryProps> = (
    props: DocumentsEditHistoryProps,
) => {

    const navigateToDocumentView = useNavigateDocumentView();

    const [loading, setLoading] = React.useState<boolean>(true);
    const [notSupported, setNotSupported] = React.useState<boolean>(false);

    const [editRecords, setEditRecords] = React.useState<DocumentEditRecord[]>([]);

    const origin: ImbricateOriginObject | null =
        useOldOrigin(props.originUniqueIdentifier);

    const database: IImbricateDatabase | UseDatabaseResponseSymbol =
        useDatabase(props.databaseUniqueIdentifier);

    const document: IImbricateDocument | UseDocumentResponseSymbol =
        useOldDocument(
            props.databaseUniqueIdentifier,
            props.documentUniqueIdentifier,
        );

    useEffect(() => {

        if (typeof document === "symbol") {
            return;
        }

        if (typeof document.getEditRecords !== "function") {
            setNotSupported(true);
            setLoading(false);
        }

        const execute = async () => {

            const records: ImbricateDocumentGetEditRecordsOutcome =
                await document.getEditRecords!();

            if (typeof records === "symbol") {

                setNotSupported(true);
                setLoading(false);
                return;
            }

            setLoading(false);
            setEditRecords(records.editRecords);
        };

        execute();
    }, [typeof document]);

    if (loading) {

        return (<LoadingWrapper
            debugDescription="Documents Edit History"
            fullHeight
        />);
    }

    if (!origin
        || typeof database === "symbol"
        || typeof document === "symbol") {

        return (<LoadingWrapper
            debugDescription="Documents Edit History"
            fullHeight
        />);
    }

    if (notSupported) {

        return (<ErrorScreen
            title="Edit History not supported by this origin"
            message={<div>
                <div>
                    Origin "{origin.originName}" does not support edit history feature. Please contact the origin administrator for more information.
                </div>
                <UISpacer />
                <UIButton
                    variant="ghost"
                    color="primary"
                    size="sm"
                    onPress={() => {
                        navigateToDocumentView(
                            origin.origin.uniqueIdentifier,
                            database.uniqueIdentifier,
                            document.uniqueIdentifier,
                            {
                                replace: true,
                            },
                        );
                    }}
                >
                    Document Details
                </UIButton>
            </div>}
            color="warning"
        />);
    }

    return (<div
        className="flex-1 min-w-0 min-h-0 pr-2 mt-2"
    >
        <div
            className="grid grid-cols-[1fr_5fr] gap-2"
        >
            {editRecords.sort((a: DocumentEditRecord, b: DocumentEditRecord) => {
                return new Date(b.editAt).getTime()
                    - new Date(a.editAt).getTime();
            }).map((
                records: DocumentEditRecord,
            ) => {

                const editAt: Date = new Date(records.editAt);

                return (<React.Fragment
                    key={records.uniqueIdentifier}
                >
                    <div
                        className="place-self-end self-start flex flex-col items-end sm:pt-2 md:pt-2 lg:pt-3"
                    >
                        {records.author ? <UIImbricateAuthorUser
                            size="sm"
                            author={records.author}
                        /> : <FaCircleDot
                            className="text-2xl"
                        />}
                        <UIDivider
                            className="mt-1"
                        />
                        <div>
                            {buildYearMonthDate(editAt)}
                        </div>
                        <div>
                            {buildHourMinuteSecond(editAt)}
                        </div>
                    </div>
                    <StyledCard>
                        {records.operations.map((
                            operation: DocumentEditOperation<IMBRICATE_DOCUMENT_EDIT_TYPE>,
                            index: number,
                        ) => {

                            const keyValue: string = `${operation.action}-${index}`;
                            const isLast: boolean = index === records.operations.length - 1;

                            return (<React.Fragment
                                key={keyValue}
                            >
                                <UICardBody
                                    className="flex flex-row items-center gap-2"
                                >
                                    {getDocumentEditTypeIcon(operation.action, "text-xl")}
                                    <UIDivider
                                        className="h-full"
                                        orientation="vertical"
                                    />
                                    <DocumentEditRecordsOperation
                                        originUniqueIdentifier={origin.origin.uniqueIdentifier}
                                        databaseUniqueIdentifier={database.uniqueIdentifier}
                                        documentUniqueIdentifier={document.uniqueIdentifier}
                                        schema={database.schema}
                                        operation={operation}
                                    />
                                </UICardBody>
                                {isLast ? null : <UIDivider />}
                            </React.Fragment>);
                        })}
                    </StyledCard>
                </React.Fragment>);
            })}
        </div>
    </div>);
};
