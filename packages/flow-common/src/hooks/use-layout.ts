/**
 * <AUTHOR>
 * @package Flow Common
 * @namespace Hooks
 * @description Use Layout
 */

import { Edge, Node, ReactFlowInstance } from "@xyflow/react";
import { useCallback } from "react";
import { GetLayoutedElementsDirection, getLayoutedElements } from "../utils/get-layouted-elements";

export const useFlowLayout = (
    nodes: Node[],
    edges: Edge[],
    setParsedNodes: React.Dispatch<React.SetStateAction<Node[]>>,
    reactFlow: ReactFlowInstance,
): (direction: GetLayoutedElementsDirection) => void => {

    const onLayout = useCallback(
        (direction: GetLayoutedElementsDirection) => {

            const layouted = getLayoutedElements(
                nodes,
                edges,
                {
                    direction,
                    fitView: true,
                },
            );

            setParsedNodes([...layouted.nodes]);

            reactFlow.fitView();
        },
        [nodes, edges],
    );

    return onLayout;
};
