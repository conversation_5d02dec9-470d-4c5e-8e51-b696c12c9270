/**
 * <AUTHOR>
 * @namespace OriginCentral
 * @description Upload Static In Base64
 */

import { $ORIGIN_NOT_FOUND } from "@imbricate-hummingbird/global-symbol";
import { TransferStatic } from "@imbricate-hummingbird/transfer-core";

export type CentralUploadStaticInBase64ResponseSymbol =
    | typeof $ORIGIN_NOT_FOUND;

export type CentralUploadStaticInBase64Value = {

    readonly originUniqueIdentifier: string;
    readonly static: TransferStatic;
};

export type CentralUploadStaticInBase64Response =
    | CentralUploadStaticInBase64Value
    | CentralUploadStaticInBase64ResponseSymbol;
