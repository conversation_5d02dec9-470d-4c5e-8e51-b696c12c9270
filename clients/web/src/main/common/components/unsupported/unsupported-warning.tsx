/**
 * <AUTHOR>
 * @namespace Common_Components_Unsupported
 * @description Unsupported Warning
 */

import { UIAlert, UIColor } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";

export type UnsupportedWarningProps = {

    readonly title: string;
    readonly message: React.ReactNode;

    readonly variant?: "flat" | "solid" | "faded" | "bordered";
    readonly color?: UIColor;
};

export const UnsupportedWarning: FC<UnsupportedWarningProps> = (
    props: UnsupportedWarningProps,
) => {

    return (<div
        className="flex flex-col justify-center items-center"
    >
        <UIAlert
            titleClassName="font-mono"
            color={props.color}
            title={props.title}
            description={props.message}
            variant={props.variant}
        />
    </div>);
};
