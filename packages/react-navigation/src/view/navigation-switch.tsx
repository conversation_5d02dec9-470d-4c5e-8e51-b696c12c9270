/**
 * <AUTHOR>
 * @namespace Navigation
 * @description Switch
 */

import { HUMMINGBIRD_NAVIGATION_SWITCH_MODE, HUMMINGBIRD_PREFERENCE_ITEM, usePreferenceItem } from "@imbricate-hummingbird/configuration";
import { FC } from "react";
import { NavigationSourceView } from "../source-view/source-navigation";
import { NavigationTriforceView } from "../triforce-view/triforce-navigation";
import { NavigationFrame } from "./navigation-frame";

export type NavigationSwitchProps = {
};

export const NavigationSwitch: FC<NavigationSwitchProps> = (
    _props: NavigationSwitchProps,
) => {

    const navigationSwitchMode = usePreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.NAVIGATION_SWITCH_MODE,
    );

    if (navigationSwitchMode.preference === HUMMINGBIRD_NAVIGATION_SWITCH_MODE.SOURCE_NAVIGATION) {

        return (<NavigationFrame>
            <NavigationSourceView />
        </NavigationFrame>);
    }

    return (<NavigationFrame>
        <NavigationTriforceView />
    </NavigationFrame>);
};
