/**
 * <AUTHOR>
 * @namespace Edit_Editors
 * @description Markdown Editor
 */

import { UseMagicButtonResult } from "@/common/components/magic-button/hooks/use-magic-button";
import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { isDefaultEmptyFormat } from "@imbricate-hummingbird/internationalization";
import { CreateMonacoEditorResponse } from "@imbricate-hummingbird/monaco-core";
import { CreateMonacoEditorForMarkdownProps, MONACO_MARKDOWN_PROFILE, createMonacoEditorForMarkdown, useMonacoMarkdownFormat } from "@imbricate-hummingbird/monaco-markdown";
import { OriginWorkerDataCentral } from "@imbricate-hummingbird/origin-central-web-worker";
import { DRAWER_TYPE, useDrawerAction } from "@imbricate-hummingbird/react-store";
import { IImbricateProperty, IMBRICATE_PROPERTY_TYPE, ImbricateTextManagerCreateTextOutcome } from "@imbricate/core";
import { SudooFormat } from "@sudoo/internationalization";
import * as monaco from "monaco-editor";
import React, { FC, useEffect } from "react";
import { TbLayoutSidebarRightExpandFilled } from "react-icons/tb";
import { ErrorFullScreen } from "../../common/components/error-full-screen";
import { OldDataCentral } from "../../data/old-data-central";
import { useText } from "../../text/hooks/use-text";
import { S_TextLoading, S_TextNotFound, S_TextNotInitialized } from "../../text/types/use-text";
import { createFindOriginForEditorAction } from "../actions/find-origin-for-editor";
import { MarkdownTableEditorModal } from "../components/markdown-table-editor/markdown-table-editor";
import { MonacoMarkdownActionDelegate } from "../delegate/monaco-markdown";
import { useScrollBarMagicButtonItem } from "../hooks/use-scroll-bar-magic-button-item";
import { useEditFormat } from "../internationalization/hook";
import { EDIT_PROFILE } from "../internationalization/profile";
import { GetValueRef } from "../types/editor-refs";

export type EditMarkdownEditorProps = {

    readonly darkMode: boolean;

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;
    readonly propertyUniqueIdentifier: string;

    readonly textUniqueIdentifier?: string;

    readonly primaryValue: IImbricateProperty<IMBRICATE_PROPERTY_TYPE> | null;

    readonly getValueRef: GetValueRef;
    readonly onValueChange: () => void;

    readonly startLineNumber?: number;

    readonly magicButtonResult: UseMagicButtonResult;
};

// LAZY LOAD ONLY
const EditMarkdownEditor: FC<EditMarkdownEditorProps> = (props: EditMarkdownEditorProps) => {

    const editFormat: SudooFormat<EDIT_PROFILE> = useEditFormat();
    const monacoMarkdownFormat: SudooFormat<MONACO_MARKDOWN_PROFILE> = useMonacoMarkdownFormat();

    const drawerActions = useDrawerAction();

    const editorRef = React.useRef<monaco.editor.IStandaloneCodeEditor | null>(null);
    const onboardedRef = React.useRef(false);

    const textContent = useText(
        props.originUniqueIdentifier,
        props.textUniqueIdentifier,
    );

    useScrollBarMagicButtonItem({
        editorRef,
        editorFormat: editFormat,
        magicButtonResult: props.magicButtonResult,
    });

    useEffect(() => {

        if (isDefaultEmptyFormat(editFormat)) {
            return;
        }

        props.magicButtonResult.addItem({
            categoryKey: "markdown",
            itemKey: "preview-markdown-editor",
            title: editFormat.get(EDIT_PROFILE.MARKDOWN_PREVIEW),
            description: editFormat.get(EDIT_PROFILE.MARKDOWN_PREVIEW_DESCRIPTION),
            icon: TbLayoutSidebarRightExpandFilled,
            quickAccess: true,
            onPress: () => {

                const editor = editorRef.current;
                if (!editor) {
                    return;
                }

                const value = editor.getValue();

                drawerActions.openDrawer({
                    type: DRAWER_TYPE.MARKDOWN_TEXT_PEEK,
                    title: "Markdown Preview",
                    payload: {
                        markdownText: value,
                    },
                });
            },
        });

        return () => {
            props.magicButtonResult.removeItem("preview-markdown-editor");
        };
    }, [isDefaultEmptyFormat(editFormat)]);

    useEffect(() => {

        if (textContent === S_TextLoading
            || textContent === S_TextNotFound
        ) {
            return;
        }

        if (onboardedRef.current) {
            return;
        }

        const editorValue = textContent === S_TextNotInitialized
            ? props.primaryValue
                ? `# ${String(props.primaryValue.propertyValue)}\n`
                : "# New Document\n"
            : textContent.textContent;

        const container = document.getElementById("edit-view-monaco");
        if (!container) {
            return;
        }

        onboardedRef.current = true;

        const monacoEditorProps: CreateMonacoEditorForMarkdownProps = {

            actionDelegate: MonacoMarkdownActionDelegate.create(drawerActions),
            originCentral: OriginWorkerDataCentral.getInstance(),

            container,
            initialEditorValue: editorValue,
            darkMode: props.darkMode,
            status: {
                originUniqueIdentifier: props.originUniqueIdentifier,
            },
            format: monacoMarkdownFormat,
        };

        const editorResponse: CreateMonacoEditorResponse = createMonacoEditorForMarkdown(
            monacoEditorProps,
        );

        const editor = editorResponse.editor;

        if (typeof props.startLineNumber === "number") {

            editor.setPosition({
                lineNumber: props.startLineNumber,
                column: 1,
            });
        }

        editor.onDidChangeModelContent(() => {
            props.onValueChange();
        });

        editorRef.current = editor;

        props.getValueRef.current = async () => {

            const updatedTextContent = editor.getValue();

            const origin = await ActionCentral.getInstance().executeAction(
                async (
                    actionIdentifier: string,
                    recordIdentifier: string,
                ) => {
                    return OldDataCentral.getInstance()
                        .getOrigin(
                            props.originUniqueIdentifier,
                            actionIdentifier,
                            recordIdentifier,
                        );
                },
                createFindOriginForEditorAction(
                    props.originUniqueIdentifier,
                    {
                        executer: import.meta.url,
                    },
                ),
            );

            if (!origin) {
                throw new Error("Origin not found");
            }

            const createdText: ImbricateTextManagerCreateTextOutcome =
                await origin.origin.getTextManager()
                    .createText(updatedTextContent);

            if (typeof createdText === "symbol") {
                throw new Error("Failed to create text");
            }

            return createdText.text.uniqueIdentifier;
        };

        return () => {

            editorResponse.disposables.forEach(disposable => disposable.dispose());
            editor.dispose();

            editorRef.current = null;
            onboardedRef.current = false;

            props.getValueRef.current = null;
        };
    }, [typeof textContent === "symbol" ? textContent.toString() : typeof textContent]);

    if (textContent === S_TextNotFound) {

        return (<ErrorFullScreen
            title="Markdown Text Object Not Found"
            message={`The markdown text object with unique identifier: "${props.textUniqueIdentifier}" is not found`}
            color="warning"
        />);
    }

    return (<React.Fragment>
        <div
            id="edit-view-monaco"
            className="flex-1"
        />
        <MarkdownTableEditorModal />
    </React.Fragment>);
};
export default EditMarkdownEditor;
