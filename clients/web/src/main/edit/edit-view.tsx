/**
 * <AUTHOR>
 * @namespace Edit
 * @description Edit View
 */

import { MagicButton } from "@/common/components/magic-button/magic-button";
import { useDirty } from "@/main/common/hooks/use-dirty";
import { useSaveKeys } from "@/main/common/hooks/use-save-keys";
import { OldDataCentral } from "@/main/data/old-data-central";
import { useWorkerTextWorker } from "@imbricate-hummingbird/editor-text-web-worker";
import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { SavedStatusController } from "@imbricate-hummingbird/monaco-core";
import { OriginWorkerDataCentral, useWorkerOriginWorker } from "@imbricate-hummingbird/origin-central-web-worker";
import { LoadingWrapper } from "@imbricate-hummingbird/react-components";
import { useWorkerScriptWorker } from "@imbricate-hummingbird/script-central-web-worker";
import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIButton, UIDivider } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import React, { FC, useCallback } from "react";
import { FaRegSave } from "react-icons/fa";
import { ErrorFullScreen } from "../common/components/error-full-screen";
import { usePrimaryProperty } from "../property/hooks/use-primary-property";
import { useProperty } from "../property/hooks/use-property";
import { S_UsePropertyErrored, S_UsePropertyLoading, S_UsePropertyNotFound } from "../property/types/use-property";
import { createFindDocumentForEditorAction } from "./actions/find-document-for-editor";
import { EditViewTitle } from "./components/edit-title";
import { EditEditors } from "./edit-editors";
import { useEditEditorsMagicButton } from "./hooks/use-edit-editors-magic-button";
import { useEditFormat } from "./internationalization/hook";
import { EDIT_PROFILE } from "./internationalization/profile";
import { createSaveValueAction } from "./operations/save-value";
import { GetValueRef } from "./types/editor-refs";

export type EditViewProps = {

    readonly darkMode: boolean;

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;
    readonly propertyUniqueIdentifier: string;

    readonly startLineNumber?: number;
};

export const EditView: FC<EditViewProps> = (props: EditViewProps) => {

    useWorkerOriginWorker("edit-view");
    useWorkerTextWorker("edit-view");
    useWorkerScriptWorker("edit-view", () => {
        return OriginWorkerDataCentral.getInstance();
    });

    const editFormat = useEditFormat();

    const property = useProperty(
        props.databaseUniqueIdentifier,
        props.documentUniqueIdentifier,
        props.propertyUniqueIdentifier,
    );

    const primaryProperty = usePrimaryProperty(
        props.databaseUniqueIdentifier,
        props.documentUniqueIdentifier,
    );

    const setIsDirty = useDirty();

    const [loading, setLoading] = React.useState<boolean>(false);
    const [valueChanged, setValueChanged] = React.useState<boolean>(false);

    const getValueRef: GetValueRef = React.useRef<(() => Promise<any>) | null>(null);

    const savePropertyAction = useCallback(async () => {

        if (typeof property === "symbol"
            || !getValueRef.current
            || !valueChanged) {
            return;
        }

        if (!getValueRef.current) {
            return;
        }

        setLoading(true);

        const valueContent = await getValueRef.current();
        const updatePropertyValue: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> = {
            key: property.schemaProperty.propertyIdentifier,
            type: property.schemaProperty.propertyType,
            value: valueContent,
            variant: property.schemaProperty.propertyVariant,
        };

        const action = async () => {

            const document = await ActionCentral.getInstance().executeAction(
                async (
                    actionIdentifier: string,
                    recordIdentifier: string,
                ) => {

                    return await OldDataCentral
                        .getInstance()
                        .getDocument(
                            props.originUniqueIdentifier,
                            props.databaseUniqueIdentifier,
                            props.documentUniqueIdentifier,
                            actionIdentifier,
                            recordIdentifier,
                        );
                },
                createFindDocumentForEditorAction(
                    props.originUniqueIdentifier,
                    props.databaseUniqueIdentifier,
                    props.documentUniqueIdentifier,
                    {
                        executer: import.meta.url,
                    },
                ),
            );

            if (typeof document === "symbol") {
                return;
            }

            const putPropertyResult = await document.document.mergeProperties((generator) => {

                return [
                    generator(
                        updatePropertyValue.key,
                        updatePropertyValue.type,
                        updatePropertyValue.value,
                        updatePropertyValue.variant,
                    ),
                ];
            });

            return putPropertyResult;
        };

        const putPropertyResult = await ActionCentral.getInstance().executeAction(
            action,
            createSaveValueAction(
                {
                    executer: import.meta.url,
                },
            ),
        );

        if (typeof putPropertyResult === "symbol") {
            return;
        }

        SavedStatusController.getInstance().notifySaved();

        setIsDirty(false);
        setLoading(false);
        setValueChanged(false);
    }, [
        typeof property === "symbol"
            ? property
            : typeof property,
        typeof getValueRef.current,
        valueChanged,
    ]);

    useSaveKeys(savePropertyAction);

    const magicButtonResult = useEditEditorsMagicButton({
        originUniqueIdentifier: props.originUniqueIdentifier,
        databaseUniqueIdentifier: props.databaseUniqueIdentifier,
        documentUniqueIdentifier: props.documentUniqueIdentifier,
        propertyUniqueIdentifier: props.propertyUniqueIdentifier,
        isLoading: loading,
        valueChanged,
        saveProperty: savePropertyAction,
    });

    if (property === S_UsePropertyErrored) {
        return (<ErrorFullScreen
            color="danger"
        />);
    }

    if (property === S_UsePropertyLoading) {
        return (<LoadingWrapper
            debugDescription="Edit View"
            fullHeight
        />);
    }

    if (property === S_UsePropertyNotFound) {
        return (<ErrorFullScreen
            color="warning"
            title="Property Not Found"
        />);
    }

    return (<div
        className="h-dvh flex flex-col"
    >
        <div
            className="m-2 flex items-center gap-2"
        >
            <EditViewTitle
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}

                primaryValue={typeof primaryProperty === "symbol" ? null : primaryProperty}
                property={property}
            />
            {valueChanged && <UIButton
                isLoading={loading}
                variant="solid"
                color="primary"
                startContent={<FaRegSave
                    className="text-medium"
                />}
                onPress={savePropertyAction}
            >
                {editFormat.get(EDIT_PROFILE.SAVE)}
            </UIButton>}
            <MagicButton
                magicButtonResult={magicButtonResult}
            />
        </div>
        <UIDivider
            className="bg-gray-300 dark:bg-gray-700"
        />
        <EditEditors
            darkMode={props.darkMode}
            originUniqueIdentifier={props.originUniqueIdentifier}
            databaseUniqueIdentifier={props.databaseUniqueIdentifier}
            documentUniqueIdentifier={props.documentUniqueIdentifier}
            propertyUniqueIdentifier={props.propertyUniqueIdentifier}

            primaryValue={typeof primaryProperty === "symbol" ? null : primaryProperty}
            usePropertyResponse={property}
            getValueRef={getValueRef}
            onValueChange={() => {
                if (!valueChanged) {
                    setIsDirty(true);
                    setValueChanged(true);
                }
            }}
            startLineNumber={props.startLineNumber}
            magicButtonResult={magicButtonResult}
        />
    </div>);
};
