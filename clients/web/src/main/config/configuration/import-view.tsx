/**
 * <AUTHOR>
 * @namespace Config_Configuration
 * @description Import View
 */

import { useTitle } from "@imbricate-hummingbird/react-common";
import { FC } from "react";
import { ConfigConfigurationImportCard } from "./import/import-card";

export type ConfigConfigurationImportViewProps = {
};

// LAZY LOAD ONLY
const ConfigConfigurationImportView: FC<ConfigConfigurationImportViewProps> = (
    _props: ConfigConfigurationImportViewProps,
) => {

    useTitle([
        "Import Configuration",
        "Config",
    ], []);

    return (<div
        className="h-full min-h-full flex flex-col"
    >
        <div
            className="flex-1 min-h-0 min-w-0 flex flex-col pr-2 mt-2 gap-2 h-full"
        >
            <ConfigConfigurationImportCard />
        </div>
    </div >);
};
export default ConfigConfigurationImportView;
