/**
 * <AUTHOR>
 * @namespace Config_Util
 * @description Write Configuration
 */

import { HUMMINGBIRD_MANAGED_CONFIG_ITEM, ManagedConfigController } from "@imbricate-hummingbird/configuration";
import { putOriginStorageInstance } from "../../origin/origin-storage";
import { ExportableConfiguration } from "../types/configuration";
import { validateExportableConfiguration } from "./validate-configuration";

export const writeConfigurationStorage = async (
    exportableConfiguration: ExportableConfiguration,
): Promise<boolean> => {

    const validationResult: boolean = validateExportableConfiguration(exportableConfiguration);

    if (!validationResult) {
        return false;
    }

    if (exportableConfiguration.lenses) {

        ManagedConfigController.getInstance()
            .setManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.LENS_CONFIG, exportableConfiguration.lenses);
    }

    if (exportableConfiguration.origins) {
        putOriginStorageInstance(exportableConfiguration.origins);
    }

    return true;
};
