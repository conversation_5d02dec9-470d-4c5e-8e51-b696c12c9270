/**
 * <AUTHOR>
 * @package UI
 * @namespace Skeleton
 * @description Skeleton
 */

import { Skeleton } from "@heroui/skeleton";

export type UISkeletonProps = {

    readonly className?: string;

    readonly isLoaded?: boolean;

    readonly children?: React.ReactNode;
};

export const UISkeleton: React.FC<UISkeletonProps> = (
    props: UISkeletonProps,
) => {

    return (<Skeleton
        className={props.className}
        isLoaded={props.isLoaded}
    >
        {props.children}
    </Skeleton>);
};
