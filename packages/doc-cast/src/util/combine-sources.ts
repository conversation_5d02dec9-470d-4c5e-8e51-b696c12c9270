/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Modals
 * @description Util
 */

import { DOC_CAST_SOURCE_TYPE, DocCastSource } from "../types/source";
import { hashDocCastSource } from "./hash-source";

export const combineSources = (
    previousSources: DocCastSource<DOC_CAST_SOURCE_TYPE>[],
    newSources: DocCastSource<DOC_CAST_SOURCE_TYPE>[],
): DocCastSource<DOC_CAST_SOURCE_TYPE>[] => {

    const previousSourcesSet: Set<string> = new Set();

    for (const source of previousSources) {
        previousSourcesSet.add(hashDocCastSource(source));
    }

    const filteredNewSources: DocCastSource<DOC_CAST_SOURCE_TYPE>[] =
        newSources.filter((
            source: DocCastSource<DOC_CAST_SOURCE_TYPE>,
        ) => {

            return !previousSourcesSet.has(
                hashDocCastSource(source),
            );
        });

    return [
        ...previousSources,
        ...filteredNewSources,
    ];
};
