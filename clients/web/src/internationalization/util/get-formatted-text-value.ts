/**
 * <AUTHOR>
 * @namespace Internationalization_Util
 * @description Get Formatted Text Value
 */

import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";

export const getFormattedTextValue = (
    format: SudooFormat<string>,
    profile: string,
    defaultValue: string,
    ...args: any[]
): string => {

    if (isFormatLoading(format)) {
        return defaultValue;
    }

    return format.get(profile, ...args);
};
