/**
 * <AUTHOR>
 * @namespace Document_Utils
 * @description Get Document Edit Type Icon
 */

import { IMBRICATE_DOCUMENT_EDIT_TYPE } from "@imbricate/core";
import React from "react";
import { RiChatDeleteFill, R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ill, RiInputField, RiSpam2Fill } from "react-icons/ri";

export const getDocumentEditTypeIcon = (
    documentEditType: IMBRICATE_DOCUMENT_EDIT_TYPE,
    className?: string,
): React.ReactNode => {

    // IMBRICATE_DOCUMENT_EDIT_TYPE SWITCH
    switch (documentEditType) {

        case IMBRICATE_DOCUMENT_EDIT_TYPE.PUT_PROPERTY:
            return (<RiInputField
                className={className}
            />);
        case IMBRICATE_DOCUMENT_EDIT_TYPE.DELETE_ANNOTATION:
            return (<RiChatDeleteFill
                className={className}
            />);
        case IMBRICATE_DOCUMENT_EDIT_TYPE.PUT_ANNOTATION:
            return (<RiChatNewFill
                className={className}
            />);
    }

    return (<RiSpam2Fill
        className={className}
    />);
};
