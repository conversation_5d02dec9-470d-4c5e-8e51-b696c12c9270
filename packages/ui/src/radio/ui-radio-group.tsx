/**
 * <AUTHOR>
 * @package UI
 * @namespace Radio
 * @description Radio Group
 */

import { Radio, RadioGroup } from "@heroui/radio";
import type { FC, ReactNode } from "react";
import { UIRadioGroupItem, UIRadioGroupOrientation } from "./types";

export type UIRadioGroupProps = {

    readonly className?: string;

    readonly isDisabled?: boolean;
    readonly isReadOnly?: boolean;
    readonly isRequired?: boolean;
    readonly isInvalid?: boolean;

    readonly orientation?: UIRadioGroupOrientation;

    readonly label?: ReactNode;

    readonly items: UIRadioGroupItem[];

    readonly selected?: string;
    readonly onSelectedChange?: (newSelected: string) => void;
};

export const UIRadioGroup: FC<UIRadioGroupProps> = (
    props: UIRadioGroupProps,
) => {

    return (
        <RadioGroup
            className={props.className}

            isDisabled={props.isDisabled}
            isReadOnly={props.isReadOnly}
            isRequired={props.isRequired}
            isInvalid={props.isInvalid}

            orientation={props.orientation}

            label={props.label}

            value={props.selected}
            onValueChange={props.onSelectedChange}
        >
            {props.items.map((
                item: UIRadioGroupItem,
            ) => {

                return (<Radio
                    key={item.itemKey}
                    value={item.itemKey}
                    className={item.className}

                    isDisabled={item.isDisabled}
                >
                    {item.itemContent}
                </Radio>);
            })}
        </RadioGroup>
    );
};
