/**
 * <AUTHOR>
 * @package UI
 * @namespace Dropdown
 * @description Dropdown
 */

import { Dropdown, DropdownItem, DropdownMenu, DropdownSection, DropdownTrigger } from "@heroui/dropdown";
import type { FC, Key, ReactNode } from "react";
import { UIDropdownBackdrop, UIDropdownItemCategory, UIDropdownItemData, UIDropdownPlacement, UIDropdownSelectionMode, UIDropdownVariant } from "./types";
import { concludeSelectedItemKeys, findUIDropdownItemByItemKey } from "./util";

export type UIDropdownProps = {

    readonly ariaLabel?: string;

    readonly className?: string;
    readonly baseClassName?: string;
    readonly contentClassName?: string;

    readonly placement?: UIDropdownPlacement;
    readonly backdrop?: UIDropdownBackdrop;
    readonly variant?: UIDropdownVariant;

    readonly isCloseOnSelect?: boolean;

    readonly selectionMode?: UIDropdownSelectionMode;

    readonly isOpen?: boolean;
    readonly onOpenChange?: (isOpen: boolean) => void;

    readonly trigger: ReactNode;

    readonly categories: Array<UIDropdownItemCategory | null>;
};

export const UIDropdown: FC<UIDropdownProps> = (
    props: UIDropdownProps,
) => {

    const fixedCategories: UIDropdownItemCategory[] = props.categories.filter((
        category: UIDropdownItemCategory | null,
    ) => {
        return category !== null;
    }) as UIDropdownItemCategory[];

    return (<Dropdown
        className={props.className}
        classNames={{
            base: props.baseClassName,
            content: props.contentClassName,
        }}

        placement={props.placement}
        backdrop={props.backdrop}

        isOpen={props.isOpen}
        onOpenChange={props.onOpenChange}
    >
        <DropdownTrigger>
            {props.trigger}
        </DropdownTrigger>
        <DropdownMenu
            aria-label={props.ariaLabel}
            selectionMode={props.selectionMode}
            selectedKeys={typeof props.selectionMode === "string"
                ? concludeSelectedItemKeys(fixedCategories)
                : undefined}
            variant={props.variant}
            closeOnSelect={props.isCloseOnSelect}
            onAction={(
                key: Key,
            ) => {

                const item = findUIDropdownItemByItemKey(
                    fixedCategories,
                    key as string,
                );

                if (!item || typeof item.onPress !== "function") {
                    return;
                }

                item.onPress();
            }}
        >
            {fixedCategories.map((
                category: UIDropdownItemCategory,
            ) => {

                return (<DropdownSection
                    key={category.categoryKey}
                    title={category.title as any}
                >

                    {category.items.map((
                        item: UIDropdownItemData,
                    ) => {

                        return (<DropdownItem
                            key={item.itemKey}

                            className={item.className}
                            classNames={{
                                description: item.descriptionClassName,
                            }}

                            color={item.color}
                            variant={item.variant}

                            shortcut={item.shortcut}

                            description={item.description}
                            startContent={item.startContent}
                            endContent={item.endContent}

                            textValue={item.textValue}
                        >
                            {item.content}
                        </DropdownItem>);
                    })}
                </DropdownSection>);
            })}
        </DropdownMenu>
    </Dropdown>);
};
