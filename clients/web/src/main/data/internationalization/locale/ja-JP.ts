/**
 * <AUTHOR>
 * @namespace Data_Internationalization_Locale
 * @description Ja-JP
 */

import { DATA_PROFILE } from "../profile";

export const jaJPDataProfile: Record<DATA_PROFILE, string> = {

    [DATA_PROFILE.CONFIRM_PROCEED_WITH_ERROR]: "エラーを無視して続行する",
    [DATA_PROFILE.ERROR_OCCURRED]: "エラーが発生しました",
    [DATA_PROFILE.ERROR_OCCURRED_WHEN_PROCESSING_DATA]: "データ処理中にエラーが発生しました",
    [DATA_PROFILE.PROCEED_WITH_ERROR]: "エラーを無視して続行する",
    [DATA_PROFILE.PROCEED_WITH_ERROR_DESCRIPTION]: "エラーを無視して続行すると、アプリケーションが予期せず動作する可能性があります。その場合、作業をエクスポートし、未保存のデータを確認してから続行してください。",
    [DATA_PROFILE.REASON]: "理由:",
    [DATA_PROFILE.RETRY]: "再試行",
    [DATA_PROFILE.RETRY_AGAIN]: "再試行",
};
