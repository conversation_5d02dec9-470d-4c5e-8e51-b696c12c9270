/**
 * <AUTHOR>
 * @namespace Drawer_Components
 * @description ImbriScript Execute
 */

import { ImbriScriptExecuteContext } from "@imbricate-hummingbird/script-core";
import { ScriptManager } from "@imbricate-hummingbird/script-manager";
import { UIButton } from "@imbricate-hummingbird/ui";
import { FC, useCallback } from "react";
import { FaRunning } from "react-icons/fa";

export type DrawerImbriScriptExecuteProps = {

    readonly imbriscriptText: string;

    readonly context: ImbriScriptExecuteContext;
};

// LAZY LOAD ONLY
const DrawerImbriScriptExecute: FC<DrawerImbriScriptExecuteProps> = (
    props: DrawerImbriScriptExecuteProps,
) => {

    const executeAction = useCallback(async () => {

        await ScriptManager.getInstance()
            .executeBlockedScript(
                "execute-script-imbriscript",
                props.imbriscriptText,
                props.context,
                {
                    actionName: "Execute ImbriScript",
                    actionDescription: "Execute ImbriScript with Execute Action Button",
                    executerMetadata: {
                        executer: import.meta.url,
                    },
                },
            );
    }, [props.imbriscriptText.length]);

    return (<div
        className="flex flex-col gap-2 h-full p-4"
    >
        <div
            className="flex-1"
        >
        </div>
        <UIButton
            startContent={<FaRunning />}
            isFullWidth
            variant="flat"
            color="primary"
            onPress={executeAction}
        >
            Execute ImbriScript
        </UIButton>
    </div>);
};
export default DrawerImbriScriptExecute;
