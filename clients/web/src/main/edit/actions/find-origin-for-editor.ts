/**
 * <AUTHOR>
 * @namespace Edit
 * @description Find Origin For Editor
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createFindOriginForEditorAction = (
    originUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Find Origin For Editor",
        actionDescription: `Find the origin for [${originUniqueIdentifier}]`,
        executerMetadata: metadata,
    };
};
