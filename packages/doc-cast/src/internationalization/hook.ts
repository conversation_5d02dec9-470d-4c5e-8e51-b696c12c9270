/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Internationalization
 * @description Hook
 */

import { defaultEmptyFormat, useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { useEffect, useState } from "react";
import { arrangeInternationalization } from "./intl";
import { ARRANGE_PROFILE } from "./profile";

export const useArrangeFormat = (): SudooFormat<ARRANGE_PROFILE> => {

    const locale = useLocale();

    const [format, setFormat] = useState<SudooFormat<ARRANGE_PROFILE>>(defaultEmptyFormat);

    useEffect(() => {

        const execute = async () => {

            const format = await arrangeInternationalization.format(locale);
            setFormat(format);
        };

        execute();
    }, [locale]);

    return format;
};
