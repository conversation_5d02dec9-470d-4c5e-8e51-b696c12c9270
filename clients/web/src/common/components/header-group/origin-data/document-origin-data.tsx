/**
 * <AUTHOR>
 * @namespace Common_Components_HeaderGroup_OriginData
 * @description Document Origin Data
 */

import { COMMON_PROFILE } from "@/common/internationalization/profile";
import { FormattedText } from "@imbricate-hummingbird/react-components";
import { <PERSON><PERSON><PERSON>, UICard<PERSON>ody, UICardH<PERSON>er, UIDivider } from "@imbricate-hummingbird/ui";
import React from "react";
import { FaCopy } from "react-icons/fa";
import { getHeaderGroupTitle } from "../group";
import { HEADER_GROUP_GROUP, HeaderGroupPropsDataOriginProps } from "../types";
import { OriginDataSnippet } from "./origin-snippets";

export const DocumentGroupOriginData: React.FC<HeaderGroupPropsDataOriginProps<HEADER_GROUP_GROUP.DOCUMENT>> = (
    props: HeaderGroupPropsDataOriginProps<HEADER_GROUP_GROUP.DOCUMENT>,
) => {

    return (<UICard>
        <UICardHeader
            className="p-2 gap-2"
        >
            <FaCopy />
            <FormattedText
                usedFormats={[props.commonFormat]}
            >
                {getHeaderGroupTitle(props.group, props.commonFormat)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="p-2"
        >
            <OriginDataSnippet
                commonFormat={props.commonFormat}
                combinations={{
                    [COMMON_PROFILE.ORIGIN_IDENTIFIER]: {
                        content: props.payload.originUniqueIdentifier,
                    },
                    [COMMON_PROFILE.DATABASE_IDENTIFIER]: {
                        content: props.payload.databaseUniqueIdentifier,
                    },
                    [COMMON_PROFILE.DOCUMENT_IDENTIFIER]: {
                        content: props.payload.documentUniqueIdentifier,
                        color: "primary",
                    },
                }}
            />
        </UICardBody>
    </UICard>);
};
