/**
 * <AUTHOR>
 * @package Monaco React
 * @namespace Hooks
 * @description Use Diff Style
 */

import { useClassStyle } from "../../../../clients/web/src/main/edit/hooks/use-class-style";

const stylesheetLight = `
.diff-add {
    background: lightgreen;
    width: 5px !important;
	margin-left: 3px;
}

.diff-remove {
    background: lightcoral;
    width: 5px !important;
	margin-left: 3px;
}

.diff-update {
    background: lightblue;
    width: 5px !important;
	margin-left: 3px;
}
`;

const stylesheetDark = `
.diff-add::before {
    background: #00ab36;
    width: 5px !important;
	margin-left: 3px;
}

.diff-remove::before {
    background: #d6002b;
    width: 5px !important;
	margin-left: 3px;
}

.diff-update::before {
    background: #0039ab;
    width: 5px !important;
	margin-left: 3px;
}
`;

export const useDiffStyle = () => {

    useClassStyle(stylesheetLight, stylesheetDark);
};
