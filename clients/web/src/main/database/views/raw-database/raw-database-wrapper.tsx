/**
 * <AUTHOR>
 * @namespace Database_Views_RawDatabase
 * @description Raw Database Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const RawDatabaseNavigator = LazyLoadComponent(
    () => import("./raw-database-navigator"),
    "Raw Database Application",
);

export const RawDatabaseWrapper: FC = () => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Raw Database Application"
            fullHeight
        />}
    >
        <RawDatabaseNavigator />
    </React.Suspense>);
};
