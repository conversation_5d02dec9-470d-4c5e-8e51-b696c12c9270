/**
 * <AUTHOR>
 * @namespace Common_Components_HeaderGroup_Types
 * @description Types
 */

import { COMMON_PROFILE } from "@/common/internationalization/profile";
import { SudooFormat } from "@sudoo/internationalization";

export enum HEADER_GROUP_GROUP {

    DATABASE = "DATABASE",
    DOCUMENT = "DOCUMENT",
    PROPERTY = "PROPERTY",
    LENS = "LENS",
}

export type HeaderGroupPayloadDatabase = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
};

export type HeaderGroupPayloadDocument = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;
};

export type HeaderGroupPayloadProperty = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;
    readonly propertyUniqueIdentifier: string;
};

export type HeaderGroupPayloadLens = {

    readonly lensIdentifier: string;
};

export type HeaderGroupPayloadSwitch<T extends HEADER_GROUP_GROUP> =
    T extends HEADER_GROUP_GROUP.DATABASE ? HeaderGroupPayloadDatabase
    : T extends HEADER_GROUP_GROUP.DOCUMENT ? HeaderGroupPayloadDocument
    : T extends HEADER_GROUP_GROUP.PROPERTY ? HeaderGroupPayloadProperty
    : T extends HEADER_GROUP_GROUP.LENS ? HeaderGroupPayloadLens
    : never;

export type HeaderGroupProps<T extends HEADER_GROUP_GROUP> = {

    readonly group: T;

    readonly payload: HeaderGroupPayloadSwitch<T>;
};

export type HeaderGroupPropsDataOriginProps<T extends HEADER_GROUP_GROUP> = {

    readonly commonFormat: SudooFormat<COMMON_PROFILE>;
} & HeaderGroupProps<T>;
