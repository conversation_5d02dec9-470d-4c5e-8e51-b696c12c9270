/**
 * <AUTHOR>
 * @namespace WorkerActivities_Activity
 * @description Header Chip
 */

import { WORKER_ACTIVITY_INITIATOR } from "@imbricate-hummingbird/bridge-core";
import { WORKER_WEB_WORKER } from "@imbricate-hummingbird/web-worker-core";
import React, { FC } from "react";

export type WorkerActivitiesActivityHeaderChipProps = {

    worker: WORKER_WEB_WORKER;
    initiator: WORKER_ACTIVITY_INITIATOR;
};

export const WorkerActivitiesActivityHeaderChip: FC<WorkerActivitiesActivityHeaderChipProps> = (
    props: WorkerActivitiesActivityHeaderChipProps,
) => {

    return (<React.Fragment>
        {props.initiator === WORKER_ACTIVITY_INITIATOR.INITIATOR
            && <span
                className="font-bold font-mono"
            >
                Initiator
            </span>}
        {props.initiator === WORKER_ACTIVITY_INITIATOR.NOTIFICATION
            && <span
                className="font-bold font-mono"
            >
                Notification
            </span>}
    </React.Fragment>);
};
