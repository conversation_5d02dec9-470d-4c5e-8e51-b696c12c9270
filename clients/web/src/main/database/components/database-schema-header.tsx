/**
 * <AUTHOR>
 * @namespace Database_Components
 * @description Schema Header
 */

import { HeaderGroup } from "@/common/components/header-group/header-group";
import { HEADER_GROUP_GROUP } from "@/common/components/header-group/types";
import { HeaderTitle } from "@/common/components/header-title/header-title";
import { HEADER_TITLE_SECONDARY_ITEM_TYPE } from "@/common/components/header-title/types";
import { UseMagicButtonResult } from "@/common/components/magic-button/hooks/use-magic-button";
import { MagicButton } from "@/common/components/magic-button/magic-button";
import { getRouteDatabaseDocumentsView } from "@imbricate-hummingbird/navigation-core";
import { UINavbar } from "@imbricate-hummingbird/ui";
import { IImbricateDatabase } from "@imbricate/core";
import { FC } from "react";
import { FaDatabase } from "react-icons/fa6";
import { useDatabaseFormat } from "../internationalization/hook";
import { DATABASE_PROFILE } from "../internationalization/profile";

export type DatabaseSchemaHeaderProps = {

    readonly originUniqueIdentifier: string;

    readonly database: IImbricateDatabase;

    readonly magicButtonResult: UseMagicButtonResult;
};

export const DatabaseSchemaHeader: FC<DatabaseSchemaHeaderProps> = (
    props: DatabaseSchemaHeaderProps,
) => {

    const databaseFormat = useDatabaseFormat();

    return (<UINavbar
        isFullWidth
        isBordered
        startContent={<HeaderGroup
            group={HEADER_GROUP_GROUP.DATABASE}
            payload={{
                originUniqueIdentifier: props.originUniqueIdentifier,
                databaseUniqueIdentifier: props.database.uniqueIdentifier,
            }}
        />}
        coreContent={<HeaderTitle
            title={props.database.databaseName}
            secondary={{
                type: HEADER_TITLE_SECONDARY_ITEM_TYPE.LINK,
                link: getRouteDatabaseDocumentsView(props.database.uniqueIdentifier),
                title: databaseFormat.get(DATABASE_PROFILE.DATABASE),
                icon: FaDatabase,
            }}
            formatDependencies={[databaseFormat]}
        />}
        endContent={<MagicButton
            magicButtonResult={props.magicButtonResult}
        />}
    />);
};
