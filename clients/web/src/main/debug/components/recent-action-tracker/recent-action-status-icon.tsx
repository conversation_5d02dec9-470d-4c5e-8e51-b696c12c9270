/**
 * <AUTHOR>
 * @namespace Debug_Components_RecentActionTracker
 * @description Recent Action Status Icon
 */

import { RECENT_ACTION_STATUS } from "@imbricate-hummingbird/interceptor-core";
import { UISpinner } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { FaCheck, FaTimes } from "react-icons/fa";

export type RECENT_ACTION_STATUSIconProps = {

    readonly status: RECENT_ACTION_STATUS;
};

export const RECENT_ACTION_STATUSIcon: FC<RECENT_ACTION_STATUSIconProps> = (
    props: RECENT_ACTION_STATUSIconProps,
) => {

    switch (props.status) {
        case RECENT_ACTION_STATUS.SUCCESS:
            return (<FaCheck
                color="green"
                className="text-xl w-6"
            />);
        case RECENT_ACTION_STATUS.FAILED:
            return (<FaTimes
                color="red"
                className="text-2xl w-6"
            />);
        case RECENT_ACTION_STATUS.PENDING:
            return (<UISpinner
                color="warning"
                className="w-6"
                size="sm"
            />);
    }
};
