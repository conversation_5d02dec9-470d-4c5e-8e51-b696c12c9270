/**
 * <AUTHOR>
 * @namespace Internationalization_Internationalization_Hooks
 * @description Language
 */

import { defaultEmptyFormat } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { useEffect, useState } from "react";
import { languageInternationalization } from "../intl";

export const useLanguageFormat = (): SudooFormat<IETF_LOCALE> => {

    const [format, setFormat] = useState<SudooFormat<IETF_LOCALE>>(defaultEmptyFormat);

    useEffect(() => {

        const execute = async () => {

            const format = await languageInternationalization.format(
                IETF_LOCALE.ENGLISH_UNITED_STATES,
            );
            setFormat(format);
        };

        execute();
    }, []);

    return format;
};
