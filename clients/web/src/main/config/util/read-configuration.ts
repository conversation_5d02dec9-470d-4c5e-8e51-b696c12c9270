/**
 * <AUTHOR>
 * @namespace Config_Util
 * @description Read Configuration
 */

import { HUMMINGBIRD_MANAGED_CONFIG_ITEM, LensConfig, ManagedConfigController } from "@imbricate-hummingbird/configuration";
import { OriginStorageInstance } from "@imbricate-hummingbird/origin-central";
import { UUIDVersion1 } from "@sudoo/uuid";
import { getOriginStorageInstance } from "../../origin/origin-storage";
import { ExportableConfiguration } from "../types/configuration";

export const readConfigurationStorage = async (
    includes: string[],
): Promise<ExportableConfiguration> => {

    let lensConfiguration: LensConfig | null = null;
    let originConfiguration: OriginStorageInstance | null = null;

    if (includes.includes("lenses")) {

        lensConfiguration = ManagedConfigController.getInstance()
            .getManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.LENS_CONFIG);
    }

    if (includes.includes("origins")) {
        originConfiguration = getOriginStorageInstance();
    }

    return {

        version: "v1",
        identifier: UUIDVersion1.generateString(),

        exportedAt: Date.now(),

        lenses: lensConfiguration,
        origins: originConfiguration,
    };
};
