/**
 * <AUTHOR>
 * @namespace Property_Draft
 * @description Flatten Drafts
 */

import { TransferDocumentProperty, TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";

export const flattenPropertyDrafts = (
    records: TransferDocumentProperty[],
): TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[] => {

    const drafts: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[] = [];

    for (const item of records) {

        drafts.push({

            key: item.propertyKey,
            type: item.propertyType,
            value: item.propertyValue,
            variant: item.propertyVariant,
        });
    }

    return drafts;
};
