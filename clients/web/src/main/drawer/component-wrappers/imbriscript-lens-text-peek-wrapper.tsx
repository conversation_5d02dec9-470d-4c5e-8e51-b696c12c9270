/**
 * <AUTHOR>
 * @namespace Drawer_Component_Wrappers
 * @description Imbriscript Lens Text Peek Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import { DrawerImbriscriptLensTextPeekProps } from "../components/imbriscript-lens-text-peek";

const DrawerImbriscriptLensTextPeek = LazyLoadComponent(
    () => import("../components/imbriscript-lens-text-peek"),
    "Drawer Imbriscript Lens Text Peek",
);

export const DrawerImbriscriptLensTextPeekWrapper: FC<DrawerImbriscriptLensTextPeekProps> = (
    props: DrawerImbriscriptLensTextPeekProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Drawer Imbriscript Lens Text Peek"
            fullHeight
        />}
    >
        <DrawerImbriscriptLensTextPeek
            {...props}
        />
    </React.Suspense>);
};
