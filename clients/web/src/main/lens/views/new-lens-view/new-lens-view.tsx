/**
 * <AUTHOR>
 * @namespace Lens
 * @description New Lens View
 */

import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { useTitle } from "@imbricate-hummingbird/react-common";
import { PropertySelector, PropertySelectorResponse, StyledCard } from "@imbricate-hummingbird/react-components";
import { useNavigateLensView } from "@imbricate-hummingbird/react-navigation";
import { LensSlice } from "@imbricate-hummingbird/react-store";
import { UIButton, UICardBody, UICardFooter, UICardHeader, UIDivider, UIInput, UINavbar, UISpacer } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import React, { FC } from "react";
import { RiCameraLensFill } from "react-icons/ri";
import { useDispatch } from "react-redux";
import { useLensFormat } from "../../internationalization/hook";
import { LENS_PROFILE } from "../../internationalization/profile";
import { CreateLensResponse, createLens } from "../../operations/create-lens";

export type NewLensViewProps = {
};

// LAZY LOAD ONLY
const NewLensView: FC<NewLensViewProps> = (
    _props: NewLensViewProps,
) => {

    const lensFormat = useLensFormat();

    const [selectedImbriScript, setSelectedImbriScript] = React.useState<PropertySelectorResponse | null>(null);
    const [lensName, setLensName] = React.useState<string>("");

    const navigateToLensView = useNavigateLensView();

    const dispatch = useDispatch();

    useTitle(
        [
            lensFormat.get(LENS_PROFILE.CREATE_NEW_LENS),
        ],
        [isFormatLoading(lensFormat)],
    );

    return (<div
        className="flex flex-col gap-2"
    >
        <UINavbar
            isFullWidth
            isBordered
            startContent={<React.Fragment>
                <RiCameraLensFill
                    className="text-2xl"
                />
                <UISpacer />
                <p
                    className="font-mono"
                >
                    {lensFormat.get(LENS_PROFILE.LENS)}
                </p>
            </React.Fragment>}
            coreContent={<p className="font-bold text-xl">
                {lensFormat.get(LENS_PROFILE.CREATE_NEW_LENS)}
            </p>}
        />
        <div
            className="pr-2 flex flex-col gap-2"
        >
            <StyledCard>
                <UICardHeader>
                    {lensFormat.get(LENS_PROFILE.CREATE_LENS)}
                </UICardHeader>
                <UIDivider />
                <UICardBody>
                    <UIInput
                        label="Lens Name"
                        placeholder="The lens name"
                        value={lensName}
                        onValueChange={(
                            newValue: string,
                        ) => {
                            setLensName(newValue);
                        }}
                    />
                </UICardBody>
            </StyledCard>
            <StyledCard>
                <UICardHeader>
                    Select ImbriScript
                </UICardHeader>
                <UIDivider />
                <UICardBody>
                    <PropertySelector
                        allowedPropertyType={[
                            IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT,
                        ]}
                        onSelectCancel={() => {
                            setSelectedImbriScript(null);
                        }}
                        onSelectConfirm={(response: PropertySelectorResponse) => {
                            setSelectedImbriScript(response);
                        }}
                    />
                </UICardBody>
                {selectedImbriScript &&
                    <React.Fragment>
                        <UIDivider />
                        <UICardFooter>
                            <UIButton
                                variant="flat"
                                color="primary"
                                isDisabled={lensName.length === 0}
                                onPress={() => {

                                    const newLens: CreateLensResponse = createLens(
                                        lensName,
                                        {
                                            databaseUniqueIdentifier: selectedImbriScript.selectedDatabase.databaseUniqueIdentifier,
                                            documentUniqueIdentifier: selectedImbriScript.selectedDocument.documentUniqueIdentifier,
                                            propertyKey: selectedImbriScript.selectedProperty,
                                        },
                                    );

                                    dispatch(
                                        LensSlice.actions.setLensConfig(
                                            newLens.updated,
                                        ),
                                    );

                                    navigateToLensView(
                                        newLens.newLens.lensIdentifier,
                                        {
                                            replace: true,
                                        },
                                    );
                                }}
                            >
                                Create Lens
                            </UIButton>
                        </UICardFooter>
                    </React.Fragment>}
            </StyledCard>
        </div>
    </div>);
};
export default NewLensView;
