/**
 * <AUTHOR>
 * @package Monaco Log
 * @namespace Monaco
 * @description Create Monaco
 */

import { CreateMonacoEditorResponse, getMonacoSimpleConfig } from "@imbricate-hummingbird/monaco-core";
import { ImbriScriptContainerLog } from "@imbricate-hummingbird/script-core";
import { SudooFormat } from "@sudoo/internationalization";
import * as monaco from "monaco-editor";
import { monacoLogLogger } from "../../util/logger";
import { MONACO_LOG_PREVIEW_PROFILE } from "../internationalization/profile";
import { getLogLineNumberMethod } from "./get-log-line-numbers";
import { stringifyLogForMonaco } from "./stringify-log-for-monaco";

const logger = monacoLogLogger.fork({
    scopes: [
        "Monaco",
        "LogPreview",
        "CreateMonaco",
    ],
});

export const createMonacoForLogPreview = (
    container: HTMLElement,
    logs: ImbriScriptContainerLog[],
    logPreviewFormat: SudooFormat<MONACO_LOG_PREVIEW_PROFILE>,
    progressing: boolean,
    darkMode: boolean,
): CreateMonacoEditorResponse => {

    const logString = stringifyLogForMonaco(logs, progressing);
    const logLineNumberMethod = getLogLineNumberMethod(logs);

    const baseConfig = getMonacoSimpleConfig({
        initialValue: logString,
        language: "plaintext",
        darkMode,
        readonly: true,
    });

    const editor = monaco.editor.create(container, {
        ...baseConfig,
        automaticLayout: true,
        lineNumbersMinChars: 19,
        lineNumbers: logLineNumberMethod,
    });

    const messageContribution: any = editor.getContribution("editor.contrib.messageController");
    const originalShowMessageFunction = messageContribution.showMessage.bind(messageContribution);

    messageContribution.showMessage = (
        message: any,
        position: monaco.Position,
    ) => {

        if (typeof message === "object" && message.value === "Cannot edit in read-only editor") {

            logger.debug("Cannot edit in read-only editor");

            originalShowMessageFunction(
                logPreviewFormat.get(MONACO_LOG_PREVIEW_PROFILE.CANNOT_EDIT_IN_LOG_VIEWER),
                position,
            );
        }
    };

    return {
        editor,
        disposables: [],
    };
};
