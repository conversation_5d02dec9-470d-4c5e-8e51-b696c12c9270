/**
 * <AUTHOR>
 * @namespace Main_Config_Components_ImbriScript_Outlook_ActionButtonCard
 * @description Action Button Card
 */

import { useConfigFormat } from "@/main/config/internationalization/hook";
import { CONFIG_PROFILE } from "@/main/config/internationalization/profile";
import { HUMMINGBIRD_IMBRISCRIPT_OUTLOOK_ACTION_BUTTON_REDIRECT_BUTTON_MODE, HUMMINGBIRD_PREFERENCE_ITEM, usePreferenceItem } from "@imbricate-hummingbird/configuration";
import { FormattedRadioGroup, FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UICard<PERSON>ody, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { AiFillInteraction } from "react-icons/ai";

export type ConfigImbricateScriptOutlookActionButtonCardProps = {
};

export const ConfigImbricateScriptOutlookActionButtonCard: FC<ConfigImbricateScriptOutlookActionButtonCardProps> = (
    _props: ConfigImbricateScriptOutlookActionButtonCardProps,
) => {

    const configFormat = useConfigFormat();

    const imbriScriptLogPreviewEditorOption = usePreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.IMBRISCRIPT_OUTLOOK_ACTION_BUTTON_REDIRECT_BUTTON_MODE,
    );

    return (<StyledCard>
        <UICardHeader>
            <AiFillInteraction />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.IMBRISCRIPT_OUTLOOK_ACTION_BUTTON)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-2"
        >
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                    className="text-sm text-gray-500"
                >
                    {configFormat.get(CONFIG_PROFILE.IMBRISCRIPT_OUTLOOK_ACTION_BUTTON_REDIRECT_BUTTON_MODE_DESCRIPTION)}
                </FormattedText>
            </div>
            <FormattedRadioGroup
                label={configFormat.get(CONFIG_PROFILE.IMBRISCRIPT_OUTLOOK_ACTION_BUTTON_REDIRECT_BUTTON_MODE)}
                selected={imbriScriptLogPreviewEditorOption.preference}
                onSelectedChange={(newSelected: string) => {

                    imbriScriptLogPreviewEditorOption.updatePreference(
                        newSelected as HUMMINGBIRD_IMBRISCRIPT_OUTLOOK_ACTION_BUTTON_REDIRECT_BUTTON_MODE,
                    );
                }}
                format={configFormat}
                items={[
                    {
                        itemKey: HUMMINGBIRD_IMBRISCRIPT_OUTLOOK_ACTION_BUTTON_REDIRECT_BUTTON_MODE.IN_PLACE,
                        itemProfileItem: CONFIG_PROFILE.IN_PLACE,
                    },
                    {
                        itemKey: HUMMINGBIRD_IMBRISCRIPT_OUTLOOK_ACTION_BUTTON_REDIRECT_BUTTON_MODE.NEW_TAB,
                        itemProfileItem: CONFIG_PROFILE.NEW_TAB,
                    },
                    {
                        itemKey: HUMMINGBIRD_IMBRISCRIPT_OUTLOOK_ACTION_BUTTON_REDIRECT_BUTTON_MODE.DRAWER,
                        itemProfileItem: CONFIG_PROFILE.DRAWER,
                    },
                ]}
            />
        </UICardBody>
    </StyledCard>);
};
