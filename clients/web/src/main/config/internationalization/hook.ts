/**
 * <AUTHOR>
 * @namespace Config_Internationalization
 * @description Hook
 */

import { defaultEmptyFormat, useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { useLayoutEffect, useState } from "react";
import { configInternationalization } from "./intl";
import { CONFIG_PROFILE } from "./profile";

export const useConfigFormat = (): SudooFormat<CONFIG_PROFILE> => {

    const locale = useLocale();

    const [format, setFormat] = useState<SudooFormat<CONFIG_PROFILE>>(defaultEmptyFormat);

    useLayoutEffect(() => {

        const execute = async () => {

            const format = await configInternationalization.format(locale);
            setFormat(format);
        };

        execute();
    }, [locale]);

    return format;
};
