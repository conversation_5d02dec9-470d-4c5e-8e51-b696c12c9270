/**
 * <AUTHOR>
 * @namespace Common_Components_MagicButton_Items
 * @description Magic Button Items
 */

import { COMMON_PROFILE } from "@/common/internationalization/profile";
import { getMetaKeyShortcutText } from "@/common/shortcut/get-meta-key";
import { UseGuidanceItemResult } from "@imbricate-hummingbird/configuration";
import { FormattedText } from "@imbricate-hummingbird/react-components";
import { UIDropdownItemCategory, UIDropdownItemData } from "@imbricate-hummingbird/ui";
import { SudooFormat } from "@sudoo/internationalization";
import clsx from "clsx";
import { UseMagicButtonResult, UseMagicButtonResultCategory } from "./hooks/use-magic-button";
import { MagicButtonEndContent } from "./magic-button-end-content";
import { MagicButtonItem } from "./types/button-item";
import { ConcludeMagicButtonCategory } from "./util/conclude";

export const getMagicButtonItems = (
    magicButtonResult: UseMagicButtonResult,
    concluded: ConcludeMagicButtonCategory[],
    dismissedMagicButtonShortcut: UseGuidanceItemResult,
    format: SudooFormat<COMMON_PROFILE>,
): UIDropdownItemCategory[] => {

    const result: UIDropdownItemCategory[] = [];

    if (!dismissedMagicButtonShortcut.itemDismissed) {

        result.push({
            categoryKey: "$-shortcut",
            title: (<FormattedText
                usedFormats={[format]}
            >
                {format.get(COMMON_PROFILE.SHORTCUT)}
            </FormattedText>),
            items: [{
                itemKey: "$-shortcut-box",
                description: (<FormattedText
                    usedFormats={[format]}
                >
                    {format.get(COMMON_PROFILE.MAGIC_BUTTON_SHORTCUT_DESCRIPTION)}
                </FormattedText>),
                variant: "light",
                shortcut: getMetaKeyShortcutText() + "E",
                onPress: () => {
                    dismissedMagicButtonShortcut.dismissItem();
                },
                content: (<FormattedText
                    usedFormats={[format]}
                >
                    {format.get(COMMON_PROFILE.MAGIC_BUTTON_SHORTCUT)}
                </FormattedText>),
            }],
        });
    }

    result.push(...concluded.map((
        category: ConcludeMagicButtonCategory,
    ) => {

        const categoryItem = magicButtonResult.categories.find((
            item: UseMagicButtonResultCategory,
        ) => {
            return item.categoryKey === category.categoryKey;
        });

        return {
            categoryKey: category.categoryKey,
            title: categoryItem?.categoryTitle,
            items: category.items.map((
                item: MagicButtonItem,
            ): UIDropdownItemData => {

                return {

                    itemKey: item.itemKey,
                    description: item.description,
                    textValue: item.title,
                    startContent: (<item.icon
                        className={clsx(
                            "text-large",
                            `text-${item.color}`,
                        )}
                    />),
                    endContent: (<MagicButtonEndContent
                        item={item}
                    />),
                    color: item.color,
                    variant: "faded",
                    content: (<span className={`text-${item.color}`}>
                        {item.title}
                    </span>),
                    onPress: () => {

                        item.onPress();
                        magicButtonResult.onExecuteItem(item.itemKey);
                    },
                };
            }),
        };
    }));

    return result;
};
