/**
 * <AUTHOR>
 * @namespace Common_Util
 * @description Format Time
 */

export const formatTime = (timestamp: number): string => {

    const date: Date = new Date(timestamp);

    const timeString: string = [
        date.getHours().toString().padStart(2, "0"),
        ":",
        date.getMinutes().toString().padStart(2, "0"),
        ":",
        date.getSeconds().toString().padStart(2, "0"),
        ".",
        date.getMilliseconds().toString().padStart(3, "0"),
    ].join("");

    return timeString;
};
