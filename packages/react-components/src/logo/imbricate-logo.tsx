/**
 * <AUTHOR>
 * @namespace Logo
 * @description Imbricate Logo
 */

import React, { FC } from "react";

export type ImbricateLogoProps = {

    readonly size?: "tiny" | "small" | "large";
};

export const ImbricateLogo: FC<ImbricateLogoProps> = (
    props: ImbricateLogoProps,
) => {

    return (<div
        className={`font-mono text-${props.size ?? "small"} font-bold`}
    >
        I M B<br />
        R I C<br />
        A T E
    </div>);
};
