/**
 * <AUTHOR>
 * @namespace Config_Panels
 * @description General Panel
 */

import React, { FC } from "react";
import { ConfigLanguageCard } from "../components/general/language-card";

export type ConfigGeneralPanelProps = {
};

export const ConfigGeneralPanel: FC<ConfigGeneralPanelProps> = (
    _props: ConfigGeneralPanelProps,
) => {

    return (<div
        className="flex flex-col gap-2 pr-2 py-2 flex-1"
    >
        <ConfigLanguageCard />
    </div>);
};
