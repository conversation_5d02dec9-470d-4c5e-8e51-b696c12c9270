/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyDisplay
 * @description Label Display
 */

import { LABEL_COLOR } from "@/common/database/label/label-color";
import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIChip } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaPropertyOptionsLabel } from "@imbricate/core";
import { FC } from "react";
import { getLabelColorClassName, getLabelColorTextClassNameReverse } from "../../../database/utils/label-color";

export type DocumentLabelDisplayProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.LABEL>;

    readonly options: ImbricateDatabaseSchemaPropertyOptionsLabel;
};

export const DocumentLabelDisplay: FC<DocumentLabelDisplayProps> = (
    props: DocumentLabelDisplayProps,
) => {

    const parsedValue = Array.isArray(props.property.value)
        ? props.property.value
        : [];

    return parsedValue.map((each: string) => {

        const labelConfig = props.options.labelOptions.find((option) => option.labelIdentifier === each);

        if (!labelConfig) {
            return null;
        }

        const labelColor = labelConfig.labelColor;
        const textValue = labelConfig.labelName;

        return (<UIChip
            key={each}
            size="sm"
            className="mr-1 last:mr-0"
            baseClassName={labelColor
                ? (getLabelColorClassName(labelColor as LABEL_COLOR) ?? "")
                : ""}
            contentClassName={labelColor
                ? (getLabelColorTextClassNameReverse(labelColor as LABEL_COLOR) ?? "")
                : ""}
        >
            {textValue}
        </UIChip>);
    });
};
