/**
 * <AUTHOR>
 * @namespace Internationalization_Internationalization_Locale
 * @description Ko-KR
 */

import { IETF_LOCALE } from "@sudoo/locale";

// spell-checker: disable   
export const koKRInternationalizationProfile: Record<IETF_LOCALE, string> = {

    [IETF_LOCALE.CHINESE_SIMPLIFIED]: "중국어 (간체)",
    [IETF_LOCALE.CHINESE_TRADITIONAL]: "중국어 (번체)",
    [IETF_LOCALE.ENGLISH_CANADA]: "영어 (캐나다)",
    [IETF_LOCALE.ENGLISH_UNITED_KINGDOM]: "영어 (영국)",
    [IETF_LOCALE.ENGLISH_UNITED_STATES]: "영어 (미국)",
    [IETF_LOCALE.FRENCH_CANADA]: "프랑스어 (캐나다)",
    [IETF_LOCALE.FRENCH_FRANCE]: "프랑스어 (프랑스)",
    [IETF_LOCALE.JAPANESE_JAPAN]: "일본어",
    [IETF_LOCALE.KOREAN_KOREA]: "한국어",
    [IETF_LOCALE.MAORI_NEW_ZEALAND]: "마오리어",
    [IETF_LOCALE.RUSSIAN_RUSSIA]: "러시아어",
    [IETF_LOCALE.SPANISH_MEXICO]: "스페인어 (멕시코)",
    [IETF_LOCALE.SPANISH_SPAIN]: "스페인어 (스페인)",
};
// spell-checker: enable