/**
 * <AUTHOR>
 * @package UI
 * @namespace Modal
 * @description Modal Body
 */

import { ModalBody } from "@heroui/modal";
import type { FC, ReactNode } from "react";

export type UIModalBodyProps = {

    readonly className?: string;

    readonly children?: ReactNode;
};

export const UIModalBody: FC<UIModalBodyProps> = (
    props: UIModalBodyProps,
) => {

    return (<ModalBody
        className={props.className}
    >
        {props.children}
    </ModalBody>);
};
