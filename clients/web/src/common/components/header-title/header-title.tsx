/**
 * <AUTHOR>
 * @namespace Common_Components_HeaderTitle
 * @description Header Title
 */

import React, { FC } from "react";
import { HeaderTitleSecondary } from "./header-title-secondary";
import { HeaderTitleSecondaryItem } from "./types";
import { SudooFormat } from "@sudoo/internationalization";

export type HeaderTitleProps = {

    readonly title: string;

    readonly secondary?: HeaderTitleSecondaryItem;
    readonly formatDependencies?: SudooFormat[];
};

export const HeaderTitle: FC<HeaderTitleProps> = (
    props: HeaderTitleProps,
) => {

    return (<div
        className="flex flex-col gap-0 items-center"
    >
        <div className="font-bold text-xl">
            {props.title}
        </div>
        <HeaderTitleSecondary
            secondary={props.secondary}
            formatDependencies={props.formatDependencies}
        />
    </div>);
};
