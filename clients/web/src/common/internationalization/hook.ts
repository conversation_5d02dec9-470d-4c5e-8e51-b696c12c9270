/**
 * <AUTHOR>
 * @namespace Common_Internationalization
 * @description Hook
 */

import { defaultEmptyFormat, useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { useEffect, useState } from "react";
import { commonInternationalization } from "./intl";
import { COMMON_PROFILE } from "./profile";

export const useCommonFormat = (): SudooFormat<COMMON_PROFILE> => {

    const locale = useLocale();

    const [format, setFormat] = useState<SudooFormat<COMMON_PROFILE>>(defaultEmptyFormat);

    useEffect(() => {

        const execute = async () => {

            const format = await commonInternationalization.format(locale);
            setFormat(format);
        };

        execute();
    }, [locale]);

    return format;
};
