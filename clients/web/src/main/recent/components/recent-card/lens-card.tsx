/**
 * <AUTHOR>
 * @namespace Recent_Components_RecentCard
 * @description Lens Card
 */

import { LensConfig, RECENT_TYPE, RecentItem, getUserFriendlyLensSourceName } from "@imbricate-hummingbird/configuration";
import { useLensConfig } from "@imbricate-hummingbird/react-store";
import { UICardHeader } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { RiCameraLensFill } from "react-icons/ri";
import { RecentCardUnknown } from "./unknown-card";

export type RecentCardLensProps = {

    readonly item: RecentItem<RECENT_TYPE.LENS>;
};

export const RecentCardLens: FC<RecentCardLensProps> = (
    props: RecentCardLensProps,
) => {

    const lensConfig: LensConfig = useLensConfig();

    const targetLens = lensConfig.items.find((each) => {
        return each.lensIdentifier === props.item.payload.lensIdentifier;
    });

    if (!targetLens) {

        return (<RecentCardUnknown
            item={props.item}
        />);
    }

    return (<React.Fragment>
        <UICardHeader
            className="flex flex-row items-center gap-3"
        >
            <RiCameraLensFill
                className="text-2xl"
            />
            <div
                className="flex-1 flex flex-col items-start text-left"
            >
                <div>
                    {targetLens.lensName}
                </div>
                <div
                    className="text-xs text-gray-600 dark:text-gray-400"
                >
                    {getUserFriendlyLensSourceName(targetLens.source)}
                </div>
            </div>
            <div
                className="self-end text-xs text-gray-600 dark:text-gray-400"
            >
                {new Date(props.item.accessedAt).toLocaleString()}
            </div>
        </UICardHeader>
    </React.Fragment>);
};
