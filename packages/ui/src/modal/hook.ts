/**
 * <AUTHOR>
 * @package UI
 * @namespace Modal
 * @description Modal
 */

import { useDisclosure } from "@heroui/modal";

export type UseUIDisclosureResponse = {

    readonly isOpen: boolean;
    readonly onOpen: () => void;
    readonly onOpenChange: (isOpen: boolean) => void;
};

export type UseUIDisclosureProps = {

    readonly initialOpen?: boolean;
};

export const useUIDisclosure = (
    props: UseUIDisclosureProps = {},
): UseUIDisclosureResponse => {

    const { isOpen, onOpen, onOpenChange } = useDisclosure({
        defaultOpen: props.initialOpen,
    });

    return {

        isOpen,
        onOpen,
        onOpenChange,
    };
};
