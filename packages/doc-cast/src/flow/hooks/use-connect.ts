/**
 * <AUTHOR>
 * @namespace Flow_Hooks
 * @description Use Connect
 */

import { Connection, Edge, addEdge } from "@xyflow/react";
import { useCallback } from "react";

export const useDocCastFlowConnection = (
    setParsedEdges: React.Dispatch<React.SetStateAction<Edge[]>>,
): (connection: Connection) => void => {

    const onConnect = useCallback((
        connection: Connection,
    ) => {

        setParsedEdges((
            previousEdges: Edge[],
        ) => {

            return addEdge(
                connection,
                previousEdges,
            );
        });
    }, []);

    return onConnect;
};
