/**
 * <AUTHOR>
 * @namespace Edit_Editors
 * @description ImbriScript Editor
 */

import { UseMagicButtonResult } from "@/common/components/magic-button/hooks/use-magic-button";
import { HUMMINGBIRD_IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE, HUMMINGBIRD_PREFERENCE_ITEM, PreferenceController } from "@imbricate-hummingbird/configuration";
import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { CreateMonacoEditorResponse } from "@imbricate-hummingbird/monaco-core";
import { createMonacoEditorForImbriscript } from "@imbricate-hummingbird/monaco-imbriscript";
import { DRAWER_TYPE, useDrawerAction } from "@imbricate-hummingbird/react-store";
import { ScriptManager } from "@imbricate-hummingbird/script-manager";
import { IImbricateProperty, IMBRICATE_PROPERTY_TYPE, ImbricateTextManagerCreateTextOutcome } from "@imbricate/core";
import { formatTypeScriptCode } from "@sudoo/marked";
import * as monaco from "monaco-editor";
import React, { FC, useEffect } from "react";
import { MdOutlineDirectionsRun, MdOutlineFormatPaint } from "react-icons/md";
import { TbLayoutSidebarRightExpandFilled } from "react-icons/tb";
import * as ts from "typescript";
import { OldDataCentral } from "../../data/old-data-central";
import { useText } from "../../text/hooks/use-text";
import { S_TextLoading, S_TextNotFound, S_TextNotInitialized } from "../../text/types/use-text";
import { createFindOriginForEditorAction } from "../actions/find-origin-for-editor";
import { useEditFormat } from "../internationalization/hook";
import { EDIT_PROFILE } from "../internationalization/profile";
import { GetValueRef } from "../types/editor-refs";

export type EditImbriscriptEditorProps = {

    readonly darkMode: boolean;

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;
    readonly propertyUniqueIdentifier: string;

    readonly textUniqueIdentifier?: string;

    readonly primaryValue: IImbricateProperty<IMBRICATE_PROPERTY_TYPE> | null;

    readonly getValueRef: GetValueRef;
    readonly onValueChange: () => void;

    readonly magicButtonResult: UseMagicButtonResult;
};

// LAZY LOAD ONLY
const EditImbriscriptEditor: FC<EditImbriscriptEditorProps> = (props: EditImbriscriptEditorProps) => {

    const editFormat = useEditFormat();

    const drawerActions = useDrawerAction();

    const editorRef = React.useRef<monaco.editor.IStandaloneCodeEditor | null>(null);
    const onboardedRef = React.useRef(false);

    const textContent = useText(
        props.originUniqueIdentifier,
        props.textUniqueIdentifier,
    );

    useEffect(() => {

        if (isFormatLoading(editFormat)) {
            return;
        }

        props.magicButtonResult.addItem({
            categoryKey: "imbriscript",
            itemKey: "preview-imbriscript-in-lens",
            title: editFormat.get(EDIT_PROFILE.IMBRISCRIPT_LENS_PREVIEW),
            description: editFormat.get(EDIT_PROFILE.IMBRISCRIPT_LENS_PREVIEW_DESCRIPTION),
            icon: TbLayoutSidebarRightExpandFilled,
            quickAccess: true,
            onPress: () => {

                const editor = editorRef.current;
                if (!editor) {
                    return;
                }

                const value = editor.getValue();

                drawerActions.openDrawer({
                    type: DRAWER_TYPE.IMBRISCRIPT_LENS_TEXT_PEEK,
                    title: editFormat.get(EDIT_PROFILE.IMBRISCRIPT_LENS_PREVIEW),
                    payload: {
                        imbriscriptText: value,
                        context: {
                            originUniqueIdentifier: props.originUniqueIdentifier,
                        },
                    },
                });
            },
        });

        props.magicButtonResult.addItem({
            categoryKey: "imbriscript",
            itemKey: "execute-imbriscript",
            title: editFormat.get(EDIT_PROFILE.EXECUTE_IMBRISCRIPT),
            description: editFormat.get(EDIT_PROFILE.EXECUTE_IMBRISCRIPT_DESCRIPTION),
            icon: MdOutlineDirectionsRun,
            quickAccess: true,
            onPress: async () => {

                const editor = editorRef.current;
                if (!editor) {
                    return;
                }

                const value = editor.getValue();

                const confirmMode: HUMMINGBIRD_IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE = PreferenceController.getPreference(
                    HUMMINGBIRD_PREFERENCE_ITEM.IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE,
                );

                // HUMMINGBIRD_IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE SWITCH
                switch (confirmMode) {

                    case HUMMINGBIRD_IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE.CONFIRM_IN_DRAWER: {

                        drawerActions.openDrawer({
                            type: DRAWER_TYPE.IMBRISCRIPT_EXECUTE,
                            title: editFormat.get(EDIT_PROFILE.EXECUTE_IMBRISCRIPT),
                            payload: {
                                imbriscriptText: value,
                                context: {
                                    originUniqueIdentifier: props.originUniqueIdentifier,
                                },
                            },
                        });
                        break;
                    }

                    case HUMMINGBIRD_IMBRISCRIPT_QUICK_EXECUTE_CONFIRM_MODE.NO_CONFIRM: {

                        await ScriptManager.getInstance()
                            .executeBlockedScript(
                                "execute-script-imbriscript",
                                value,
                                {
                                    originUniqueIdentifier: props.originUniqueIdentifier,
                                },
                                {
                                    actionName: "Execute ImbriScript",
                                    actionDescription: "Execute ImbriScript with Execute Action Button",
                                    executerMetadata: {
                                        executer: import.meta.url,
                                    },
                                },
                            );
                        break;
                    }
                }
            },
        });

        props.magicButtonResult.addItem({
            categoryKey: "imbriscript",
            itemKey: "format-imbriscript",
            title: editFormat.get(EDIT_PROFILE.FORMAT_IMBRISCRIPT),
            description: editFormat.get(EDIT_PROFILE.FORMAT_IMBRISCRIPT_DESCRIPTION),
            icon: MdOutlineFormatPaint,
            quickAccess: true,
            onPress: async () => {

                const editor = editorRef.current;
                if (!editor) {
                    return;
                }

                const value = editor.getValue();
                const formatted = await formatTypeScriptCode(value, {
                    baseIndentSize: 0,
                    indentSize: 4,
                    tabSize: 4,
                    newLineCharacter: "\n",
                    convertTabsToSpaces: true,
                    indentStyle: ts.IndentStyle.Block,
                    trimTrailingWhitespace: true,
                    semicolons: ts.SemicolonPreference.Insert,
                });

                editor.setValue(formatted);
            },
        });

        return () => {
            props.magicButtonResult.removeItem("preview-imbriscript-in-lens");
            props.magicButtonResult.removeItem("execute-imbriscript");
            props.magicButtonResult.removeItem("format-imbriscript");
        };
    }, [isFormatLoading(editFormat)]);

    useEffect(() => {

        if (textContent === S_TextLoading
            || textContent === S_TextNotFound
        ) {
            return;
        }

        const editorValue = textContent === S_TextNotInitialized
            ? "// New ImbriScript"
            : textContent.textContent;

        const container = document.getElementById("edit-view-monaco");
        if (!container) {
            return;
        }

        if (onboardedRef.current) {
            return;
        }

        onboardedRef.current = true;
        const editorResponse: CreateMonacoEditorResponse = createMonacoEditorForImbriscript(
            container,
            editorValue,
            props.darkMode,
        );
        const editor = editorResponse.editor;

        const model = editor.getModel();
        if (!model) {
            return;
        }

        model.onDidChangeContent(() => {
            props.onValueChange();
        });

        editorRef.current = editor;

        props.getValueRef.current = async () => {

            const updatedTextContent = editor.getValue();

            const origin = await ActionCentral.getInstance().executeAction(
                async (
                    actionIdentifier: string,
                    recordIdentifier: string,
                ) => {
                    return OldDataCentral.getInstance()
                        .getOrigin(
                            props.originUniqueIdentifier,
                            actionIdentifier,
                            recordIdentifier,
                        );
                },
                createFindOriginForEditorAction(
                    props.originUniqueIdentifier,
                    {
                        executer: import.meta.url,
                    },
                ),
            );

            if (!origin) {
                throw new Error("Origin not found");
            }

            const createdText: ImbricateTextManagerCreateTextOutcome =
                await origin.origin.getTextManager()
                    .createText(updatedTextContent);

            if (typeof createdText === "symbol") {
                throw new Error("Failed to create text");
            }

            return createdText.text.uniqueIdentifier;
        };

        return () => {
            editor.dispose();

            editorRef.current = null;
            onboardedRef.current = false;

            props.getValueRef.current = null;
        };
    }, [typeof textContent === "symbol"
        ? textContent.toString()
        : typeof textContent]);

    return (<div
        id="edit-view-monaco"
        className="flex-1"
    />);
};
export default EditImbriscriptEditor;
