/**
 * <AUTHOR>
 * @namespace Common
 * @description Dynamic Opener
 */

export type DynamicOpenerListener<T> = (input: T) => void;

export class DynamicOpener<T> {

    public static create<T>(): DynamicOpener<T> {

        const dynamicOpener = new DynamicOpener<T>();
        return dynamicOpener;
    }

    private readonly _listener: Set<DynamicOpenerListener<T>>;

    private constructor() {

        this._listener = new Set();
    }

    public attachListener(listener: DynamicOpenerListener<T>) {

        this._listener.add(listener);
    }

    public detachListener(listener: DynamicOpenerListener<T>) {

        this._listener.delete(listener);
    }

    public open(input: T) {

        this._listener.forEach((listener) => {
            listener(input);
        });
    }
}
