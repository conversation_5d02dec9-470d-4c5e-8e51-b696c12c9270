/**
 * <AUTHOR>
 * @namespace Data
 * @description Old Data Central
 */

import { RecentActions } from "@imbricate-hummingbird/interceptor-core";
import { ImbricateOriginObject, realizeOrigins } from "@imbricate-hummingbird/origin-central";
import { WORKER_WEB_WORKER } from "@imbricate-hummingbird/web-worker-core";
import { IImbricateDatabase, ImbricateCommonQueryOriginActionsOutcome, ImbricateDatabaseGetDocumentOutcome, ImbricateDatabaseManagerGetDatabaseOutcome, ImbricateDatabaseManagerQueryDatabasesOutcome, ImbricateDatabaseQueryDocumentsOutcome, ImbricateDocumentQuery, ImbricateStaticManagerGetStaticUriOutcome, ImbricateTextGetContentOutcome, ImbricateTextManagerGetTextOutcome, S_Common_Origin_ConnectionFail, S_DatabaseManager_GetDatabase_NotFound, S_DatabaseManager_QueryDatabases_Stale, S_Database_GetDocument_NotFound, S_Database_QueryDocuments_Stale, S_StaticManager_GetStaticUri_NotFound, S_TextManager_GetText_NotFound, S_Text_GetContent_NotFound } from "@imbricate/core";
import { IMBRICATE_ORIGIN_FEATURE, checkImbricateOriginFeatureSupported } from "@imbricate/core/origin/feature";
import { executeDeduplicate } from "../common/ongoing/ongoing";
import { rootLogger } from "../log/logger";
import { getOriginStorageInstance } from "../origin/origin-storage";
import { DataCache, buildDatabaseCacheKey, buildDatabasesCacheKey, buildDocumentCacheKey, buildOriginCacheKey, buildOriginOriginActionsCacheKey, buildQueryDocumentsCacheKey, buildWideOriginCacheKey, buildWideOriginDatabasesCacheKey, buildWideOriginQueryDocumentsCacheKey } from "./cache";

const S_NoCache: unique symbol = Symbol("No Cache");

const logger = rootLogger.fork({
    scopes: [
        "Data",
        "Old Central",
    ],
});

export class OldDataCentral {

    private static _instance: OldDataCentral;

    public static getInstance(): OldDataCentral {

        if (!this._instance) {
            this._instance = new OldDataCentral();
        }
        return this._instance;
    }

    private readonly _origins: ImbricateOriginObject[];

    private readonly _cache: Map<string, DataCache<any>>;

    private constructor() {

        const originInstance = getOriginStorageInstance();
        this._origins = realizeOrigins(originInstance);

        this._cache = new Map<string, DataCache<any>>();
    }

    public getOrigins(): ImbricateOriginObject[] {

        return this._origins;
    }

    public getOrigin(
        originUniqueIdentifier: string,
        _actionIdentifier: string,
        recordIdentifier: string,
    ): ImbricateOriginObject | null {

        const cacheKey = buildOriginCacheKey(originUniqueIdentifier);
        const cache = this._retrieveCache<ImbricateOriginObject>(cacheKey);

        if (cache !== S_NoCache) {

            this._submitCachedAction(recordIdentifier);
            return cache;
        }

        const origin = this._origins.find((origin: ImbricateOriginObject) => {
            return origin.origin.uniqueIdentifier === originUniqueIdentifier;
        });

        if (!origin) {
            return null;
        }

        this._putCache(cacheKey, origin);
        return origin;
    }

    public async queryOriginActions(
        originUniqueIdentifier: string,
        actionIdentifier: string,
        recordIdentifier: string,
    ): Promise<ImbricateCommonQueryOriginActionsOutcome> {

        const cacheKey = buildOriginOriginActionsCacheKey(originUniqueIdentifier);
        const cache = this._retrieveCache<ImbricateCommonQueryOriginActionsOutcome>(cacheKey);

        if (cache !== S_NoCache) {

            this._submitCachedAction(recordIdentifier);
            return cache;
        }

        const origin = this.getOrigin(
            originUniqueIdentifier,
            actionIdentifier,
            recordIdentifier,
        );

        if (!origin) {
            return S_Common_Origin_ConnectionFail;
        }

        const result = await executeDeduplicate(
            `query-origin-origin-actions-${origin.origin.uniqueIdentifier}`,
            () => origin.origin
                .queryOriginActions({}),
            actionIdentifier,
            recordIdentifier,
        );

        this._putCache(cacheKey, result);
        return result;
    }

    public async wideOriginGetOrigin(
        databaseUniqueIdentifier: string,
        actionIdentifier: string,
        recordIdentifier: string,
    ): Promise<ImbricateOriginObject | null> {

        const cacheKey = buildWideOriginCacheKey(databaseUniqueIdentifier);
        const cache = this._retrieveCache<ImbricateOriginObject>(cacheKey);

        if (cache !== S_NoCache) {

            this._submitCachedAction(recordIdentifier);
            return cache;
        }

        for (const origin of this._origins) {

            const originDatabases = await this.getDatabases(
                origin.origin.uniqueIdentifier,
                actionIdentifier,
                recordIdentifier,
            );

            if (typeof originDatabases === "symbol") {
                continue;
            }

            const targetDatabase = originDatabases.databases.some((
                database: IImbricateDatabase,
            ) => database.uniqueIdentifier === databaseUniqueIdentifier);

            if (!targetDatabase) {
                continue;
            }

            this._putCache(cacheKey, origin);
            return origin;
        }

        this._putCache(cacheKey, null);
        return null;
    }

    public async getDatabase(
        originUniqueIdentifier: string,
        databaseUniqueIdentifier: string,
        actionIdentifier: string,
        recordIdentifier: string,
    ): Promise<ImbricateDatabaseManagerGetDatabaseOutcome> {

        const cacheKey = buildDatabaseCacheKey(
            originUniqueIdentifier,
            databaseUniqueIdentifier,
        );

        const cache = this._retrieveCache<ImbricateDatabaseManagerGetDatabaseOutcome>(cacheKey);

        if (cache !== S_NoCache) {

            this._submitCachedAction(recordIdentifier);
            return cache;
        }

        const origin = this.getOrigin(
            originUniqueIdentifier,
            actionIdentifier,
            recordIdentifier,
        );

        if (!origin) {
            return S_DatabaseManager_GetDatabase_NotFound;
        }

        const database = await executeDeduplicate(
            `get-database-${origin.origin.uniqueIdentifier}-${databaseUniqueIdentifier}`,
            () => origin.origin
                .getDatabaseManager()
                .getDatabase(databaseUniqueIdentifier),
            actionIdentifier,
            recordIdentifier,
        );

        this._putCache(cacheKey, database);
        return database;
    }

    public async getDatabases(
        originUniqueIdentifier: string,
        actionIdentifier: string,
        recordIdentifier: string,
    ): Promise<ImbricateDatabaseManagerQueryDatabasesOutcome> {

        const scopeLogger = logger.forkScope("Get Databases");

        const cacheKey = buildDatabasesCacheKey(originUniqueIdentifier);
        const cache = this._retrieveCache<ImbricateDatabaseManagerQueryDatabasesOutcome>(cacheKey);

        if (cache !== S_NoCache) {

            this._submitCachedAction(recordIdentifier);
            return cache;
        }

        const origin = this.getOrigin(
            originUniqueIdentifier,
            actionIdentifier,
            recordIdentifier,
        );

        if (!origin) {

            scopeLogger.error(`${originUniqueIdentifier} is not found`);
            return S_DatabaseManager_QueryDatabases_Stale;
        }

        const originSupported: boolean = checkImbricateOriginFeatureSupported(
            origin.origin.supportedFeatures,
            IMBRICATE_ORIGIN_FEATURE.ORIGIN_DATABASE_MANAGER,
        );

        if (!originSupported) {

            scopeLogger.error(`${originUniqueIdentifier} is not supported`);
            return S_DatabaseManager_QueryDatabases_Stale;
        }

        const isFeatureSupported = checkImbricateOriginFeatureSupported(
            origin.origin.supportedFeatures,
            IMBRICATE_ORIGIN_FEATURE.ORIGIN_DATABASE_MANAGER,
        );

        if (!isFeatureSupported) {

            scopeLogger.error(`${originUniqueIdentifier} is not supported`);
            return S_DatabaseManager_QueryDatabases_Stale;
        }

        const result = await executeDeduplicate(
            `query-databases-${origin.origin.uniqueIdentifier}`,
            () => origin.origin.getDatabaseManager().queryDatabases({}),
            actionIdentifier,
            recordIdentifier,
        );

        this._putCache(cacheKey, result);
        return result;
    }

    public async wideOriginGetDatabases(
        actionIdentifier: string,
        recordIdentifier: string,
    ): Promise<ImbricateDatabaseManagerQueryDatabasesOutcome[]> {

        const databases: ImbricateDatabaseManagerQueryDatabasesOutcome[] = [];

        for (const origin of this._origins) {

            const supportGetOrigin = checkImbricateOriginFeatureSupported(
                origin.origin.supportedFeatures,
                IMBRICATE_ORIGIN_FEATURE.ORIGIN_DATABASE_MANAGER,
            );

            if (!supportGetOrigin) {
                continue;
            }

            const originDatabases = await this.getDatabases(
                origin.origin.uniqueIdentifier,
                actionIdentifier,
                recordIdentifier,
            );
            databases.push(originDatabases);
        }

        return databases;
    }

    public async wideOriginGetDatabase(
        databaseUniqueIdentifier: string,
        actionIdentifier: string,
        recordIdentifier: string,
    ): Promise<ImbricateDatabaseManagerGetDatabaseOutcome> {

        const cacheKey = buildWideOriginDatabasesCacheKey(databaseUniqueIdentifier);
        const cache = this._retrieveCache<ImbricateDatabaseManagerGetDatabaseOutcome>(cacheKey);

        if (cache !== S_NoCache) {

            this._submitCachedAction(recordIdentifier);
            return cache;
        }

        const databases = await this.wideOriginGetDatabases(
            actionIdentifier,
            recordIdentifier,
        );

        for (const database of databases) {

            if (typeof database === "symbol") {
                continue;
            }

            const targetDatabase = database.databases.find((
                database: IImbricateDatabase,
            ) => database.uniqueIdentifier === databaseUniqueIdentifier);

            if (!targetDatabase) {
                continue;
            }

            const result = {
                database: targetDatabase,
            };

            this._putCache(cacheKey, result);
            return result;
        }

        return S_DatabaseManager_GetDatabase_NotFound;
    }

    public async wideOriginGetDocument(
        databaseUniqueIdentifier: string,
        documentUniqueIdentifier: string,
        actionIdentifier: string,
        recordIdentifier: string,
    ): Promise<ImbricateDatabaseGetDocumentOutcome> {

        const database = await this.wideOriginGetDatabase(
            databaseUniqueIdentifier,
            actionIdentifier,
            recordIdentifier,
        );

        if (typeof database === "symbol") {
            return S_Database_GetDocument_NotFound;
        }

        return await executeDeduplicate(
            `get-document-${documentUniqueIdentifier}`,
            () => database.database.getDocument(documentUniqueIdentifier),
            actionIdentifier,
            recordIdentifier,
        );
    }

    public async getDocument(
        originUniqueIdentifier: string,
        databaseUniqueIdentifier: string,
        documentUniqueIdentifier: string,
        actionIdentifier: string,
        recordIdentifier: string,
    ): Promise<ImbricateDatabaseGetDocumentOutcome> {

        const cacheKey = buildDocumentCacheKey(
            originUniqueIdentifier,
            databaseUniqueIdentifier,
            documentUniqueIdentifier,
        );
        const cache = this._retrieveCache<ImbricateDatabaseGetDocumentOutcome>(cacheKey);

        if (cache !== S_NoCache) {

            this._submitCachedAction(recordIdentifier);
            return cache;
        }

        const database = await this.getDatabase(
            originUniqueIdentifier,
            databaseUniqueIdentifier,
            actionIdentifier,
            recordIdentifier,
        );

        if (typeof database === "symbol") {
            this._putCache(cacheKey, S_Database_GetDocument_NotFound);
            return S_Database_GetDocument_NotFound;
        }

        const document = await executeDeduplicate(
            `get-document-${documentUniqueIdentifier}`,
            () => database.database.getDocument(documentUniqueIdentifier),
            actionIdentifier,
            recordIdentifier,
        );

        this._putCache(cacheKey, document);
        return document;
    }

    public async queryDocuments(
        originUniqueIdentifier: string,
        databaseUniqueIdentifier: string,
        query: ImbricateDocumentQuery,
        actionIdentifier: string,
        recordIdentifier: string,
    ): Promise<ImbricateDatabaseQueryDocumentsOutcome> {

        const cacheKey = buildQueryDocumentsCacheKey(
            databaseUniqueIdentifier,
            query,
        );
        const cache = this._retrieveCache<ImbricateDatabaseQueryDocumentsOutcome>(cacheKey);

        if (cache !== S_NoCache) {

            this._submitCachedAction(recordIdentifier);
            return cache;
        }

        const database = await this.getDatabase(
            originUniqueIdentifier,
            databaseUniqueIdentifier,
            actionIdentifier,
            recordIdentifier,
        );

        if (typeof database === "symbol") {
            this._putCache(cacheKey, S_Database_QueryDocuments_Stale);
            return S_Database_QueryDocuments_Stale;
        }

        const result = await executeDeduplicate(
            `query-documents-${databaseUniqueIdentifier}`,
            () => database.database.queryDocuments(query),
            actionIdentifier,
            recordIdentifier,
        );

        this._putCache(cacheKey, result);
        return result;
    }

    public async wideOriginQueryDocuments(
        databaseUniqueIdentifier: string,
        query: ImbricateDocumentQuery,
        actionIdentifier: string,
        recordIdentifier: string,
    ): Promise<ImbricateDatabaseQueryDocumentsOutcome> {

        const cacheKey = buildWideOriginQueryDocumentsCacheKey(
            databaseUniqueIdentifier,
            query,
        );
        const cache = this._retrieveCache<ImbricateDatabaseQueryDocumentsOutcome>(cacheKey);

        if (cache !== S_NoCache) {

            this._submitCachedAction(recordIdentifier);
            return cache;
        }

        const databases = await this.wideOriginGetDatabases(
            actionIdentifier,
            recordIdentifier,
        );

        for (const database of databases) {

            if (typeof database === "symbol") {
                continue;
            }

            const targetDatabase = database.databases.find((
                database: IImbricateDatabase,
            ) => database.uniqueIdentifier === databaseUniqueIdentifier);

            if (!targetDatabase) {
                continue;
            }

            const queryResult = await executeDeduplicate(
                `query-documents-${databaseUniqueIdentifier}`,
                () => targetDatabase.queryDocuments(query),
                actionIdentifier,
                recordIdentifier,
            );

            this._putCache(cacheKey, queryResult);
            return queryResult;
        }

        this._putCache(cacheKey, S_Database_QueryDocuments_Stale);
        return S_Database_QueryDocuments_Stale;
    }

    public invalidateWideOriginQueryDocumentsCache(
        databaseUniqueIdentifier: string,
        query: ImbricateDocumentQuery,
    ): void {

        const cacheKey = buildWideOriginQueryDocumentsCacheKey(
            databaseUniqueIdentifier,
            query,
        );
        this._invalidateCache(cacheKey);
    }

    public async getText(
        originUniqueIdentifier: string,
        textUniqueIdentifier: string,
        actionIdentifier: string,
        recordIdentifier: string,
    ): Promise<ImbricateTextManagerGetTextOutcome> {

        const origin = this.getOrigin(
            originUniqueIdentifier,
            actionIdentifier,
            recordIdentifier,
        );

        if (!origin) {
            return S_TextManager_GetText_NotFound;
        }

        return await executeDeduplicate(
            `get-text-${textUniqueIdentifier}`,
            () => origin.origin
                .getTextManager()
                .getText(textUniqueIdentifier),
            actionIdentifier,
            recordIdentifier,
        );
    }

    public async getTextContent(
        originUniqueIdentifier: string,
        textUniqueIdentifier: string,
        actionIdentifier: string,
        recordIdentifier: string,
    ): Promise<ImbricateTextGetContentOutcome> {

        const text = await this.getText(
            originUniqueIdentifier,
            textUniqueIdentifier,
            actionIdentifier,
            recordIdentifier,
        );

        if (typeof text === "symbol") {
            return S_Text_GetContent_NotFound;
        }

        return await executeDeduplicate(
            `get-text-content-${textUniqueIdentifier}`,
            () => text.text.getContent(),
            actionIdentifier,
            recordIdentifier,
        );
    }

    public async getStaticUri(
        originUniqueIdentifier: string,
        staticUniqueIdentifier: string,
        actionIdentifier: string,
        recordIdentifier: string,
    ): Promise<ImbricateStaticManagerGetStaticUriOutcome> {

        const origin = this.getOrigin(
            originUniqueIdentifier,
            actionIdentifier,
            recordIdentifier,
        );

        if (!origin) {
            return S_StaticManager_GetStaticUri_NotFound;
        }

        return await executeDeduplicate(
            `get-static-uri-${staticUniqueIdentifier}`,
            () => origin.origin.getStaticManager().getStaticUri(staticUniqueIdentifier),
            actionIdentifier,
            recordIdentifier,
        );
    }

    private _invalidateCache(
        key: string,
    ): void {

        const cacheLogger = logger.forkScope("Cache");
        cacheLogger.debug(`(INVALIDATE) ${key}`);
        this._cache.delete(key);
    }

    private _retrieveCache<T>(
        key: string,
    ): T | typeof S_NoCache {

        const cacheLogger = logger.forkScope("Cache");

        // 5 minutes
        const expireTime: number = 1000 * 60 * 5;

        const cacheObject = this._cache.get(key);
        if (!cacheObject) {

            cacheLogger.debug(`(MISS) ${key}`);
            return S_NoCache;
        }

        if (cacheObject.storedAt + expireTime < Date.now()) {

            cacheLogger.debug(`(EXPIRED) ${key}`);

            this._invalidateCache(key);
            return S_NoCache;
        }

        cacheLogger.debug(`(HIT) ${key}`);
        return cacheObject.cache;
    }

    private _putCache<T>(
        key: string,
        cache: T,
    ): void {

        this._cache.set(key, {
            cache,
            storedAt: Date.now(),
        });
    }

    private _submitCachedAction(
        recordIdentifier: string,
    ): void {

        const recentActions: RecentActions<WORKER_WEB_WORKER> =
            RecentActions.getInstance<WORKER_WEB_WORKER>();

        recentActions.setCached(recordIdentifier, true);
    }
}
