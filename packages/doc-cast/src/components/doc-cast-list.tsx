/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Components
 * @description Doc Cast List
 */

import React from "react";
import { DOC_CAST_SOURCE_TYPE, DocCastSource } from "../types/source";
import { hashDocCastSource } from "../util/hash-source";
import { DocCastSourceItem } from "./doc-cast-source-item";

export type DocCastListProps = {

    readonly sources: DocCastSource<DOC_CAST_SOURCE_TYPE>[];
};

export const DocCastList: React.FC<DocCastListProps> = (
    props: DocCastListProps,
) => {

    return (<div>
        {props.sources.map((
            source: DocCastSource<DOC_CAST_SOURCE_TYPE>,
        ) => {

            const hash: string = hashDocCastSource(source);

            return (<div
                key={hash}
            >
                <DocCastSourceItem
                    source={source}
                />
            </div>);
        })}
    </div>);
};
