/**
 * <AUTHOR>
 * @namespace Common_Components_HeaderGroup
 * @description Group
 */

import { COMMON_PROFILE } from "@/common/internationalization/profile";
import { SudooFormat } from "@sudoo/internationalization";
import { IconType } from "react-icons";
import { FaDatabase } from "react-icons/fa";
import { IoIosDocument } from "react-icons/io";
import { RiCameraLensFill } from "react-icons/ri";
import { HEADER_GROUP_GROUP } from "./types";
import { FaHashnode } from "react-icons/fa6";

export const getHeaderGroupIcon = (
    group: HEADER_GROUP_GROUP,
): IconType => {

    // HEADER_GROUP_GROUP SWITCH
    switch (group) {

        case HEADER_GROUP_GROUP.DATABASE: {

            return FaDatabase;
        }
        case HEADER_GROUP_GROUP.DOCUMENT: {

            return IoIosDocument;
        }
        case HEADER_GROUP_GROUP.PROPERTY: {

            return FaHashnode;
        }
        case HEADER_GROUP_GROUP.LENS: {

            return RiCameraLensFill;
        }
    }
};

export const getHeaderGroupTitle = (
    group: HEADER_GROUP_GROUP,
    format: SudooFormat<COMMON_PROFILE>,
): string => {

    // HEADER_GROUP_GROUP SWITCH
    switch (group) {

        case HEADER_GROUP_GROUP.DATABASE: {

            return format.get(COMMON_PROFILE.DATABASE);
        }
        case HEADER_GROUP_GROUP.DOCUMENT: {

            return format.get(COMMON_PROFILE.DOCUMENT);
        }
        case HEADER_GROUP_GROUP.PROPERTY: {

            return format.get(COMMON_PROFILE.PROPERTY);
        }
        case HEADER_GROUP_GROUP.LENS: {

            return format.get(COMMON_PROFILE.LENS);
        }
    }
};
