/**
 * <AUTHOR>
 * @namespace Config_Util
 * @description Reset Configuration
 */

import { HUMMINGBIRD_MANAGED_CONFIG_ITEM, ManagedConfigController, clearRecent } from "@imbricate-hummingbird/configuration";
import { clearOriginStorageInstance } from "../../origin/origin-storage";

export const resetConfigurationStorage = async (): Promise<void> => {

    ManagedConfigController.getInstance()
        .deleteManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.LENS_CONFIG);

    clearOriginStorageInstance();
    clearRecent();
};
