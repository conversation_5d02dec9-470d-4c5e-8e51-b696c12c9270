/**
 * <AUTHOR>
 * @namespace Origin_Internationalization_Locale
 * @description En-US
 */

import { ORIGIN_PROFILE } from "../profile";

export const enUSOriginProfile: Record<ORIGIN_PROFILE, string> = {

    [ORIGIN_PROFILE.NOT_SURE_WHICH_TYPE_OF_ORIGIN_TO_USE]: "Not sure which type of origin to use?",
    [ORIGIN_PROFILE.NOT_SURE_WHICH_TYPE_OF_ORIGIN_TO_USE_DESCRIPTION]: "Imbricate supports multiple data sources, allowing you to place your data in databases, file systems, memory, cloud, or anywhere else. If you are not sure which type of data source to use, please refer to",
    [ORIGIN_PROFILE.VIEW_DOCUMENT]: "Imbricate Document",
};
