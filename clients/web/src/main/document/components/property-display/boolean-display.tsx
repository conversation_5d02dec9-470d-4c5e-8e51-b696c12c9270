/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyDisplay
 * @description Boolean Display
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UICheckbox } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";

export type DocumentBooleanDisplayProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BOOLEAN>;
};

export const DocumentBooleanDisplay: FC<DocumentBooleanDisplayProps> = (
    props: DocumentBooleanDisplayProps,
) => {

    return (<UICheckbox
        isSelected={props.property.value}
        isReadOnly
    />);
};
