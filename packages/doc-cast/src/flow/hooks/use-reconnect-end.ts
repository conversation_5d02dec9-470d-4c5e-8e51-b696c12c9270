/**
 * <AUTHOR>
 * @namespace Flow_Hooks
 * @description Use Reconnect End
 */

import { Edge } from "@xyflow/react";
import { useCallback } from "react";

export const useDocCastFlowReconnectEnd = (
    setParsedEdges: React.Dispatch<React.SetStateAction<Edge[]>>,
    edgeReconnectSuccessful: React.RefObject<boolean>,
) => {

    const onReconnectEnd = useCallback((
        _event: MouseEvent | TouchEvent,
        edge: Edge,
    ) => {

        if (!edgeReconnectSuccessful.current) {
            setParsedEdges((previousEdges: Edge[]) => {
                return previousEdges.filter((e) => {
                    return e.id !== edge.id;
                });
            });
        }

        edgeReconnectSuccessful.current = true;
    }, []);

    return onReconnectEnd;
};
