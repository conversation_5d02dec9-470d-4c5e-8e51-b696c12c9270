/**
 * <AUTHOR>
 * @namespace Config_Panels_Wrapper
 * @description Markdown Panel Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const ConfigMarkdownPanel = LazyLoadComponent(
    () => import("../panels/markdown-panel"),
    "Config Markdown Panel",
);

export type ConfigMarkdownPanelWrapperProps = {
};

export const ConfigMarkdownPanelWrapper: FC<ConfigMarkdownPanelWrapperProps> = (
    _props: ConfigMarkdownPanelWrapperProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Config Markdown Panel"
            fullHeight
        />}
    >
        <ConfigMarkdownPanel />
    </React.Suspense>);
};
