/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyCard
 * @description Property Cards
 */

import { DebugInformation } from "@/debug/components/debug-information";
import { useWideOrigin } from "@/main/origin/hooks/use-wide-origin";
import { useEditableDrafts } from "@/main/property/hooks/use-editable-drafts";
import { submitPropertyDrafts } from "@/main/property/utils/submit-drafts";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { LoadingWrapper } from "@imbricate-hummingbird/react-components";
import { TransferDocument, TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIButton } from "@imbricate-hummingbird/ui";
import { IImbricateDocument, IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchema, getImbricateDefaultValueOfProperty } from "@imbricate/core";
import React from "react";
import { IoSaveSharp } from "react-icons/io5";
import { MdDelete } from "react-icons/md";
import { UseDocumentResponseSymbol, useOldDocument } from "../../hooks/use-document";
import { useDocumentFormat } from "../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../internationalization/profile";
import { DocumentPropertyCard } from "./property-card";

export type DocumentPropertyCardsProps = {

    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;

    readonly schema: ImbricateDatabaseSchema;
    readonly document: TransferDocument;
};

export const DocumentPropertyCards: React.FC<DocumentPropertyCardsProps> = (
    props: DocumentPropertyCardsProps,
) => {

    const documentFormat = useDocumentFormat();

    const propertyDrafts = useEditableDrafts(props.document);

    const [edited, setEdited] = React.useState<boolean>(false);
    const [saving, setSaving] = React.useState<boolean>(false);

    const origin: ImbricateOriginObject | null =
        useWideOrigin(props.databaseUniqueIdentifier);

    const document: IImbricateDocument | UseDocumentResponseSymbol =
        useOldDocument(
            props.databaseUniqueIdentifier,
            props.documentUniqueIdentifier,
        );

    if (!origin
        || typeof document === "symbol") {

        return (<LoadingWrapper
            debugDescription="Property Cards"
        />);
    }

    if (typeof propertyDrafts === "symbol") {
        return null;
    }

    return (<div
        className="flex flex-col gap-2 py-4 pr-2"
    >
        <DebugInformation
            information={JSON.stringify(props)}
        />
        <DebugInformation
            information={JSON.stringify(propertyDrafts)}
        />
        {props.schema.properties.map((propertySchema) => {

            const propertyDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> =
                propertyDrafts.mergedDraftRecord[propertySchema.propertyIdentifier]
                ?? getImbricateDefaultValueOfProperty(propertySchema.propertyType);

            return (<DocumentPropertyCard
                key={propertySchema.propertyIdentifier}
                originUniqueIdentifier={origin.origin.uniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                schema={propertySchema}
                propertyDraft={propertyDraft}
                deleteProperty={() => {
                    propertyDrafts.deleteDraft(propertySchema.propertyIdentifier);
                }}
                updateProperty={(
                    value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>,
                ) => {
                    setEdited(true);
                    propertyDrafts.addEditedDraft(value);
                }}
            />);
        })}
        {edited && <div
            className="flex gap-1"
        >
            <UIButton
                startContent={<IoSaveSharp />}
                variant="solid"
                color="primary"
                isLoading={saving}
                onPress={async () => {

                    setSaving(true);
                    await submitPropertyDrafts(
                        origin.origin.uniqueIdentifier,
                        props.databaseUniqueIdentifier,
                        props.documentUniqueIdentifier,
                        propertyDrafts.editedDrafts,
                    );

                    setSaving(false);
                    setEdited(false);
                }}
            >
                {documentFormat.get(
                    DOCUMENT_PROFILE.SAVE_CHANGES,
                )}
            </UIButton>
            <UIButton
                variant="flat"
                color="danger"
                isIconOnly
                isLoading={saving}
                onPress={async () => {

                    propertyDrafts.resetEdits();
                    setEdited(false);
                }}
            >
                <MdDelete
                    className="text-large"
                />
            </UIButton>
        </div>}
    </div>);
};
