/**
 * <AUTHOR>
 * @package Web
 * @namespace Document_Components_PropertyValue
 * @description Json Value
 */

import { openEditWindow, openViewWindow } from "@imbricate-hummingbird/navigation-core";
import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIButton } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { FaEye } from "react-icons/fa6";
import { MdAddCircleOutline, MdEdit } from "react-icons/md";
import { PiKeyholeFill } from "react-icons/pi";
import { CommonCopyItem } from "../../../common/components/copy-item";

export type DocumentJsonValueProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.JSON>;
    readonly updateProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.JSON>) => void;
};

export const DocumentJsonValue: FC<DocumentJsonValueProps> = (
    props: DocumentJsonValueProps,
) => {

    const fileAlreadyExists = props.property.value?.length > 0;

    if (fileAlreadyExists) {

        return (<div
            className="flex flex-col gap-1 w-full"
        >
            <div className="flex gap-1 items-center">
                <CommonCopyItem
                    startContent="JSON Document Identifier"
                    prefix={<PiKeyholeFill />}
                    content={props.property.value}
                />
            </div>
            <UIButton
                isFullWidth
                startContent={<MdEdit />}
                color="secondary"
                onPress={() => {

                    if (
                        !props.originUniqueIdentifier
                        || !props.databaseUniqueIdentifier
                        || !props.documentUniqueIdentifier) {
                        throw new Error("[Imbricate] Database or document unique identifier not found");
                    }

                    openEditWindow(
                        props.originUniqueIdentifier,
                        props.databaseUniqueIdentifier,
                        props.documentUniqueIdentifier,
                        props.propertyKey,
                    );
                }}
            >
                Edit JSON Document
            </UIButton>
            <UIButton
                isFullWidth
                startContent={<FaEye />}
                onPress={() => {

                    if (
                        !props.originUniqueIdentifier
                        || !props.databaseUniqueIdentifier
                        || !props.documentUniqueIdentifier) {
                        throw new Error("[Imbricate] Database or document unique identifier not found");
                    }

                    openViewWindow(
                        props.originUniqueIdentifier,
                        props.databaseUniqueIdentifier,
                        props.documentUniqueIdentifier,
                        props.propertyKey,
                    );
                }}
            >
                View JSON Document
            </UIButton>
        </div>);
    }

    return (<UIButton
        isFullWidth
        startContent={<MdAddCircleOutline />}
        color="primary"
        variant="flat"
        onPress={() => {

            if (!props.originUniqueIdentifier
                || !props.databaseUniqueIdentifier
                || !props.documentUniqueIdentifier) {
                throw new Error("[Imbricate] Database or document unique identifier not found");
            }

            openEditWindow(
                props.originUniqueIdentifier,
                props.databaseUniqueIdentifier,
                props.documentUniqueIdentifier,
                props.propertyKey,
            );
        }}
    >
        Create JSON Document
    </UIButton>);
};
