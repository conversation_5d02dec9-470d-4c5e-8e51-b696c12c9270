/**
 * <AUTHOR>
 * @namespace Origin_Components
 * @description Origin Card
 */

import { getRouteOriginView } from "@imbricate-hummingbird/navigation-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { StyledCard } from "@imbricate-hummingbird/react-components";
import { UIButton, UIButtonLink, UICardBody, UICardFooter, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { IMBRICATE_ORIGIN_FEATURE } from "@imbricate/core";
import { FC } from "react";
import { FaArrowRight, FaUnlink } from "react-icons/fa";
import { OriginActionDropdown } from "./origin-action/origin-action-dropdown";

export type OriginCardProps = {

    readonly origin: ImbricateOriginObject;
};

export const OriginCard: FC<OriginCardProps> = (
    props: OriginCardProps,
) => {

    return (
        <StyledCard
            key={props.origin.origin.uniqueIdentifier}
        >
            <UICardHeader
                className="flex"
            >
                <div
                    className="font-bold font-mono flex-1"
                >
                    {props.origin.originName}
                </div>
                <div>
                    <UIButton
                        startContent={<FaUnlink />}
                        variant="flat"
                        color="danger"
                        isDisabled
                    >
                        Unlink
                    </UIButton>
                </div>
            </UICardHeader>
            <UIDivider />
            <UICardBody
                className="grid grid-cols-[1fr,3fr] gap-2 w-full"
            >
                <div
                    className="text-end font-bold"
                >
                    Origin Type
                </div>
                <div
                    className="break-all font-mono"
                >
                    {props.origin.originInstance.type}
                </div>
                <div
                    className="text-end font-bold"
                >
                    Supported Feature
                </div>
                <div
                    className="break-all font-mono"
                >
                    {props.origin.origin.supportedFeatures.map((feature: IMBRICATE_ORIGIN_FEATURE) => {
                        return (<div
                            key={feature}
                        >
                            {feature}
                        </div>);
                    })}
                </div>
                <div
                    className="text-end font-bold"
                >
                    Origin Unique Identifier
                </div>
                <div
                    className="break-all font-mono"
                >
                    {props.origin.origin.uniqueIdentifier}
                </div>
            </UICardBody>
            <UIDivider />
            <UICardFooter
                className="flex"
            >
                <OriginActionDropdown
                    origin={props.origin}
                />
                <UISpacer
                    isFlexFill
                />
                <UIButtonLink
                    href={getRouteOriginView(props.origin.origin.uniqueIdentifier)}
                    startContent={<FaArrowRight />}
                    color="primary"
                    variant="flat"
                >
                    Origin Databases
                </UIButtonLink>
            </UICardFooter>
        </StyledCard>
    );
}; 
