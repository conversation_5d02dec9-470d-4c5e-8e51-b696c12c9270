/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Modals
 * @description Modal Provider
 */

import { ReactFlowProvider } from "@xyflow/react";
import React, { useCallback } from "react";
import { createPortal } from "react-dom";
import { DocCastModalContext, DocCastModalContextProps } from "../contexts/doc-cast-context";
import { DOC_CAST_SOURCE_TYPE, DocCastSource } from "../types/source";
import { combineSources } from "../util/combine-sources";
import { DocCastModal } from "./modal";

export type DocCastModalProviderProps = {

    readonly children: React.ReactNode;
};

export const DocCastModalProvider: React.FC<DocCastModalProviderProps> = (
    props: DocCastModalProviderProps,
) => {

    const [open, setOpen] = React.useState(false);

    const [sources, setSources] =
        React.useState<DocCastSource<DOC_CAST_SOURCE_TYPE>[]>([]);

    const onOpenChangeCallback = useCallback((
        open: boolean,
    ) => {

        setOpen(open);
    }, []);

    const addSourcesCallback = useCallback((
        sources: DocCastSource<DOC_CAST_SOURCE_TYPE>[],
    ) => {

        setSources((
            previousSources: DocCastSource<DOC_CAST_SOURCE_TYPE>[],
        ) => {

            return combineSources(
                previousSources,
                sources,
            );
        });
        setOpen(true);
    }, []);

    const clearSourcesCallback = useCallback(() => {

        setSources([]);
    }, []);

    const context: DocCastModalContextProps = {

        open,
        onOpenChange: onOpenChangeCallback,
        addSources: addSourcesCallback,
        clearSources: clearSourcesCallback,
        sources,
    };

    return (<DocCastModalContext.Provider
        value={context}
    >
        {createPortal(
            (<ReactFlowProvider>
                <DocCastModal
                    context={context}
                />
            </ReactFlowProvider>),
            document.body,
        )}
        {props.children}
    </DocCastModalContext.Provider>);
};
