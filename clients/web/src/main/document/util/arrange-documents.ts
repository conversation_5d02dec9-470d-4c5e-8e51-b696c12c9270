/**
 * <AUTHOR>
 * @namespace Document_Utils
 * @description Arrange Documents
 */

import { TransferDocument, TransferDocumentProperty, TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { IImbricateDatabase, IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaProperty, ImbricatePropertyValueObject } from "@imbricate/core";
import { rootLogger } from "../../log/logger";
import { DocumentEditingController } from "../controller/editing-controller";

const logger = rootLogger.fork({
    scopes: [
        "Document",
        "Util",
        "ArrangeDocuments",
    ],
});

type ArrangeDocumentsResultItemFloatingProperty = {

    readonly propertyIdentifier: string;
    readonly propertyValue: ImbricatePropertyValueObject<IMBRICATE_PROPERTY_TYPE>;
};

export type ArrangeDocumentsResultItem = {

    readonly document: TransferDocument;
    readonly propertyValueMap: Record<string, TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>>;
    readonly floatingProperties: ArrangeDocumentsResultItemFloatingProperty[];
    readonly editing: boolean;
};

export type ArrangeDocumentsResult = {

    readonly propertyIdentifiers: string[];
    readonly propertyNameMap: Record<string, string>;
    readonly propertyTypesMap: Record<string, IMBRICATE_PROPERTY_TYPE>;
    readonly schemaMap: Record<string, ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>>;
    readonly documents: ArrangeDocumentsResultItem[];
    readonly creatingDocuments: Array<[string, TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[]]>;

    readonly primaryPropertyIdentifier?: string;
};

export const arrangeDocuments = (
    database: IImbricateDatabase,
    documents: TransferDocument[],
    editingController: DocumentEditingController,
    displayedProperties: string[],
): ArrangeDocumentsResult => {

    const allPropertyIdentifiers: string[] = database.schema.properties
        .map((property) => property.propertyIdentifier);

    const propertyIdentifiers: string[] = displayedProperties.length === 0
        ? allPropertyIdentifiers
        : allPropertyIdentifiers
            .filter((propertyIdentifier) => {
                return displayedProperties.includes(propertyIdentifier);
            });

    const propertyNameMap: Record<string, string> = database.schema.properties
        .reduce((previous: Record<string, string>, current) => {
            return {
                ...previous,
                [current.propertyIdentifier]: current.propertyName,
            };
        }, {});

    const propertyTypesMap: Record<string, IMBRICATE_PROPERTY_TYPE> = database.schema.properties
        .reduce((previous: Record<string, IMBRICATE_PROPERTY_TYPE>, current) => {
            return {
                ...previous,
                [current.propertyIdentifier]: current.propertyType,
            };
        }, {});

    const schemaMap: Record<string, ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>> = database.schema.properties.reduce((previous, current) => {
        return {
            ...previous,
            [current.propertyIdentifier]: current,
        };
    }, {});

    const documentsResult: ArrangeDocumentsResultItem[] = documents
        .map((document): ArrangeDocumentsResultItem => {

            const properties = document.properties;

            if (typeof properties === "symbol") {

                logger.error("Document properties is not an object", {
                    document,
                });
                throw new Error("[Imbricate] Document properties is not an object");
            }

            const propertyValueMap: Record<string, TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>> = database.schema.properties
                .reduce((
                    previous: Record<string, TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>>,
                    current: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>,
                ) => {

                    const targetProperty: TransferDocumentProperty | undefined =
                        properties.find((property) => {
                            return property.propertyKey === current.propertyIdentifier;
                        });

                    if (typeof targetProperty === "undefined") {
                        return previous;
                    }

                    const draft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> = {
                        key: current.propertyIdentifier,
                        type: current.propertyType,
                        value: targetProperty.propertyValue,
                        variant: current.propertyVariant,
                    };

                    return {
                        ...previous,
                        [current.propertyIdentifier]: draft,
                    };
                }, {});

            const floatingProperties: ArrangeDocumentsResultItemFloatingProperty[] =
                properties.filter((
                    property: TransferDocumentProperty,
                ) => {
                    return !allPropertyIdentifiers.includes(property.propertyKey);
                }).map((
                    property: TransferDocumentProperty,
                ) => {

                    const floatingProperty: ArrangeDocumentsResultItemFloatingProperty = {
                        propertyIdentifier: property.propertyKey,
                        propertyValue: property.propertyValue,
                    };

                    return floatingProperty;
                });

            return {
                document,
                propertyValueMap,
                floatingProperties,
                editing: editingController.isDocumentEditing(document),
            };
        });

    const creatingDocuments: Array<[string, TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[]]> =
        editingController.getCreatingDocuments();

    const primaryPropertyIdentifier: string | undefined =
        database.schema.properties.find((
            property: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>,
        ) => {
            return property.isPrimaryKey;
        })?.propertyIdentifier;

    const response: ArrangeDocumentsResult = {

        propertyIdentifiers,
        propertyNameMap,
        propertyTypesMap,
        schemaMap,
        documents: documentsResult,
        creatingDocuments,
        primaryPropertyIdentifier,
    };

    return response;
};
