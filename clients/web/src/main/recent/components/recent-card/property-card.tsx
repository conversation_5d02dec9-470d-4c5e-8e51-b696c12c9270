/**
 * <AUTHOR>
 * @namespace Recent_Components_RecentCard
 * @description Property Card
 */

import { RECENT_TYPE, RecentItem } from "@imbricate-hummingbird/configuration";
import { getPropertyIcon } from "@imbricate-hummingbird/react-components";
import { U<PERSON><PERSON>Header } from "@imbricate-hummingbird/ui";
import { IImbricateDatabase } from "@imbricate/core";
import React, { FC } from "react";
import { FaDatabase } from "react-icons/fa";
import { MdKeyboardDoubleArrowRight } from "react-icons/md";
import { S_UseDatabaseLoading, UseDatabaseResponseSymbol, useDatabase } from "../../../database/hooks/use-database";
import { usePrimaryProperty } from "../../../property/hooks/use-primary-property";
import { useProperty } from "../../../property/hooks/use-property";
import { S_UsePropertyLoading } from "../../../property/types/use-property";
import { RecentCardLoading } from "./loading-card";
import { RecentCardUnknown } from "./unknown-card";

export type RecentCardPropertyProps = {

    readonly item: RecentItem<RECENT_TYPE.PROPERTY>;
};

export const RecentCardProperty: FC<RecentCardPropertyProps> = (
    props: RecentCardPropertyProps,
) => {

    const property = useProperty(
        props.item.payload.databaseUniqueIdentifier,
        props.item.payload.documentUniqueIdentifier,
        props.item.payload.propertyUniqueIdentifier,
    );

    const primaryProperty = usePrimaryProperty(
        props.item.payload.databaseUniqueIdentifier,
        props.item.payload.documentUniqueIdentifier,
    );

    const database: IImbricateDatabase | UseDatabaseResponseSymbol =
        useDatabase(props.item.payload.databaseUniqueIdentifier);

    if (property === S_UsePropertyLoading
        || database === S_UseDatabaseLoading) {

        return (<RecentCardLoading />);
    }

    if (typeof property === "symbol"
        || typeof database === "symbol") {

        return (<RecentCardUnknown
            item={props.item}
        />);
    }

    const primaryValueText: string = typeof primaryProperty !== "symbol"
        ? String(primaryProperty.propertyValue)
        : props.item.payload.propertyUniqueIdentifier;

    return (<React.Fragment>
        <UICardHeader
            className="flex flex-row items-center gap-3"
        >
            {getPropertyIcon(
                property.schemaProperty.propertyType,
                "text-2xl",
            )}
            <div
                className="flex-1 flex flex-col items-start text-left"
            >
                <div>
                    {primaryValueText}
                </div>
                <div
                    className="text-xs text-gray-600 dark:text-gray-400 flex gap-1 items-center"
                >
                    <FaDatabase />
                    {database.databaseName}
                    <MdKeyboardDoubleArrowRight
                        className="text-medium text-gray-600"
                    />
                    {property.schemaProperty.propertyName}
                </div>
            </div>
            <div
                className="self-end text-xs text-gray-600 dark:text-gray-400"
            >
                {new Date(props.item.accessedAt).toLocaleString()}
            </div>
        </UICardHeader>
    </React.Fragment>);
};
