/**
 * <AUTHOR>
 * @namespace Edit_Components
 * @description Edit Title Logo
 */

import { getHeaderGroupOriginData } from "@/common/components/header-group/origin-data";
import { HEADER_GROUP_GROUP } from "@/common/components/header-group/types";
import { useCommonFormat } from "@/common/internationalization/hook";
import { HUMMINGBIRD_ANIMATION_LEVEL, HUMMINGBIRD_PREFERENCE_ITEM, usePreferenceItem } from "@imbricate-hummingbird/configuration";
import { OptimusPrime } from "@imbricate-hummingbird/react-components";
import { UIPopover } from "@imbricate-hummingbird/ui";
import { FC } from "react";

export type EditTitleLogoProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;
    readonly propertyUniqueIdentifier: string;
};

export const EditTitleLogo: FC<EditTitleLogoProps> = (
    props: EditTitleLogoProps,
) => {

    const commonFormat = useCommonFormat();

    const animationLevel = usePreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.APPLICATION_APPEARANCE_ANIMATION_LEVEL,
    );

    const enableAnimation = animationLevel.preference === HUMMINGBIRD_ANIMATION_LEVEL.FULL;

    return (<div
        className="flex items-center min-h-0 min-w-0"
    >
        <UIPopover
            contentClassName="p-0"
            trigger={<button
                className="p-0 m-0 hover:cursor-pointer"
            >
                <OptimusPrime
                    enableAnimation={enableAnimation}
                    size="tiny"
                    sideRows={0}
                    sideColumns={0}
                    transitionBaseline={100}
                />
            </button>}
        >
            {getHeaderGroupOriginData({
                group: HEADER_GROUP_GROUP.PROPERTY,
                payload: {
                    originUniqueIdentifier: props.originUniqueIdentifier,
                    databaseUniqueIdentifier: props.databaseUniqueIdentifier,
                    documentUniqueIdentifier: props.documentUniqueIdentifier,
                    propertyUniqueIdentifier: props.propertyUniqueIdentifier,
                },
                commonFormat,
            })}
        </UIPopover>
    </div>);
};
