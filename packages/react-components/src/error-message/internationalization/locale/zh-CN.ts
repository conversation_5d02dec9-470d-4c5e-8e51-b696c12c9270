/**
 * <AUTHOR>
 * @package React Components
 * @namespace ErrorMessage_Internationalization_Locale
 * @description Zh-CN
 */

import { REACT_COMPONENTS_ERROR_MESSAGE_PROFILE } from "../profile";

export const zhCNReactComponentsErrorMessageProfile: Record<REACT_COMPONENTS_ERROR_MESSAGE_PROFILE, string> = {

    [REACT_COMPONENTS_ERROR_MESSAGE_PROFILE.DEFAULT_ERROR_MESSAGE]: "应用程序出现问题。请稍后再试。或者检查故障排除指南。",
    [REACT_COMPONENTS_ERROR_MESSAGE_PROFILE.DEFAULT_ERROR_TITLE]: "发生错误",
    [REACT_COMPONENTS_ERROR_MESSAGE_PROFILE.TROUBLESHOOT_GUIDE]: "故障排除指南",
};
