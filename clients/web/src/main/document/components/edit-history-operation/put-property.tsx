/**
 * <AUTHOR>
 * @namespace Document_Components_EditHistoryOperation
 * @description Put Property
 */

import { UITooltip } from "@imbricate-hummingbird/ui";
import { DocumentEditOperation, IMBRICATE_DOCUMENT_EDIT_TYPE, IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchema, ImbricateDatabaseSchemaProperty } from "@imbricate/core";
import { FC } from "react";
import { FaCircleRight } from "react-icons/fa6";
import { DocumentPropertyDisplayContent } from "../property-card/property-display-content";

export type DocumentEditRecordsPutPropertyOperationProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;

    readonly schema: ImbricateDatabaseSchema;
    readonly operation: DocumentEditOperation<IMBRICATE_DOCUMENT_EDIT_TYPE.PUT_PROPERTY>;
};

export const DocumentEditRecordsPutPropertyOperation: FC<DocumentEditRecordsPutPropertyOperationProps> = (
    props: DocumentEditRecordsPutPropertyOperationProps,
) => {

    const schemaProperty = props.schema.properties.find((
        property: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>,
    ) => {
        return property.propertyIdentifier === props.operation.value.key;
    });

    if (!schemaProperty) {

        return (<div
            className="flex gap-2 items-center w-full flex-wrap"
        >
            <div>
                {props.operation.value.key} <span
                    className="font-bold"
                >(Stale)</span>
            </div>
            <div>
                <FaCircleRight />
            </div>
            <div>
                {JSON.stringify(props.operation.value.value, null, 2)}
            </div>
        </div>);
    }

    const propertyName = schemaProperty.propertyName;

    return (<div
        className="flex gap-2 items-center w-full flex-wrap"
    >
        <UITooltip
            content={schemaProperty.propertyIdentifier}
        >
            <div
                className="font-bold"
            >
                {propertyName}
            </div>
        </UITooltip>
        <div>
            <FaCircleRight />
        </div>
        <div>
            <DocumentPropertyDisplayContent
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                schema={schemaProperty}
                property={props.operation.value}
            />
        </div>
    </div>);
};
