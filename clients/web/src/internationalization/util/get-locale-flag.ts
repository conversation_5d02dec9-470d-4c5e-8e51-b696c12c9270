/**
 * <AUTHOR>
 * @namespace Internationalization_Util
 * @description Get Locale Flag
 */

import { IETF_LOCALE } from "@sudoo/locale";

export const getLocaleFlag = (locale: IETF_LOCALE): string => {

    switch (locale) {
        case IETF_LOCALE.CHINESE_SIMPLIFIED:
            return "🇨🇳";
        case IETF_LOCALE.CHINESE_TRADITIONAL:
            return "🇹🇼";
        case IETF_LOCALE.ENGLISH_CANADA:
            return "🇨🇦";
        case IETF_LOCALE.ENGLISH_UNITED_STATES:
            return "🇺🇸";
        case IETF_LOCALE.ENGLISH_UNITED_KINGDOM:
            return "🇬🇧";
        case IETF_LOCALE.FRENCH_FRANCE:
            return "🇫🇷";
        case IETF_LOCALE.JAPANESE_JAPAN:
            return "🇯🇵";
        case IETF_LOCALE.KOREAN_KOREA:
            return "🇰🇷";
        case IETF_LOCALE.RUSSIAN_RUSSIA:
            return "🇷🇺";
        case IETF_LOCALE.SPANISH_MEXICO:
            return "🇲🇽";
        case IETF_LOCALE.SPANISH_SPAIN:
            return "🇪🇸";
        default:
            return "🇺🇳";
    }
};
