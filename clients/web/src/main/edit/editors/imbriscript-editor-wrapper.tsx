/**
 * <AUTHOR>
 * @namespace Edit_Editors
 * @description ImbriScript Editor Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import type { EditImbriscriptEditorProps } from "./imbriscript-editor";

const ImbriScriptEditorApplication = LazyLoadComponent(
    () => import("./imbriscript-editor"),
    "ImbriScript Editor Application",
);

export const ImbriScriptEditorWrapper: FC<EditImbriscriptEditorProps> = (
    props: EditImbriscriptEditorProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="ImbriScript Editor Application"
            fullHeight
        />}
    >
        <ImbriScriptEditorApplication
            {...props}
        />
    </React.Suspense>);
};
