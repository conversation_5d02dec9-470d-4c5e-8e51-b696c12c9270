/**
 * <AUTHOR>
 * @namespace Recent_Components_RecentCard
 * @description Loading Card
 */

import { UICardHeader, UISkeleton, UISpinner } from "@imbricate-hummingbird/ui";
import { FC } from "react";

export type RecentCardLoadingProps = {
};

export const RecentCardLoading: FC<RecentCardLoadingProps> = (
    _props: RecentCardLoadingProps,
) => {

    return (<UICardHeader
        className="flex flex-row items-center gap-3"
    >
        <UISpinner
            variant="simple"
            color="default"
            className="w-[24px] h-[24px]"
            size="sm"
        />
        <div
            className="flex flex-col gap-2 w-full"
        >
            <UISkeleton
                className="h-4 max-w-96 w-full rounded-sm"
            />
            <UISkeleton
                className="h-2.5 max-w-32 w-full rounded-sm"
            />
        </div>
    </UICardHeader>);
};
