/**
 * <AUTHOR>
 * @namespace Property_Utils
 * @description Stringify
 */

import { IImbricateProperty, IMBRICATE_PROPERTY_TYPE, ImbricatePropertyValueObject, ImbricatePropertyValueObjectReference } from "@imbricate/core";

export const stringifyProperty = (
    property: IImbricateProperty<IMBRICATE_PROPERTY_TYPE>,
): string => {

    if (typeof property.propertyValue === "undefined" || property.propertyValue === null) {
        return "Empty";
    }

    // IMBRICATE_PROPERTY_TYPE SWITCH
    switch (property.propertyType) {

        case IMBRICATE_PROPERTY_TYPE.BINARY: {

            return `Binary File ${property.propertyValue}`;
        }
        case IMBRICATE_PROPERTY_TYPE.BOOLEAN: {

            return property.propertyValue ? "True" : "False";
        }
        case IMBRICATE_PROPERTY_TYPE.STRING: {

            return property.propertyValue as string;
        }
        case IMBRICATE_PROPERTY_TYPE.NUMBER: {

            return String(property.propertyValue);
        }
        case IMBRICATE_PROPERTY_TYPE.DATE: {

            return new Date(property.propertyValue as string).toLocaleString();
        }
        case IMBRICATE_PROPERTY_TYPE.MARKDOWN: {

            return `Markdown File ${property.propertyValue}`;
        }
        case IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT: {

            return `Imbriscript File ${property.propertyValue}`;
        }
        case IMBRICATE_PROPERTY_TYPE.JSON: {

            return `JSON File ${property.propertyValue}`;
        }
        case IMBRICATE_PROPERTY_TYPE.LABEL: {

            return (property.propertyValue as ImbricatePropertyValueObject<IMBRICATE_PROPERTY_TYPE.LABEL>)
                .map((each: string) => each)
                .join(", ");
        }
        case IMBRICATE_PROPERTY_TYPE.REFERENCE: {

            return (property.propertyValue as ImbricatePropertyValueObject<IMBRICATE_PROPERTY_TYPE.REFERENCE>)
                .map((each: ImbricatePropertyValueObjectReference) => each.documentUniqueIdentifier)
                .join(", ");
        }
    }

    return "Unknown";
};
