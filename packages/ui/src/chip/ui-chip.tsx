/**
 * <AUTHOR>
 * @package UI
 * @namespace Chip
 * @description Chip
 */

import { Chip } from "@heroui/chip";
import React from "react";
import { UIColor, UIRadius, UISize } from "../common/types";
import { UIChipVariant } from "./types";

export type UIChipProps = {

    readonly className?: string;

    readonly baseClassName?: string;
    readonly contentClassName?: string;

    readonly color?: UIColor;
    readonly size?: UISize;
    readonly radius?: UIRadius;
    readonly variant?: UIChipVariant;

    readonly children?: React.ReactNode;

    readonly startContent?: React.ReactNode;
    readonly endContent?: React.ReactNode;
};

export const UIChip: React.FC<UIChipProps> = (
    props: UIChipProps,
) => {

    return (<Chip
        className={props.className}
        classNames={{
            base: props.baseClassName,
            content: props.contentClassName,
        }}
        color={props.color}
        size={props.size}
        radius={props.radius}
        variant={props.variant}
        startContent={props.startContent}
        endContent={props.endContent}
    >
        {props.children}
    </Chip>);
};
