/**
 * <AUTHOR>
 * @namespace Recent_Hooks
 * @description Use Recent
 */

import React, { useEffect } from "react";
import { PassiveDataController } from "../../passive-data-controller";
import { HUMMINGBIRD_PASSIVE_DATA_ITEM } from "../../types/passive-data/enum";
import { RecentDefinition } from "../types/recent-definition";

export const useRecent = (): RecentDefinition | null => {

    const [recent, setRecent] = React.useState<RecentDefinition | null>(null);

    useEffect(() => {

        const recentRecords = PassiveDataController.getInstance().getPassiveData(
            HUMMINGBIRD_PASSIVE_DATA_ITEM.RECENT_ITEMS,
        );
        setRecent(recentRecords);
    }, []);

    return recent;
};
