/**
 * <AUTHOR>
 * @namespace Internationalization_Internationalization_Locale
 * @description En-US
 */

import { IETF_LOCALE } from "@sudoo/locale";

export const enUSInternationalizationProfile: Record<IETF_LOCALE, string> = {

    [IETF_LOCALE.CHINESE_SIMPLIFIED]: "Simplified Chinese",
    [IETF_LOCALE.CHINESE_TRADITIONAL]: "Traditional Chinese",
    [IETF_LOCALE.ENGLISH_CANADA]: "English (Canada)",
    [IETF_LOCALE.ENGLISH_UNITED_KINGDOM]: "English (UK)",
    [IETF_LOCALE.ENGLISH_UNITED_STATES]: "English (US)",
    [IETF_LOCALE.FRENCH_CANADA]: "French (Canada)",
    [IETF_LOCALE.FRENCH_FRANCE]: "French (France)",
    [IETF_LOCALE.JAPANESE_JAPAN]: "Japanese",
    [IETF_LOCALE.KOREAN_KOREA]: "Korean",
    [IETF_LOCALE.MAORI_NEW_ZEALAND]: "<PERSON><PERSON>",
    [IETF_LOCALE.RUSSIAN_RUSSIA]: "Russian",
    [IETF_LOCALE.SPANISH_MEXICO]: "Spanish (Mexico)",
    [IETF_LOCALE.SPANISH_SPAIN]: "Spanish (Spain)",
};
