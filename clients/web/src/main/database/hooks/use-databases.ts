/**
 * <AUTHOR>
 * @namespace Database_Hooks
 * @description Use Databases
 */

import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { DisconnectSlice, getDisconnectType } from "@imbricate-hummingbird/react-store";
import { IImbricateDatabase } from "@imbricate/core";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { findCache, saveCache } from "../../common/cache/cache";
import { DATABASE_CACHE_IDENTIFIER } from "../../common/cache/static";
import { rootLogger } from "../../log/logger";
import { useOldOrigins } from "../../origin/hooks/use-origins";
import { createFindDatabasesAction } from "../actions/find-databases";
import { findDatabase } from "../utils/find-database";

const logger = rootLogger.fork({
    scopes: [
        "Database",
        "Hooks",
        "UseDatabases",
    ],
});

export const S_UseDatabasesLoading: unique symbol = Symbol("UseDatabasesLoading");
export const S_UseDatabasesNotFound: unique symbol = Symbol("UseDatabasesNotFound");
export const S_UseDatabasesErrored: unique symbol = Symbol("UseDatabasesErrored");

export type ImbricateDatabasesObject = {

    readonly originUniqueIdentifier: string;
    readonly originName: string;
    readonly database: IImbricateDatabase;
};

export type UseDatabasesResponse = {

    readonly databases: ImbricateDatabasesObject[];
};

export type UseDatabasesResponseSymbol =
    | typeof S_UseDatabasesLoading
    | typeof S_UseDatabasesNotFound
    | typeof S_UseDatabasesErrored;

export const useDatabases = (
    originUniqueIdentifier?: string,
): UseDatabasesResponse | UseDatabasesResponseSymbol => {

    const origins: ImbricateOriginObject[] = useOldOrigins();
    const [databases, setDatabases] = useState<ImbricateDatabasesObject[] | null>(null);
    const [errored, setErrored] = useState<boolean>(false);

    const dispatch = useDispatch();

    const deps: string = origins
        .map((origin: ImbricateOriginObject) => origin.origin.uniqueIdentifier)
        .join("|");

    useEffect(() => {

        if (origins.length === 0) {
            return;
        }

        const cache = findCache<ImbricateDatabasesObject[]>(
            DATABASE_CACHE_IDENTIFIER,
            [deps],
        );

        if (cache) {

            logger.debug("List Databases (Cache)", origins);

            setDatabases(cache);
            return;
        }

        const execute = async () => {

            const targetOrigins: ImbricateOriginObject[] = originUniqueIdentifier
                ? origins.filter((
                    origin: ImbricateOriginObject,
                ) => origin.origin.uniqueIdentifier === originUniqueIdentifier)
                : origins;

            const response: ImbricateDatabasesObject[] = [];
            for (const origin of targetOrigins) {

                try {

                    const originResponse =
                        await ActionCentral.getInstance().executeAction(
                            async () => {

                                const originResponse: ImbricateDatabasesObject[] =
                                    await findDatabase(
                                        origin.origin.uniqueIdentifier,
                                    );

                                return originResponse;
                            },
                            createFindDatabasesAction(
                                origin.origin.uniqueIdentifier,
                                {
                                    executer: import.meta.url,
                                },
                            ),
                        );

                    response.push(...originResponse);

                    dispatch(
                        DisconnectSlice.actions.markAsResolved(
                            origin.origin.uniqueIdentifier,
                        ),
                    );
                } catch (error) {

                    const fixedError: any = error;

                    const status: number = fixedError.status;
                    const message: string = fixedError.message;

                    logger.error({
                        status,
                        message,
                    });

                    dispatch(
                        DisconnectSlice.actions.markAsDisconnected({
                            type: getDisconnectType(status),
                            originUniqueIdentifier: origin.origin.uniqueIdentifier,
                            originInstance: origin.originInstance,
                        }),
                    );

                    setErrored(true);
                }

                saveCache(DATABASE_CACHE_IDENTIFIER, [deps], response);
                setDatabases(response);
            }
        };

        execute();
    }, [deps]);

    if (errored) {
        return S_UseDatabasesErrored;
    }

    if (databases === null) {
        return S_UseDatabasesLoading;
    }

    return {
        databases,
    };
};
