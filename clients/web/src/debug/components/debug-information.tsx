/**
 * <AUTHOR>
 * @namespace Debug_Components
 * @description Debug Information
 */

import { useDebugMode } from "@imbricate-hummingbird/debug";
import { UIAccordion, UITextarea } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { FaBug } from "react-icons/fa";

export type DebugInformationProps = {

    readonly className?: string;

    readonly title?: string;
    readonly information: string;

    readonly accordion?: boolean;
};

export const DebugInformation: FC<DebugInformationProps> = (
    props: DebugInformationProps,
) => {

    const { isDebugMode } = useDebugMode();

    if (!isDebugMode) {
        return null;
    }

    if (props.accordion) {

        const title = props.title ?? "Debug Information";

        return (<UIAccordion
            isCompact
            variant="bordered"
            className={props.className}
            itemKey="debug-information"
            itemTitle={title}
            startContent={<FaBug />}
        >
            <UITextarea
                startContent={<FaBug />}
                variant="flat"
                color="warning"
                value={props.information}
                isReadOnly
                isFullWidth
            />
        </UIAccordion>);
    }

    return (<UITextarea
        label={props.title}
        startContent={<FaBug />}
        variant="flat"
        color="warning"
        value={props.information}
        className={props.className}
        isReadOnly
        isFullWidth
    />);
};
