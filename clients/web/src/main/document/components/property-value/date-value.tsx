/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyValue
 * @description Date Value
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIButton, UIDatePicker } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { FaUnlink } from "react-icons/fa";

export type DocumentDateValueProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.DATE>;
    readonly deleteProperty: () => void;
    readonly updateProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.DATE>) => void;
};

export const DocumentDateValue: FC<DocumentDateValueProps> = (
    props: DocumentDateValueProps,
) => {

    const parsedDate = typeof props.property.value === "string"
        ? new Date(props.property.value)
        : null;

    return (<div
        className="flex gap-1 w-full"
    >
        <div
            className="flex-1"
        >
            <UIDatePicker
                ariaLabel="Date picker"
                value={parsedDate}
                onChange={(newDate: Date | null) => {

                    if (!newDate) {
                        return;
                    }

                    props.updateProperty({
                        key: props.propertyKey,
                        type: IMBRICATE_PROPERTY_TYPE.DATE,
                        value: newDate.toISOString(),
                        variant: props.property.variant,
                    });
                }}
            />
        </div>
        {parsedDate !== null && <UIButton
            variant="flat"
            color="danger"
            isIconOnly
            onPress={props.deleteProperty}
        >
            <FaUnlink
                className="text-large"
            />
        </UIButton>}
    </div>);
};
