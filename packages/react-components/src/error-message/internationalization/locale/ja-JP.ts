/**
 * <AUTHOR>
 * @package React Components
 * @namespace ErrorMessage_Internationalization_Locale
 * @description Ja-JP
 */

import { REACT_COMPONENTS_ERROR_MESSAGE_PROFILE } from "../profile";

export const jaJPReactComponentsErrorMessageProfile: Record<REACT_COMPONENTS_ERROR_MESSAGE_PROFILE, string> = {

    [REACT_COMPONENTS_ERROR_MESSAGE_PROFILE.DEFAULT_ERROR_MESSAGE]: "アプリケーションに問題があります。後でもう一度試してください。またはトラブルシューティングガイドを確認してください。",
    [REACT_COMPONENTS_ERROR_MESSAGE_PROFILE.DEFAULT_ERROR_TITLE]: "エラーが発生しました",
    [REACT_COMPONENTS_ERROR_MESSAGE_PROFILE.TROUBLESHOOT_GUIDE]: "トラブルシューティングガイド",
};
