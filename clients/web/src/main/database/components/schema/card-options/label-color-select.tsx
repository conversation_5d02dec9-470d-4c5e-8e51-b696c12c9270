/**
 * <AUTHOR>
 * @namespace Database_Components_Schema_Label
 * @description Label Color Select
 */

import { useCommonDatabaseLabelFormat } from "@/common/database/label/internationalization/hook";
import { LABEL_COLOR } from "@/common/database/label/label-color";
import { useDatabaseFormat } from "@/main/database/internationalization/hook";
import { DATABASE_PROFILE } from "@/main/database/internationalization/profile";
import { UIButton, UIInput, UISingleSelect, UITooltip } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchema, ImbricateDatabaseSchemaProperty, ImbricateDatabaseSchemaPropertyOptionsLabel, ImbricateDatabaseSchemaPropertyOptionsLabelOption } from "@imbricate/core";
import { FC } from "react";
import { MdDelete } from "react-icons/md";
import { CommonCopyItem } from "../../../../common/components/copy-item";
import { getLabelColorDot } from "../../../utils/label-color";

export type DatabaseSchemaPropertyCardOptionsLabelColorSelectProps = {

    readonly property: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE.LABEL>;
    readonly schema: ImbricateDatabaseSchema;
    readonly setSchema: (schema: ImbricateDatabaseSchema) => void;

    readonly labelIdentifier: string;
    readonly currentSchema: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE.LABEL>;
};

export const DatabaseSchemaPropertyCardOptionsLabelColorSelect: FC<DatabaseSchemaPropertyCardOptionsLabelColorSelectProps> = (
    props: DatabaseSchemaPropertyCardOptionsLabelColorSelectProps,
) => {

    const databaseFormat = useDatabaseFormat();
    const labelColorFormat = useCommonDatabaseLabelFormat();

    const options: ImbricateDatabaseSchemaPropertyOptionsLabel = props.currentSchema.propertyOptions;

    const labelOptions: ImbricateDatabaseSchemaPropertyOptionsLabelOption[] = options.labelOptions ?? [];

    const labelOption: ImbricateDatabaseSchemaPropertyOptionsLabelOption | undefined =
        labelOptions.find((each) => each.labelIdentifier === props.labelIdentifier);

    if (!labelOption) {
        return null;
    }

    return (<div
        className="flex gap-1"
    >
        <div
            className="flex-1"
        >
            <UISingleSelect
                ariaLabel="Label Color"
                isFullWidth
                size="sm"
                label={databaseFormat.get(DATABASE_PROFILE.LABEL_COLOR)}
                defaultSelectedKey={labelOption.labelColor}
                onSelectedKeyChange={(
                    newSelectedKey: string,
                ) => {

                    const newOptions: ImbricateDatabaseSchemaPropertyOptionsLabelOption[] = labelOptions.map((each) => {
                        if (each.labelIdentifier === props.labelIdentifier) {
                            return {
                                ...each,
                                labelColor: newSelectedKey,
                            };
                        }
                        return each;
                    });

                    props.setSchema({
                        ...props.schema,
                        properties: props.schema.properties.map((each) => {
                            if (each.propertyIdentifier === props.property.propertyIdentifier) {
                                return {
                                    ...each,
                                    propertyOptions: {
                                        ...options,
                                        labelOptions: newOptions,
                                    },
                                };
                            }
                            return each;
                        }),
                    });
                }}
                items={[
                    {
                        itemKey: LABEL_COLOR.BLACK,
                        content: labelColorFormat.get(LABEL_COLOR.BLACK),
                        startContent: getLabelColorDot(LABEL_COLOR.BLACK),
                    },
                    {
                        itemKey: LABEL_COLOR.BLUE,
                        content: labelColorFormat.get(LABEL_COLOR.BLUE),
                        startContent: getLabelColorDot(LABEL_COLOR.BLUE),
                    },
                    {
                        itemKey: LABEL_COLOR.BROWN,
                        content: labelColorFormat.get(LABEL_COLOR.BROWN),
                        startContent: getLabelColorDot(LABEL_COLOR.BROWN),
                    },
                    {
                        itemKey: LABEL_COLOR.CYAN,
                        content: labelColorFormat.get(LABEL_COLOR.CYAN),
                        startContent: getLabelColorDot(LABEL_COLOR.CYAN),
                    },
                    {
                        itemKey: LABEL_COLOR.GREEN,
                        content: labelColorFormat.get(LABEL_COLOR.GREEN),
                        startContent: getLabelColorDot(LABEL_COLOR.GREEN),
                    },
                    {
                        itemKey: LABEL_COLOR.ORANGE,
                        content: labelColorFormat.get(LABEL_COLOR.ORANGE),
                        startContent: getLabelColorDot(LABEL_COLOR.ORANGE),
                    },
                    {
                        itemKey: LABEL_COLOR.PINK,
                        content: labelColorFormat.get(LABEL_COLOR.PINK),
                        startContent: getLabelColorDot(LABEL_COLOR.PINK),
                    },
                    {
                        itemKey: LABEL_COLOR.PURPLE,
                        content: labelColorFormat.get(LABEL_COLOR.PURPLE),
                        startContent: getLabelColorDot(LABEL_COLOR.PURPLE),
                    },
                    {
                        itemKey: LABEL_COLOR.RED,
                        content: labelColorFormat.get(LABEL_COLOR.RED),
                        startContent: getLabelColorDot(LABEL_COLOR.RED),
                    },
                    {
                        itemKey: LABEL_COLOR.VIOLET,
                        content: labelColorFormat.get(LABEL_COLOR.VIOLET),
                        startContent: getLabelColorDot(LABEL_COLOR.VIOLET),
                    },
                    {
                        itemKey: LABEL_COLOR.YELLOW,
                        content: labelColorFormat.get(LABEL_COLOR.YELLOW),
                        startContent: getLabelColorDot(LABEL_COLOR.YELLOW),
                    },
                ]}
            />
        </div>
        <div
            className="flex-1"
        >
            <UIInput
                isFullWidth
                size="sm"
                label={databaseFormat.get(DATABASE_PROFILE.LABEL_NAME)}
                value={labelOption.labelName}
                onValueChange={(newValue: string) => {

                    const newOptions: ImbricateDatabaseSchemaPropertyOptionsLabelOption[] = labelOptions.map((
                        each: ImbricateDatabaseSchemaPropertyOptionsLabelOption,
                    ) => {

                        if (each.labelIdentifier === props.labelIdentifier) {
                            return {
                                ...each,
                                labelName: newValue,
                            };
                        }
                        return each;
                    });

                    props.setSchema({
                        ...props.schema,
                        properties: props.schema.properties.map((
                            each: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>,
                        ) => {

                            if (each.propertyIdentifier === props.property.propertyIdentifier) {
                                return {
                                    ...each,
                                    propertyOptions: {
                                        ...options,
                                        labelOptions: newOptions,
                                    },
                                };
                            }
                            return each;
                        }),
                    });
                }}
            />
        </div>
        <div>
            <UITooltip
                content={<CommonCopyItem
                    startContent="Label Identifier"
                    content={props.labelIdentifier}
                />}
                delay={1000}
                placement="left"
            >
                <UIButton
                    variant="flat"
                    color="danger"
                    isIconOnly
                    radius="sm"
                    size="lg"
                    onPress={() => {

                        const newOptions: ImbricateDatabaseSchemaPropertyOptionsLabelOption[] = labelOptions.filter((each) => each.labelIdentifier !== props.labelIdentifier);

                        props.setSchema({
                            ...props.schema,
                            properties: props.schema.properties.map((each) => {
                                if (each.propertyIdentifier === props.property.propertyIdentifier) {
                                    return {
                                        ...each,
                                        propertyOptions: {
                                            ...options,
                                            labelOptions: newOptions,
                                        },
                                    };
                                }
                                return each;
                            }),
                        });
                    }}
                >
                    <MdDelete
                        className="text-large"
                    />
                </UIButton>
            </UITooltip>
        </div>
    </div>);
};
