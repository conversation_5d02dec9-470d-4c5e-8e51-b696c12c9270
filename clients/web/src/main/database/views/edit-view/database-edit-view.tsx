/**
 * <AUTHOR>
 * @namespace Database_Views_EditView
 * @description Database Edit View
 */

import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { useAsyncTitle } from "@imbricate-hummingbird/react-common";
import { StyledCard } from "@imbricate-hummingbird/react-components";
import { useNavigateDatabaseDocumentsView } from "@imbricate-hummingbird/react-navigation";
import { UIButton, UICardBody, UICardFooter, UICardHeader, UIDivider, UIInput, UINavbar, UISpacer } from "@imbricate-hummingbird/ui";
import { IImbricateDatabase } from "@imbricate/core";
import React, { FC } from "react";
import { RiCameraLensFill } from "react-icons/ri";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { useWideOrigin } from "../../../origin/hooks/use-wide-origin";
import { UseDatabaseResponseSymbol, useDatabase } from "../../hooks/use-database";

export type DatabaseEditViewProps = {
};

// LAZY LOAD ONLY
const DatabaseEditView: FC<DatabaseEditViewProps> = (
    _props: DatabaseEditViewProps,
) => {

    const params = useParams();
    const databaseUniqueIdentifier: string =
        params["database-unique-identifier"] as string;

    const origin: ImbricateOriginObject | null =
        useWideOrigin(databaseUniqueIdentifier);

    const database: IImbricateDatabase | UseDatabaseResponseSymbol =
        useDatabase(databaseUniqueIdentifier);

    if (!origin
        || typeof database === "symbol") {

        throw new Error("[Imbricate] Database not found");
    }

    const [edit, setEdit] = React.useState<boolean>(false);
    const [databaseName, setDatabaseName] = React.useState<string>(
        database.databaseName,
    );

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const navigateToDatabaseDocuments = useNavigateDatabaseDocumentsView();

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const dispatch = useDispatch();

    useAsyncTitle(
        () => typeof database !== "symbol",
        () => {

            if (typeof database === "symbol") {
                return [];
            }

            return [
                database.databaseName,
                "Edit",
                "Lens",
            ];
        },
        [
            typeof database === "symbol"
                ? database
                : origin.origin.uniqueIdentifier,
        ],
    );

    if (!database) {
        return null;
    }

    return (<div
        className="flex flex-col gap-2 pb-4 overflow-auto h-full min-h-full"
    >
        <UINavbar
            isFullWidth
            isBordered
            startContent={<React.Fragment>
                <RiCameraLensFill
                    className="text-2xl"
                />
                <UISpacer />
                <p
                    className="font-mono"
                >
                    Lens
                </p>
            </React.Fragment>}
            coreContent={<p className="font-bold text-xl">
                <span
                    className="font-mono font-light"
                >
                    Editing
                </span> {database.databaseName}
            </p>}
        />
        <div
            className="pr-2 h-full"
        >
            <StyledCard>
                <UICardHeader>
                    Database Name
                </UICardHeader>
                <UIDivider />
                <UICardBody>
                    <UIInput
                        label="Database Name"
                        placeholder="The Database name"
                        value={databaseName}
                        onValueChange={(newValue: string) => {

                            setDatabaseName(newValue);
                            setEdit(true);
                        }}
                    />
                </UICardBody>
                {edit &&
                    <React.Fragment>
                        <UIDivider />
                        <UICardFooter>
                            <UIButton
                                variant="flat"
                                color="primary"
                                isDisabled={databaseName.length === 0}
                                onPress={() => {

                                }}
                            >
                                Update Lens
                            </UIButton>
                        </UICardFooter>
                    </React.Fragment>}
            </StyledCard>
        </div>
    </div>);
};

export default DatabaseEditView;
