/**
 * <AUTHOR>
 * @namespace Database_Components_Table
 * @description Database Table
 */

import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { DocumentsTable } from "@/main/document/components/documents-table";
import { DocumentEditingController } from "@/main/document/controller/editing-controller";
import { TransferDocument } from "@imbricate-hummingbird/transfer-core";
import { IImbricateDatabase, ImbricateDocumentQuery } from "@imbricate/core";
import React, { FC } from "react";

export type DatabaseTableProps = {

    readonly origin: ImbricateOriginObject;
    readonly database: IImbricateDatabase;
    readonly documents: TransferDocument[];

    readonly query: ImbricateDocumentQuery;

    readonly forceUpdate: () => void;
    readonly updateVersion: () => void;
    readonly editingControllerRef: React.RefObject<DocumentEditingController | undefined>;

    readonly displayedProperties: string[];
};

export const DatabaseDatabaseTable: FC<DatabaseTableProps> = (
    props: DatabaseTableProps,
) => {

    return (<DocumentsTable
        origin={props.origin}
        database={props.database}
        documents={props.documents}
        query={props.query}
        forceUpdate={props.forceUpdate}
        updateVersion={props.updateVersion}
        editingControllerRef={props.editingControllerRef}
        displayedProperties={props.displayedProperties}
    />);
};
