/**
 * <AUTHOR>
 * @namespace Property_Types
 * @description Use Property
 */

import { IImbricateProperty, IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaProperty } from "@imbricate/core";

export const S_UsePropertyLoading: unique symbol = Symbol("UsePropertyLoading");
export const S_UsePropertyNotFound: unique symbol = Symbol("UsePropertyNotFound");
export const S_UsePropertyErrored: unique symbol = Symbol("UsePropertyErrored");

export type UsePropertyResponse = {

    readonly schemaProperty: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>;
    readonly documentProperty?: IImbricateProperty<IMBRICATE_PROPERTY_TYPE>;
};

export type UsePropertyResponseSymbol =
    | typeof S_UsePropertyLoading
    | typeof S_UsePropertyNotFound
    | typeof S_UsePropertyErrored;
