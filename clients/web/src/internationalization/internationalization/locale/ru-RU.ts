/**
 * <AUTHOR>
 * @namespace Internationalization_Internationalization_Locale
 * @description Ru-RU
 */

import { IETF_LOCALE } from "@sudoo/locale";

// spell-checker: disable
export const ruRUInternationalizationProfile: Record<IETF_LOCALE, string> = {

    [IETF_LOCALE.CHINESE_SIMPLIFIED]: "Китайский (упрощенный)",
    [IETF_LOCALE.CHINESE_TRADITIONAL]: "Китайский (традиционный)",
    [IETF_LOCALE.ENGLISH_CANADA]: "Английский (Канада)",
    [IETF_LOCALE.ENGLISH_UNITED_KINGDOM]: "Английский (Великобритания)",
    [IETF_LOCALE.ENGLISH_UNITED_STATES]: "Английский (США)",
    [IETF_LOCALE.FRENCH_CANADA]: "Французский (Канада)",
    [IETF_LOCALE.FRENCH_FRANCE]: "Французский (Франция)",
    [IETF_LOCALE.JAPANESE_JAPAN]: "Японский",
    [IETF_LOCALE.KOREAN_KOREA]: "Корейский",
    [IETF_LOCALE.MAORI_NEW_ZEALAND]: "Маори",
    [IETF_LOCALE.RUSSIAN_RUSSIA]: "Русский",
    [IETF_LOCALE.SPANISH_MEXICO]: "Испанский (Мексика)",
    [IETF_LOCALE.SPANISH_SPAIN]: "Испанский (Испания)",
};
// spell-checker: enable
