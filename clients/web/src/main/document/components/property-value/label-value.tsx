/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyValue
 * @description Label Value
 */

import { LABEL_COLOR } from "@/common/database/label/label-color";
import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIButton, UIChip, UISelect, UISelectSelectedItem } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaPropertyOptionsLabel, ImbricateDatabaseSchemaPropertyOptionsLabelOption } from "@imbricate/core";
import { FC } from "react";
import { FaUnlink } from "react-icons/fa";
import { getLabelColorClassName, getLabelColorDot, getLabelColorTextClassNameReverse } from "../../../database/utils/label-color";

export type DocumentLabelValueProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.LABEL>;
    readonly deleteProperty: () => void;
    readonly updateProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.LABEL>) => void;

    readonly options: ImbricateDatabaseSchemaPropertyOptionsLabel;
};

export const DocumentLabelValue: FC<DocumentLabelValueProps> = (
    props: DocumentLabelValueProps,
) => {

    const parsedValue = Array.isArray(props.property.value)
        ? props.property.value
        : [];

    return (<div
        className="flex gap-1 w-full"
    >
        <div
            className="flex-1"
        >
            <UISelect
                ariaLabel="Select label"
                selectionMode={props.options.allowMultiple ? "multiple" : "single"}
                selectedKeys={parsedValue}
                renderValue={(values: UISelectSelectedItem[]) => {
                    return values.map((
                        item: UISelectSelectedItem,
                    ) => {

                        const labelColor = props.options.labelOptions.find((
                            option: ImbricateDatabaseSchemaPropertyOptionsLabelOption,
                        ) => option.labelIdentifier === item.itemKey)?.labelColor;

                        return (<UIChip
                            key={item.itemKey}
                            size="sm"
                            className="mr-1 last:mr-0"
                            baseClassName={labelColor
                                ? (getLabelColorClassName(labelColor as LABEL_COLOR) ?? "")
                                : ""}
                            contentClassName={labelColor
                                ? (getLabelColorTextClassNameReverse(labelColor as LABEL_COLOR) ?? "")
                                : ""}
                        >
                            {item.itemTextValue}
                        </UIChip>);
                    });
                }}
                onSelectedKeysChange={(
                    newSelectedKeys: string[],
                ) => {

                    props.updateProperty({
                        key: props.propertyKey,
                        type: IMBRICATE_PROPERTY_TYPE.LABEL,
                        value: newSelectedKeys,
                        variant: props.property.variant,
                    });
                }}
                items={props.options.labelOptions.map((
                    each: ImbricateDatabaseSchemaPropertyOptionsLabelOption,
                ) => {

                    return {
                        itemKey: each.labelIdentifier,
                        startContent: getLabelColorDot(
                            each.labelColor as LABEL_COLOR,
                        ),
                        content: each.labelName,
                    };
                })}
            />
        </div>
        {parsedValue.length > 0 && (<UIButton
            variant="flat"
            color="danger"
            isIconOnly
            onPress={props.deleteProperty}
        >
            <FaUnlink
                className="text-large"
            />
        </UIButton>)}
    </div>);
};
