/**
 * <AUTHOR>
 * @namespace Data_Components_DataRecover
 * @description Data Recover Icon
 */

import { ActionItem } from "@imbricate-hummingbird/interceptor-core";
import { UISpinner } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { FaTimes } from "react-icons/fa";

export type DataRecoverIconProps = {

    readonly item: ActionItem<any>;
};

export const DataRecoverIcon: FC<DataRecoverIconProps> = (
    props: DataRecoverIconProps,
) => {

    if (props.item.retrying) {
        return (<UISpinner
            color="warning"
            className="w-6"
            size="sm"
        />);
    }

    return (<FaTimes
        color="red"
        className="text-2xl w-6"
    />);
};
