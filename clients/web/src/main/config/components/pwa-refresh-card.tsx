/**
 * <AUTHOR>
 * @namespace Config_Components
 * @description PWA Refresh Card
 */

import { StyledCard, openMessageToaster } from "@imbricate-hummingbird/react-components";
import { UIButton, UICardBody, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { BiReset } from "react-icons/bi";
import { GrUpgrade } from "react-icons/gr";
import { refreshPWA, updatePWA } from "../../common/pwa/refresh";

export const ConfigPWARefreshCard = () => {

    return (<StyledCard>
        <UICardHeader>
            PWA Refresh
        </UICardHeader>
        <UIDivider />
        <UICardBody>
            <UIButton
                radius="sm"
                variant="flat"
                startContent={<GrUpgrade />}
                onPress={async () => {
                    await updatePWA();

                    openMessageToaster("PWA Upgrade completed");
                }}
            >
                Upgrade Imbricate Hummingbird
            </UIButton>
            <UISpacer />
            <UIButton
                radius="sm"
                variant="flat"
                startContent={<BiReset />}
                color="primary"
                onPress={async () => {
                    await refreshPWA();

                    openMessageToaster("PWA Refresh completed");
                }}
            >
                Reset PWA
            </UIButton>
        </UICardBody>
    </StyledCard>);
};
