/**
 * <AUTHOR>
 * @namespace Navigation_Hooks
 * @description Use Title
 */

import { checkIsPWA } from "@imbricate-hummingbird/platformless";
import { useEffect } from "react";
import { ReactDependency } from "../types/react-types";

const getTitlesList = (
    titles: string[],
): string[] => {

    const isRunningAsPWA: boolean = checkIsPWA();

    return isRunningAsPWA
        ? titles
        : [
            ...titles,
            "Imbricate",
        ];
};

export const usePureTitle = (
    titles: string[],
): void => {

    const joinedTitle: string = titles.join(" | ");

    useEffect(() => {

        document.title = joinedTitle;
    }, [joinedTitle]);
};

export const useTitle = (
    titles: string[],
    deps: ReactDependency[],
): void => {

    const joinedTitle: string = getTitlesList(titles).join(" | ");

    useEffect(() => {

        document.title = joinedTitle;
    }, [joinedTitle, ...deps]);
};

export const useAsyncTitle = (
    condition: () => boolean,
    getTitle: () => string[],
    deps: ReactDependency[],
): void => {

    useEffect(() => {

        if (!condition()) {
            return;
        }

        const title = getTitle();
        document.title = getTitlesList(title).join(" | ");
    }, deps);
};
