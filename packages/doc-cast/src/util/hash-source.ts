/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Util
 * @description Hash Source
 */

import { DOC_CAST_SOURCE_TYPE, DocCastSource, DocCastSourceDocumentStackIdentifierPayload } from "../types/source";

export const hashDocCastSource = (
    source: DocCastSource<DOC_CAST_SOURCE_TYPE>,
): string => {

    const chain: string[] = [
        source.type,
    ];

    // DOC_CAST_SOURCE_TYPE SWITCH
    switch (source.type) {

        case DOC_CAST_SOURCE_TYPE.DOCUMENT_STACK_IDENTIFIER: {

            const fixedPayload: DocCastSourceDocumentStackIdentifierPayload =
                source.payload as DocCastSourceDocumentStackIdentifierPayload;

            chain.push(fixedPayload.originUniqueIdentifier);
            chain.push(fixedPayload.databaseUniqueIdentifier);
            chain.push(fixedPayload.documentUniqueIdentifier);
            break;
        }
    }

    return chain.join("|");
};
