/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyValue_Binary
 * @description Binary Popover
 */

import { useDocumentFormat } from "@/main/document/internationalization/hook";
import { DOCUMENT_PROFILE } from "@/main/document/internationalization/profile";
import { UploadStaticFileButton } from "@/main/statics/components/upload-static-file-button";
import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIRadius, UISize } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaPropertyOptionsBinary } from "@imbricate/core";
import { FC } from "react";
import { FaFileArrowUp, FaFileCirclePlus } from "react-icons/fa6";

export type DocumentBinaryValueBinaryPopoverProps = {

    readonly originUniqueIdentifier: string;

    readonly size?: UISize;
    readonly radius?: UIRadius;
    readonly iconOnly?: boolean;

    readonly propertyKey: string;
    readonly currentProperty: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY>;
    readonly updateProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY>) => void;

    readonly options: ImbricateDatabaseSchemaPropertyOptionsBinary;
};

export const DocumentBinaryValueBinaryPopover: FC<DocumentBinaryValueBinaryPopoverProps> = (
    props: DocumentBinaryValueBinaryPopoverProps,
) => {

    const documentFormat = useDocumentFormat();

    const isSingleBinary = !props.options.allowMultiple;

    return (<UploadStaticFileButton
        isDisabled={isSingleBinary && props.currentProperty.value.length > 0}
        color="primary"
        variant="flat"
        originUniqueIdentifier={props.originUniqueIdentifier}
        onFileUploaded={(
            staticUniqueIdentifier: string,
        ) => {

            props.updateProperty({
                key: props.currentProperty.key,
                type: props.currentProperty.type,
                value: [
                    ...props.currentProperty.value,
                    staticUniqueIdentifier,
                ],
                variant: props.currentProperty.variant,
            });
        }}
        size={props.size}
        radius={props.radius}
        isIconOnly={props.iconOnly}
    >
        {isSingleBinary
            ? <FaFileArrowUp
                className={props.iconOnly ? "text-large" : undefined}
            />
            : <FaFileCirclePlus
                className={props.iconOnly ? "text-large" : undefined}
            />}

        {!props.iconOnly && documentFormat.get(
            DOCUMENT_PROFILE.UPLOAD_BINARY,
        )}
    </UploadStaticFileButton>);
};
