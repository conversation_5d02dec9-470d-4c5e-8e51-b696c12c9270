/**
 * <AUTHOR>
 * @package UI
 * @namespace Snippet
 * @description Snippet
 */

import { Snippet } from "@heroui/snippet";
import type { ReactNode } from "react";
import { UIColor, UISize } from "../common/types";
import { UITooltipPlacement } from "../tooltip/types";
import { UISnippetVariant } from "./types";
import clsx from "clsx";

export type UISnippetProps = {

    readonly className?: string;

    readonly size?: UISize;
    readonly color?: UIColor;
    readonly variant?: UISnippetVariant;

    readonly leadingSymbol?: ReactNode;
    readonly isInlineSymbol?: boolean;

    readonly copyButtonTooltipContent?: ReactNode;
    readonly copyButtonTooltipPlacement?: UITooltipPlacement;

    readonly children: ReactNode;
};

export const UISnippet: React.FC<UISnippetProps> = (
    props: UISnippetProps,
) => {

    return (<Snippet
        tooltipProps={{
            content: props.copyButtonTooltipContent,
            placement: props.copyButtonTooltipPlacement,
        }}
        size={props.size}
        color={props.color}
        variant={props.variant}
        symbol={props.leadingSymbol}
        className={props.className}
        classNames={{
            pre: clsx(
                "w-full overflow-hidden overflow-ellipsis",
                props.isInlineSymbol && "flex items-center gap-2",
            ),
        }}
        hideSymbol={typeof props.leadingSymbol === "undefined"}
    >
        {props.children}
    </Snippet>);
};
