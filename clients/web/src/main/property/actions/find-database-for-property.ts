/**
 * <AUTHOR>
 * @namespace Property_Actions
 * @description Find Database For Property
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createFindDatabaseForPropertyAction = (
    databaseUniqueIdentifier: string,
    propertyUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Find Database For Property",
        actionDescription: `Find the database from [${databaseUniqueIdentifier}] with property [${propertyUniqueIdentifier}]`,
        executerMetadata: metadata,
    };
};
