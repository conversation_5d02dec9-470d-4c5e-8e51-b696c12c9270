/**
 * <AUTHOR>
 * @namespace Drawer_Hooks
 * @description Use Drawer Action
 */

import { Dispatch, UnknownAction } from "@reduxjs/toolkit";
import { useDispatch } from "react-redux";
import { reactStoreLogger } from "../../util/logger";
import { DrawerSlice } from "../feature";
import { DRAWER_TYPE, DrawerStatus } from "../types/status";

const logger = reactStoreLogger.fork({
    scopes: [
        "Drawer",
        "Hooks",
        "UseDrawerAction",
    ],
});

export type UseDrawerActionResponse = {

    readonly closeDrawer: () => void;
    readonly openDrawer: (status: DrawerStatus<DRAWER_TYPE>) => void;

    readonly previousDrawer: () => void;
    readonly nextDrawer: () => void;
};

export const useDrawerAction = (
    reuseDispatch?: Dispatch<UnknownAction>,
): UseDrawerActionResponse => {

    const dispatch = typeof reuseDispatch === "undefined"
        ? useDispatch()
        : reuseDispatch;

    return {

        closeDrawer: () => {

            logger.debug("[Drawer] Close Drawer");
            dispatch(
                DrawerSlice.actions.closeDrawer(),
            );
        },
        openDrawer: (status: DrawerStatus<DRAWER_TYPE>) => {

            logger.debug("[Drawer] Open Drawer", status);
            dispatch(
                DrawerSlice.actions.openDrawer(status),
            );
        },
        previousDrawer: () => {

            logger.debug("[Drawer] Previous Drawer");
            dispatch(
                DrawerSlice.actions.previousDrawer(),
            );
        },
        nextDrawer: () => {

            logger.debug("[Drawer] Next Drawer");
            dispatch(
                DrawerSlice.actions.nextDrawer(),
            );
        },
    };
};
