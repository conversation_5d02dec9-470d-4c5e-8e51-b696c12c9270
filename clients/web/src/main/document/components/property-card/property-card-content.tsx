/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyCard
 * @description Property Card Content
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaProperty, ImbricateDatabaseSchemaPropertyOptionsBinary } from "@imbricate/core";
import React from "react";
import { DocumentBinaryValue } from "../property-value/binary-value";
import { DocumentBooleanValue } from "../property-value/boolean-value";
import { DocumentDateValue } from "../property-value/date-value";
import { DocumentImbriscriptValue } from "../property-value/imbriscript-value";
import { DocumentJsonValue } from "../property-value/json-value";
import { DocumentLabelValue } from "../property-value/label-value";
import { DocumentMarkdownValue } from "../property-value/markdown-value";
import { DocumentNumberValue } from "../property-value/number-value";
import { DocumentReferenceValue } from "../property-value/reference-value";
import { DocumentStringValue } from "../property-value/string-value";

export type DocumentPropertyCardContentProps = {

    readonly liteMode?: boolean;
    readonly showPropertyName?: boolean;

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;

    readonly schema: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>;
    readonly deleteProperty: () => void;
    readonly updateProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>) => void;
};

export const DocumentPropertyCardContent: React.FC<DocumentPropertyCardContentProps> = (
    props: DocumentPropertyCardContentProps,
) => {

    const propertyType: IMBRICATE_PROPERTY_TYPE = props.schema.propertyType;
    const propertyIdentifier: string = props.schema.propertyIdentifier;

    // IMBRICATE_PROPERTY_TYPE SWITCH
    switch (propertyType) {

        case IMBRICATE_PROPERTY_TYPE.BINARY: {

            return (<DocumentBinaryValue
                originUniqueIdentifier={props.originUniqueIdentifier}
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY>}
                updateProperty={props.updateProperty}
                options={props.schema.propertyOptions as ImbricateDatabaseSchemaPropertyOptionsBinary}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.BOOLEAN: {

            return (<DocumentBooleanValue
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BOOLEAN>}
                updateProperty={props.updateProperty}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.STRING: {

            return (<DocumentStringValue
                showPropertyName={props.showPropertyName}
                propertySchema={props.schema as ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE.STRING>}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.STRING>}
                updateProperty={props.updateProperty}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.NUMBER: {

            return (<DocumentNumberValue
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.NUMBER>}
                updateProperty={props.updateProperty}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.DATE: {

            return (<DocumentDateValue
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.DATE>}
                deleteProperty={props.deleteProperty}
                updateProperty={props.updateProperty}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.MARKDOWN: {

            return (<DocumentMarkdownValue
                liteMode={props.liteMode}
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.MARKDOWN>}
                updateProperty={props.updateProperty}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT: {

            return (<DocumentImbriscriptValue
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT>}
                updateProperty={props.updateProperty}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.JSON: {

            return (<DocumentJsonValue
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.JSON>}
                updateProperty={props.updateProperty}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.LABEL: {

            return (<DocumentLabelValue
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.LABEL>}
                deleteProperty={props.deleteProperty}
                updateProperty={props.updateProperty}
                options={props.schema.propertyOptions as any}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.REFERENCE: {

            return (<DocumentReferenceValue
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.REFERENCE>}
                updateProperty={props.updateProperty}
                options={props.schema.propertyOptions as any}
            />);
        }
        default: {

            return (<DocumentStringValue
                showPropertyName={props.showPropertyName}
                propertySchema={props.schema as ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE.STRING>}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.STRING>}
                updateProperty={props.updateProperty}
            />);
        }
    }
};
