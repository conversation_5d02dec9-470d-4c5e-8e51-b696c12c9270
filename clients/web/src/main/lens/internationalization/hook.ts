/**
 * <AUTHOR>
 * @namespace Lens_Internationalization
 * @description Hook
 */

import { defaultEmptyFormat, useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { useLayoutEffect, useState } from "react";
import { lensInternationalization } from "./intl";
import { LENS_PROFILE } from "./profile";

export const useLensFormat = (): SudooFormat<LENS_PROFILE> => {

    const locale = useLocale();

    const [format, setFormat] = useState<SudooFormat<LENS_PROFILE>>(defaultEmptyFormat);

    useLayoutEffect(() => {

        const execute = async () => {

            const format = await lensInternationalization.format(locale);
            setFormat(format);
        };

        execute();
    }, [locale]);

    return format;
};
