/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyDisplay
 * @description Json Display
 */

import { DRAWER_TYPE, useDrawerAction } from "@imbricate-hummingbird/react-store";
import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIButton } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { useDocumentFormat } from "../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../internationalization/profile";

export type DocumentJsonDisplayProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.JSON>;
};

export const DocumentJsonDisplay: FC<DocumentJsonDisplayProps> = (
    props: DocumentJsonDisplayProps,
) => {

    const documentFormat = useDocumentFormat();

    const drawerActions = useDrawerAction();

    const fileAlreadyExists = props.property.value?.length > 0;

    if (fileAlreadyExists) {

        return (<div
            className="flex gap-2 flex-wrap items-center"
        >
            <UIButton
                size="sm"
                variant="ghost"
                onPress={() => {

                    drawerActions.openDrawer({
                        title: "JSON Preview",
                        type: DRAWER_TYPE.TEXT_RAW_PEEK,
                        payload: {
                            originUniqueIdentifier: props.originUniqueIdentifier,
                            textUniqueIdentifier: props.property.value,
                        },
                    });
                }}
            >
                Peek JSON
            </UIButton>
            <div
                className="text-xs text-gray-600 dark:text-gray-400"
            >
                {props.property.value}
            </div>
        </div>);
    }

    return (<div>
        {documentFormat.get(DOCUMENT_PROFILE.NO_DATA)}
    </div>);
};
