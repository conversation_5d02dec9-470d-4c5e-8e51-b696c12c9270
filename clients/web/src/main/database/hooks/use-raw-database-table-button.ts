/**
 * <AUTHOR>
 * @namespace Database_Hooks
 * @description Raw Database Table Button
 */

import { useMagicButtonAdjustItems } from "@/common/components/magic-button/hooks/use-adjust-items";
import { UseMagicButtonResult } from "@/common/components/magic-button/hooks/use-magic-button";
import { documentInvalidateQueryDocumentsCache } from "@/main/document/operation/invalid-query-documents-cache";
import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { IImbricateDatabase, ImbricateDocumentQuery } from "@imbricate/core";
import { MdRefresh } from "react-icons/md";
import { useDatabaseFormat } from "../internationalization/hook";
import { DATABASE_PROFILE } from "../internationalization/profile";

export type UseRawDatabaseTableButtonProps = {

    readonly origin: ImbricateOriginObject;
    readonly database: IImbricateDatabase;
    readonly query: ImbricateDocumentQuery;

    readonly updateVersion: () => void;
};

export const useRawDatabaseTableButton = (
    magicButtonResult: UseMagicButtonResult,
    props: UseRawDatabaseTableButtonProps,
) => {

    const databaseFormat = useDatabaseFormat();

    useMagicButtonAdjustItems(
        magicButtonResult,
        !isFormatLoading(databaseFormat),
        (result: UseMagicButtonResult) => {

            const cleanup = () => {
                result.removeItem("refresh-document-list");
            };

            result.addItem({
                categoryKey: "database",
                itemKey: "refresh-document-list",
                icon: MdRefresh,
                title: databaseFormat.get(DATABASE_PROFILE.REFRESH_DOCUMENT_LIST),
                description: databaseFormat.get(DATABASE_PROFILE.REFRESH_DOCUMENT_LIST_DESCRIPTION),
                quickAccess: true,
                color: "primary",
                onPress: async () => {

                    await documentInvalidateQueryDocumentsCache(
                        props.origin.origin.uniqueIdentifier,
                        props.database.uniqueIdentifier,
                        props.query,
                        {
                            executer: import.meta.url,
                        },
                    );

                    props.updateVersion();
                },
            });

            return cleanup;
        },
        [],
    );
};
