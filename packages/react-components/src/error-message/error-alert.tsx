/**
 * <AUTHOR>
 * @package React Components
 * @namespace ErrorMessage
 * @description Error Alert
 */

import { UIAlert, UIButtonLink, UIColor, UISpacer } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { useReactComponentsErrorMessageFormat } from "./internationalization/hook";
import { REACT_COMPONENTS_ERROR_MESSAGE_PROFILE } from "./internationalization/profile";

export type ErrorMessageAlertProps = {

    readonly hideIcon?: boolean;

    readonly title?: string;
    readonly message?: React.ReactNode;

    readonly color?: UIColor;
};

export const ErrorMessageAlert: FC<ErrorMessageAlertProps> = (
    props: ErrorMessageAlertProps,
) => {

    const format = useReactComponentsErrorMessageFormat();

    const title: string = props.title ?? format.get(REACT_COMPONENTS_ERROR_MESSAGE_PROFILE.DEFAULT_ERROR_TITLE);
    const message: React.ReactNode = props.message ?? (<React.Fragment>
        {format.get(REACT_COMPONENTS_ERROR_MESSAGE_PROFILE.DEFAULT_ERROR_MESSAGE)}
        <UISpacer />
        <UIButtonLink
            variant="ghost"
            color="primary"
            size="sm"
            href="https://imbricate.io/docs/hummingbird/troubleshoot/"
            isExternal
            className="whitespace-break-spaces"
        >
            {format.get(REACT_COMPONENTS_ERROR_MESSAGE_PROFILE.TROUBLESHOOT_GUIDE)}
        </UIButtonLink>
    </React.Fragment>);

    return (<UIAlert
        color={props.color}
        isHideIcon={props.hideIcon}
        title={title}
        description={message}
        variant="flat"
    />);
};
