/**
 * <AUTHOR>
 * @namespace Drawer
 * @description Drawer Provider
 */

import { rootLogger } from "@/main/log/logger";
import { FlyingLoadingWrapper, LazyLoadComponent } from "@imbricate-hummingbird/react-components";
import { useDrawerSlice } from "@imbricate-hummingbird/react-store";
import React, { FC } from "react";
import { useDrawerFormat } from "./internationalization/hook";
import { DRAWER_PROFILE } from "./internationalization/profile";

const DrawerRender = LazyLoadComponent(
    () => import("./drawer-render"),
    "Drawer Render",
);

const logger = rootLogger.fork({
    scopes: [
        "Drawer",
        "Provider",
    ],
});

export const DrawerProvider: FC = () => {

    const format = useDrawerFormat();

    const drawerSlice = useDrawerSlice();

    const renderedRef = React.useRef<boolean>(false);

    if (!drawerSlice.drawerOpen && !renderedRef.current) {

        logger.debug("Drawer is not open and not rendered");
        return null;
    }

    renderedRef.current = true;

    return (<React.Suspense
        fallback={<FlyingLoadingWrapper
            debugDescription="Drawer Provider"
            label={[
                format.get(DRAWER_PROFILE.LOADING_DRAWER),
            ]}
        />}
    >
        <DrawerRender
            drawerSlice={drawerSlice}
        />
    </React.Suspense>);
};
