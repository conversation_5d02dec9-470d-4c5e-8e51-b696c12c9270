/**
 * <AUTHOR>
 * @namespace Document_Components
 * @description Documents Table
 */

import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { TransferDocument, TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { Table, TableBody, TableColumn, TableHeader, TableRow } from "@imbricate-hummingbird/ui";
import { IImbricateDatabase, IMBRICATE_PROPERTY_TYPE, ImbricateDocumentQuery } from "@imbricate/core";
import React, { FC, useEffect } from "react";
import { MdMore } from "react-icons/md";
import { useForceUpdate } from "../../common/hooks/use-version";
import { DocumentEditingController } from "../controller/editing-controller";
import { documentInvalidateQueryDocumentsCache } from "../operation/invalid-query-documents-cache";
import { ArrangeDocumentsResult, ArrangeDocumentsResultItem, arrangeDocuments } from "../util/arrange-documents";
import { createDocumentsTableCells } from "./table-cells";
import { createDocumentsTableCellsCreate } from "./table-cells-create";
import { DocumentsTableHeaderCell } from "./table-headers/document-table-header-cell";

export type DocumentsTableProps = {

    readonly origin: ImbricateOriginObject;

    readonly database: IImbricateDatabase;
    readonly documents: TransferDocument[];

    readonly query: ImbricateDocumentQuery;

    readonly forceUpdate: () => void;
    readonly updateVersion: () => void;

    readonly editingControllerRef: React.RefObject<DocumentEditingController | undefined>;

    readonly displayedProperties: string[];
};

export const DocumentsTable: FC<DocumentsTableProps> = (
    props: DocumentsTableProps,
) => {

    const forceUpdate = useForceUpdate();

    const editingControllerRef = props.editingControllerRef;

    useEffect(() => {

        editingControllerRef.current = DocumentEditingController.create(
            props.origin,
            props.database,
        );

        const disposeStatusChange = editingControllerRef.current.listenStatusChange(() => {
            props.forceUpdate();
        });

        const disposeVersionChange = editingControllerRef.current.listenVersionChange(
            async () => {

                await documentInvalidateQueryDocumentsCache(
                    props.origin.origin.uniqueIdentifier,
                    props.database.uniqueIdentifier,
                    props.query,
                    {
                        executer: import.meta.url,
                    },
                );

                props.updateVersion();
            },
        );

        forceUpdate();

        return () => {

            editingControllerRef.current = void 0;
            disposeStatusChange();
            disposeVersionChange();
        };
    }, [props.database.uniqueIdentifier]);

    if (!props.database.schema) {
        return null;
    }

    if (!editingControllerRef.current) {
        return null;
    }

    const arrangedDocuments: ArrangeDocumentsResult = arrangeDocuments(
        props.database,
        props.documents,
        editingControllerRef.current,
        props.displayedProperties,
    );

    return (<div
        className="flex flex-col flex-1 min-w-0 min-h-0"
    >
        <Table
            classNames={{
                base: "h-full overflow-auto p-1",
            }}
            isHeaderSticky
            aria-label="document-list"
            removeWrapper
            className="overflow-y-auto"
        >
            <TableHeader>
                <React.Fragment>
                    {arrangedDocuments.propertyIdentifiers.map((
                        propertyIdentifier: string,
                    ) => {
                        return (<TableColumn
                            key={propertyIdentifier}
                        >
                            <DocumentsTableHeaderCell
                                propertyIdentifier={propertyIdentifier}
                                arrangedDocuments={arrangedDocuments}
                            />
                        </TableColumn>);
                    })}
                </React.Fragment>
                <TableColumn
                    className="flex items-center"
                >
                    Extra&nbsp;<MdMore />
                </TableColumn>
            </TableHeader>
            <TableBody>
                {[
                    ...arrangedDocuments.creatingDocuments.map((
                        [documentKey, propertyDrafts]: [string, TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[]],
                    ) => {

                        const cells = createDocumentsTableCellsCreate({
                            originUniqueIdentifier: props.origin.origin.uniqueIdentifier,
                            propertyIdentifiers: arrangedDocuments.propertyIdentifiers,
                            propertyTypesMap: arrangedDocuments.propertyTypesMap,
                            schemaMap: arrangedDocuments.schemaMap,
                            creatingDocumentKey: documentKey,
                            creatingDocumentPropertyDrafts: propertyDrafts,
                            editingController: editingControllerRef.current!,
                        });

                        return (<TableRow
                            key={documentKey}
                        >
                            {cells}
                        </TableRow>);
                    }),
                    ...arrangedDocuments.documents.map((
                        document: ArrangeDocumentsResultItem,
                    ) => {

                        const cells = createDocumentsTableCells({
                            originUniqueIdentifier: props.origin.origin.uniqueIdentifier,
                            databaseUniqueIdentifier: props.database.uniqueIdentifier,
                            propertyIdentifiers: arrangedDocuments.propertyIdentifiers,
                            propertyTypesMap: arrangedDocuments.propertyTypesMap,
                            document,
                            schemaMap: arrangedDocuments.schemaMap,
                            editingController: editingControllerRef.current!,
                        });

                        return (<TableRow
                            key={document.document.documentUniqueIdentifier}
                        >
                            {cells}
                        </TableRow>);
                    }),
                ]}
            </TableBody>
        </Table>
    </div>);
};
