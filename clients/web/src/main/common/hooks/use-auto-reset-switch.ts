/**
 * <AUTHOR>
 * @namespace Common_Hooks
 * @description Use Auto Reset Switch
 */

import { useEffect, useRef, useState } from "react";

export const useAutoResetStateSwitch = (
    delay: number,
    startState: boolean = false,
): [boolean, () => void] => {

    const timeoutIdRef = useRef<NodeJS.Timeout | null>(null);

    const [state, setState] = useState(startState);

    useEffect(() => {
        return () => {
            if (timeoutIdRef.current) {
                clearTimeout(timeoutIdRef.current);
            }
        };
    }, []);

    const toggleState = () => {
        setState(!startState);

        if (timeoutIdRef.current) {
            clearTimeout(timeoutIdRef.current);
        }

        timeoutIdRef.current = setTimeout(() => {
            setState(startState);
        }, delay);
    };

    return [state, toggleState];
};
