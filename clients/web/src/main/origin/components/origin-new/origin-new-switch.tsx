/**
 * <AUTHOR>
 * @namespace Origin_Components_OriginNew
 * @description New Origin Switch
 */

import { ORIGIN_INSTANCE_TYPE, OriginInstance, OriginInstanceMockOrigin, OriginInstanceStackAPIOrigin } from "@imbricate-hummingbird/transfer-core";
import { FC } from "react";
import { NewOriginMockOrigin } from "../origin-new-types/mock-origin";
import { NewOriginStackAPIOrigin } from "../origin-new-types/stack-api-origin";

export type NewOriginSwitchProps = {

    readonly originInstance: OriginInstance;
    readonly onOriginChange: (originInstance: OriginInstance) => void;

    readonly originType: ORIGIN_INSTANCE_TYPE | null;
};

export const NewOriginSwitch: FC<NewOriginSwitchProps> = (
    props: NewOriginSwitchProps,
) => {

    if (!props.originType) {
        return null;
    }

    switch (props.originType) {

        case ORIGIN_INSTANCE_TYPE.STACK_API: {

            return (<NewOriginStackAPIOrigin
                originInstance={props.originInstance as OriginInstanceStackAPIOrigin}
                onOriginChange={props.onOriginChange as (
                    originInstance: OriginInstanceStackAPIOrigin,
                ) => void}
            />);
        }
        case ORIGIN_INSTANCE_TYPE.MOCK: {

            return (<NewOriginMockOrigin
                originInstance={props.originInstance as OriginInstanceMockOrigin}
                onOriginChange={props.onOriginChange as (
                    originInstance: OriginInstanceMockOrigin,
                ) => void}
            />);
        }
    }
};
