/**
 * <AUTHOR>
 * @namespace Document_Internationalization
 * @description Profile
 */

export enum DOCUMENT_PROFILE {

    ARRANGE_DOCUMENT = "ARRANGE_DOCUMENT",
    ARRANGE_DOCUMENT_DESCRIPTION = "ARRANGE_DOCUMENT_DESCRIPTION",
    $1_BINARIES = "$1_BINARIES",
    CREATE_MARKDOWN_DOCUMENT = "CREATE_MARKDOWN_DOCUMENT",
    DANGER_ZONE = "DANGER_ZONE",
    DELETE_DOCUMENT = "DELETE_DOCUMENT",
    DELETE_DOCUMENT_DESCRIPTION = "DELETE_DOCUMENT_DESCRIPTION",
    DATABASE = "DATABASE",
    DOCUMENT = "DOCUMENT",
    DOCUMENT_DELETE_SUCCESS = "DOCUMENT_DELETE_SUCCESS",
    DOCUMENT_DELETE_SUCCESS_DESCRIPTION = "DOCUMENT_DELETE_SUCCESS_DESCRIPTION",
    DOCUMENT_DETAILS = "DOCUMENT_DETAILS",
    EDIT_HISTORY = "EDIT_HISTORY",
    EDIT_MARKDOWN_DOCUMENT = "EDIT_MARKDOWN_DOCUMENT",
    INVALID_DATE = "INVALID_DATE",
    LINK_DOCUMENT = "LINK_DOCUMENT",
    NO_DATA = "NO_DATA",
    NO_BINARY = "NO_BINARY",
    NO_REFERENCE = "NO_REFERENCE",
    ONE_BINARY = "ONE_BINARY",
    ONE_REFERENCE = "ONE_REFERENCE",
    PRIMARY = "PRIMARY",
    PRIMARY_KEY = "PRIMARY_KEY",
    PRIMARY_PROPERTY_IDENTIFIER = "PRIMARY_PROPERTY_IDENTIFIER",
    PROPERTY_IDENTIFIER = "PROPERTY_IDENTIFIER",
    $1_REFERENCES = "$1_REFERENCES",
    SAVE_CHANGES = "SAVE_CHANGES",
    UNABLE_TO_LOAD_DOCUMENT = "UNABLE_TO_LOAD_DOCUMENT",
    UPLOAD_BINARY = "UPLOAD_BINARY",
    VIEW_EDIT_HISTORY = "VIEW_EDIT_HISTORY",
    VIEW_MARKDOWN_DOCUMENT = "VIEW_MARKDOWN_DOCUMENT",
}
