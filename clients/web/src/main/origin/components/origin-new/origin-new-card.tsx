/**
 * <AUTHOR>
 * @namespace Origin_Components_OriginNew
 * @description New Origin Card
 */

import { OriginStorageInstance } from "@imbricate-hummingbird/origin-central";
import { StyledCard } from "@imbricate-hummingbird/react-components";
import { ORIGIN_INSTANCE_TYPE, OriginInstance } from "@imbricate-hummingbird/transfer-core";
import { UIButton, UIButtonLink, UICardBody, UICardHeader, UIDivider } from "@imbricate-hummingbird/ui";
import { UUIDVersion4 } from "@sudoo/uuid";
import React, { FC } from "react";
import { getOriginStorageInstance, putOriginStorageInstance } from "../../../origin/origin-storage";
import { useOldOriginFormat } from "../../internationalization/hook";
import { ORIGIN_PROFILE } from "../../internationalization/profile";
import { NewOriginSelect } from "./origin-new-select";
import { NewOriginSwitch } from "./origin-new-switch";

export type NewOriginCardProps = {
};

export const NewOriginCard: FC<NewOriginCardProps> = (
    _props: NewOriginCardProps,
) => {

    const originFormat = useOldOriginFormat();

    const [originType, setOriginType] =
        React.useState<ORIGIN_INSTANCE_TYPE | null>(null);
    const [originInstance, setOriginInstance] =
        React.useState<OriginInstance | null>(null);

    return (<React.Fragment>
        <NewOriginSelect
            onSelect={(type: ORIGIN_INSTANCE_TYPE) => {
                setOriginType(type);

                const originStorageInstanceIdentifier: string = UUIDVersion4.generateString();

                switch (type) {

                    case ORIGIN_INSTANCE_TYPE.STACK_API: {
                        setOriginInstance({
                            originStorageInstanceIdentifier,
                            originName: "new origin",
                            type: ORIGIN_INSTANCE_TYPE.STACK_API,
                            basePath: "",
                            authentication: {
                                type: "Basic",
                                value: "",
                            },
                        });
                        return;
                    }
                    case ORIGIN_INSTANCE_TYPE.MOCK: {
                        setOriginInstance({
                            originStorageInstanceIdentifier,
                            originName: "new origin",
                            type: ORIGIN_INSTANCE_TYPE.MOCK,
                        });
                        return;
                    }
                }
            }}
        />
        {(originType && originInstance)
            ? <React.Fragment>
                <StyledCard>
                    <UICardHeader>
                        Create Origin as {originType}
                    </UICardHeader>
                    <UIDivider />
                    <UICardBody>
                        <NewOriginSwitch
                            originType={originType}
                            originInstance={originInstance}
                            onOriginChange={(
                                instance: OriginInstance,
                            ) => {
                                setOriginInstance(instance);
                            }}
                        />
                    </UICardBody>
                </StyledCard>
                <div>
                    <UIButton
                        variant="flat"
                        color="primary"
                        onPress={() => {

                            const currentOrigins: OriginStorageInstance =
                                getOriginStorageInstance();

                            currentOrigins.origins.push(originInstance);

                            putOriginStorageInstance(currentOrigins);
                        }}
                    >
                        Create Origin
                    </UIButton>
                </div>
            </React.Fragment>
            : <StyledCard>
                <UICardHeader>
                    {originFormat.get(ORIGIN_PROFILE.NOT_SURE_WHICH_TYPE_OF_ORIGIN_TO_USE)}
                </UICardHeader>
                <UIDivider />
                <UICardBody
                    className="flex flex-col gap-2"
                >
                    {originFormat.get(ORIGIN_PROFILE.NOT_SURE_WHICH_TYPE_OF_ORIGIN_TO_USE_DESCRIPTION)}
                    <div>
                        <UIButtonLink
                            size="sm"
                            variant="flat"
                            color="primary"
                            href="https://imbricate.io/"
                            isExternal
                        >
                            {originFormat.get(ORIGIN_PROFILE.VIEW_DOCUMENT)}
                        </UIButtonLink>
                    </div>
                </UICardBody>
            </StyledCard>}
    </React.Fragment>);
};
