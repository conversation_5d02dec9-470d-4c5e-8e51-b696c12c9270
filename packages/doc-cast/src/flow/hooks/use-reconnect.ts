/**
 * <AUTHOR>
 * @namespace Flow_Hooks
 * @description Use Reconnect
 */

import { Connection, Edge, reconnectEdge } from "@xyflow/react";
import { useCallback } from "react";

export const useDocCastFlowReconnect = (
    setParsedEdges: React.Dispatch<React.SetStateAction<Edge[]>>,
    edgeReconnectSuccessful: React.RefObject<boolean>,
) => {

    const onReconnect = useCallback((
        oldEdge: Edge,
        newConnection: Connection,
    ) => {

        edgeReconnectSuccessful.current = true;
        setParsedEdges((previousEdges: Edge[]) => {

            return reconnectEdge(oldEdge, newConnection, previousEdges);
        });
    }, []);

    return onReconnect;
};
