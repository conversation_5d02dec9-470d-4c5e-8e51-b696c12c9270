/**
 * <AUTHOR>
 * @namespace Lens_Internationalization_Locale
 * @description Ja-JP
 */

import { LENS_PROFILE } from "../profile";

export const jaJPLensProfile: Record<LENS_PROFILE, string> = {

    [LENS_PROFILE.COMPLETED_AT]: "完了時間",
    [LENS_PROFILE.CREATE_LENS]: "レンズを作成",
    [LENS_PROFILE.CREATE_NEW_LENS]: "新しいレンズを作成",
    [LENS_PROFILE.DANGER_ZONE]: "危険ゾーン",
    [LENS_PROFILE.DELETE_LENS]: "レンズを削除",
    [LENS_PROFILE.DELETE_LENS_DESCRIPTION]: "現在のレンズを削除します",
    [LENS_PROFILE.EDIT_IMBRISCRIPT]: "ImbriScriptを編集",
    [LENS_PROFILE.EDIT_IMBRISCRIPT_DESCRIPTION]: "現在のレンズのImbriScriptを編集します",
    [LENS_PROFILE.EDIT_LENS]: "レンズを編集",
    [LENS_PROFILE.EDIT_LENS_DESCRIPTION]: "現在のレンズの設定を編集します",
    [LENS_PROFILE.EDITING_LENS]: "レンズを編集中",
    [LENS_PROFILE.HIDE_EXTRA_DETAILS]: "詳細を非表示",
    [LENS_PROFILE.IMBRISCRIPT]: "ImbriScript",
    [LENS_PROFILE.IMBRISCRIPT_EXECUTION]: "ImbriScript実行",
    [LENS_PROFILE.IN]: "で",
    [LENS_PROFILE.LENS]: "レンズ",
    [LENS_PROFILE.LENS_IDENTIFIER]: "レンズ識別子",
    [LENS_PROFILE.LENS_SOURCE]: "レンズソース",
    [LENS_PROFILE.LENS_NAME]: "レンズ名",
    [LENS_PROFILE.LENS_TARGET]: "レンズターゲット",
    [LENS_PROFILE.MS]: "ミリ秒",
    [LENS_PROFILE.NAVIGATE_TO_LENS_SOURCE]: "レンズソースに移動",
    [LENS_PROFILE.OPEN_IMBRISCRIPT_DOCUMENT_IN_DRAWER]: "ImbriScriptドキュメントをドロワーで開く",
    [LENS_PROFILE.OPEN_IMBRISCRIPT_DOCUMENT_IN_DRAWER_DESCRIPTION]: "サブパネルの下でImbriScriptドキュメントを開きます",
    [LENS_PROFILE.RELOAD_LENS]: "レンズを再読み込み",
    [LENS_PROFILE.RELOAD_LENS_DESCRIPTION]: "最新の変更を反映するためにレンズを再読み込みします",
    [LENS_PROFILE.RENDER_LENS]: "レンズをレンダリング",
    [LENS_PROFILE.RENDER_LENS_DESCRIPTION]: "レンズをレンダリングします",
    [LENS_PROFILE.SHOW_EXTRA_DETAILS]: "詳細を表示",
    [LENS_PROFILE.UPDATE_LENS]: "レンズを更新",
    [LENS_PROFILE.VIEW_LENS]: "レンズを表示",
    [LENS_PROFILE.VIEW_LENS_DESCRIPTION]: "レンズを表示します",
};
