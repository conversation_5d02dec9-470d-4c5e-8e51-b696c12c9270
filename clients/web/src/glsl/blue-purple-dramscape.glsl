precision mediump float;

uniform float u_time;
uniform vec2 u_resolution;
varying vec2 v_uv;

#define PI 3.14159265359
#define NUM_LAYERS 6.0

float rand(vec2 n) { 
    return fract(sin(dot(n, vec2(12.9898, 4.1414))) * 43758.5453);
}

float noise(vec2 p){
    vec2 ip = floor(p);
    vec2 u = fract(p);
    u = u*u*(3.0-2.0*u);
    
    float res = mix(
        mix(rand(ip),rand(ip+vec2(1.0,0.0)),u.x),
        mix(rand(ip+vec2(0.0,1.0)),rand(ip+vec2(1.0,1.0)),u.x),u.y);
    return res*res;
}

float fbm(vec2 x) {
    float v = 0.0;
    float a = 0.5;
    vec2 shift = vec2(100);
    mat2 rot = mat2(cos(0.5), sin(0.5), -sin(0.5), cos(0.5));
    for (float i = 0.0; i < NUM_LAYERS; ++i) {
        v += a * noise(x);
        x = rot * x * 2.0 + shift;
        a *= 0.5;
    }
    return v;
}

void main() {
    vec2 uv = v_uv;
    vec2 aspect = vec2(u_resolution.x/u_resolution.y, 1.0);
    uv = uv * 2.0 - 1.0;
    uv *= aspect;
    
    float time = u_time * 0.2;
    
    vec3 color1 = vec3(0.1, 0.3, 0.5);
    vec3 color2 = vec3(0.7, 0.2, 0.4);
    vec3 color3 = vec3(0.2, 0.5, 0.8);
    
    float n1 = fbm(uv + time);
    float n2 = fbm(uv - time * 0.5 + n1);
    float n3 = fbm(uv + time * 0.25 + n2);
    
    vec3 finalColor = mix(color1, color2, n1);
    finalColor = mix(finalColor, color3, n2 * n3);
    
    float vignette = length(uv * 0.5);
    vignette = smoothstep(1.0, 0.4, vignette);
    
    finalColor *= vignette;
    finalColor += vec3(n3 * 0.1);
    
    float pulse = sin(time * 2.0) * 0.5 + 0.5;
    finalColor *= 1.0 + pulse * 0.2;
    
    gl_FragColor = vec4(finalColor, 1.0);
    
    #include <colorspace_fragment>
}