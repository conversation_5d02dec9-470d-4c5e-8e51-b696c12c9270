/**
 * <AUTHOR>
 * @namespace Edit_Internationalization
 * @description Hook
 */

import { defaultEmptyFormat, useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { useEffect, useState } from "react";
import { editInternationalization } from "./intl";
import { EDIT_PROFILE } from "./profile";

export const useEditFormat = (): SudooFormat<EDIT_PROFILE> => {

    const locale = useLocale();

    const [format, setFormat] = useState<SudooFormat<EDIT_PROFILE>>(defaultEmptyFormat);

    useEffect(() => {

        const execute = async () => {

            const format = await editInternationalization.format(locale);
            setFormat(format);
        };

        execute();
    }, [locale]);

    return format;
};
