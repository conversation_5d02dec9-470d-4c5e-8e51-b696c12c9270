/**
 * <AUTHOR>
 * @namespace Navigation_Components
 * @description Navigation Config Button
 */

import { HUMMINGBIRD_NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE } from "@imbricate-hummingbird/configuration";
import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { getRouteConfigView } from "@imbricate-hummingbird/navigation-core";
import { UIButtonLink } from "@imbricate-hummingbird/ui";
import { SudooFormat } from "@sudoo/internationalization";
import { FC } from "react";
import { FaCog } from "react-icons/fa";
import { NAVIGATION_PROFILE } from "../internationalization/profile";

export type NavigationConfigButtonProps = {

    readonly displayMode: HUMMINGBIRD_NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE;
    readonly navigationFormat: SudooFormat<NAVIGATION_PROFILE>;
};

export const NavigationConfigButton: FC<NavigationConfigButtonProps> = (
    props: NavigationConfigButtonProps,
) => {

    if (isFormatLoading(props.navigationFormat)) {
        return null;
    }

    if (props.displayMode === HUMMINGBIRD_NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE.TEXT) {

        return (<UIButtonLink
            href={getRouteConfigView()}
            startContent={<div>
                <FaCog
                    className="text-small"
                />
            </div>}
        >
            {props.navigationFormat.get(NAVIGATION_PROFILE.CONFIGURATION)}
        </UIButtonLink>);
    }


    if (props.displayMode === HUMMINGBIRD_NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE.ICON) {

        return (<UIButtonLink
            href={getRouteConfigView()}
        >
            <FaCog
                className="text-xl"
            />
        </UIButtonLink>);
    }

    return null;
};
