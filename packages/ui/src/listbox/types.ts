/**
 * <AUTHOR>
 * @package UI
 * @namespace Listbox
 * @description Types
 */

import type { ReactNode } from "react";
import { UIColor } from "../common/types";

export type UIListboxVariant =
    | "flat"
    | "solid"
    | "bordered"
    | "light"
    | "faded"
    | "shadow";

export type UIListboxItem = {

    readonly key: string;

    readonly className?: string;
    readonly titleClassName?: string;
    readonly descriptionClassName?: string;

    readonly isDisabled?: boolean;

    readonly href?: string;

    readonly color?: UIColor;

    readonly titleContent: ReactNode;
    readonly descriptionContent?: ReactNode;

    readonly startContent?: ReactNode;
    readonly endContent?: ReactNode;
};
