/**
 * <AUTHOR>
 * @namespace Edit_Editors
 * @description Json Editor Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import type { EditJsonEditorProps } from "./json-editor";

const JsonEditorApplication = LazyLoadComponent(
    () => import("./json-editor"),
    "Json Editor Application",
);

export const JsonEditorWrapper: FC<EditJsonEditorProps> = (
    props: EditJsonEditorProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Json Editor Application"
            fullHeight
        />}
    >
        <JsonEditorApplication
            {...props}
        />
    </React.Suspense>);
};
