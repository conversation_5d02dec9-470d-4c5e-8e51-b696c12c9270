/**
 * <AUTHOR>
 * @namespace Debug_Components_Theme
 * @description Theme Quick Access
 */

import { StyledCard } from "@imbricate-hummingbird/react-components";
import { useControlledTheme } from "@imbricate-hummingbird/theme-core";
import { UICardBody } from "@imbricate-hummingbird/ui";
import React from "react";

export const ThemeQuickAccess: React.FC = () => {

    const theme = useControlledTheme();

    return (<StyledCard>
        <UICardBody>
            <div
                className="text-tiny text-foreground-500"
            >
                Dependency Theme: <span className="font-bold text-foreground-900">
                    {theme.dependencyTheme}
                </span>
            </div>
            <div
                className="text-tiny text-foreground-500"
            >
                Controlled Theme: <span className="font-bold text-foreground-900">
                    {theme.theme}
                </span>
            </div>
        </UICardBody>
    </StyledCard>);
};
