/**
 * <AUTHOR>
 * @namespace Recent_Components
 * @description Recent Card
 */

import { RECENT_TYPE, RecentItem } from "@imbricate-hummingbird/configuration";
import { openEditWindow } from "@imbricate-hummingbird/navigation-core";
import { StyledCard } from "@imbricate-hummingbird/react-components";
import { useNavigateDatabaseDocumentsView, useNavigateDocumentView, useNavigateLensView } from "@imbricate-hummingbird/react-navigation";
import { FC } from "react";
import { RecentCardDatabase } from "./recent-card/database-card";
import { RecentCardDocument } from "./recent-card/document-card";
import { RecentCardLens } from "./recent-card/lens-card";
import { RecentCardProperty } from "./recent-card/property-card";
import { RecentCardUnknown } from "./recent-card/unknown-card";

export type RecentCardProps = {

    readonly item: RecentItem<RECENT_TYPE>;
};

export const RecentCard: FC<RecentCardProps> = (
    props: RecentCardProps,
) => {

    const navigateToLens = useNavigateLensView();
    const navigateToDatabase = useNavigateDatabaseDocumentsView();
    const navigateToDocument = useNavigateDocumentView();

    const getCardItem = () => {

        // RECENT_TYPE SWITCH
        switch (props.item.type) {
            case RECENT_TYPE.LENS:
                return (<RecentCardLens
                    item={props.item as RecentItem<RECENT_TYPE.LENS>}
                />);
            case RECENT_TYPE.DATABASE:
                return (<RecentCardDatabase
                    item={props.item as RecentItem<RECENT_TYPE.DATABASE>}
                />);
            case RECENT_TYPE.DOCUMENT:
                return (<RecentCardDocument
                    item={props.item as RecentItem<RECENT_TYPE.DOCUMENT>}
                />);
            case RECENT_TYPE.PROPERTY:
                return (<RecentCardProperty
                    item={props.item as RecentItem<RECENT_TYPE.PROPERTY>}
                />);
        }

        return (<RecentCardUnknown
            item={props.item}
        />);
    };

    return (<StyledCard
        className="w-full min-h-0 min-w-0"
        isPressable
        onPress={() => {

            // RECENT_TYPE SWITCH
            switch (props.item.type) {
                case RECENT_TYPE.LENS: {

                    const item = props.item as RecentItem<RECENT_TYPE.LENS>;
                    navigateToLens(
                        item.payload.lensIdentifier,
                        {
                            replace: true,
                        },
                    );
                    return;
                }
                case RECENT_TYPE.DATABASE: {

                    const item = props.item as RecentItem<RECENT_TYPE.DATABASE>;
                    navigateToDatabase(
                        item.payload.originUniqueIdentifier,
                        item.payload.databaseUniqueIdentifier,
                        {
                            replace: true,
                        },
                    );
                    return;
                }
                case RECENT_TYPE.DOCUMENT: {

                    const item = props.item as RecentItem<RECENT_TYPE.DOCUMENT>;
                    navigateToDocument(
                        item.payload.originUniqueIdentifier,
                        item.payload.databaseUniqueIdentifier,
                        item.payload.documentUniqueIdentifier,
                        {
                            replace: true,
                        },
                    );
                    return;
                }
                case RECENT_TYPE.PROPERTY: {

                    const item = props.item as RecentItem<RECENT_TYPE.PROPERTY>;
                    openEditWindow(
                        item.payload.originUniqueIdentifier,
                        item.payload.databaseUniqueIdentifier,
                        item.payload.documentUniqueIdentifier,
                        item.payload.propertyUniqueIdentifier,
                    );
                    return;
                }
            }
        }}
    >
        <div
            className="w-full h-full overflow-visible"
        >
            {getCardItem()}
        </div>
    </StyledCard>);
};
