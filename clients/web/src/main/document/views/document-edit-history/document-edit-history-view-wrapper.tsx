/**
 * <AUTHOR>
 * @namespace Document_Views_DocumentEditHistory
 * @description Document Edit History View Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const DocumentEditHistoryViewApplication = LazyLoadComponent(
    () => import("./document-edit-history"),
    "Document Edit History View Application",
);

export const DocumentEditHistoryViewWrapper: FC = () => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Document Edit History View Application"
            fullHeight
        />}
    >
        <DocumentEditHistoryViewApplication />
    </React.Suspense>);
};
