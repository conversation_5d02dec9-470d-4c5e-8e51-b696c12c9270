/**
 * <AUTHOR>
 * @namespace Lens
 * @description Lens View Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const LensViewApplication = LazyLoadComponent(
    () => import("./lens-view-fetcher"),
    "Lens View Application",
);

export const LensViewWrapper: FC = () => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Lens View Application"
            fullHeight
        />}
    >
        <LensViewApplication />
    </React.Suspense>);
};
