/**
 * <AUTHOR>
 * @namespace Data_Internationalization_Locale
 * @description En-US
 */

import { DATA_PROFILE } from "../profile";

export const enUSDataProfile: Record<DATA_PROFILE, string> = {

    [DATA_PROFILE.CONFIRM_PROCEED_WITH_ERROR]: "Confirm ignore error and continue",
    [DATA_PROFILE.ERROR_OCCURRED]: "Error Occurred",
    [DATA_PROFILE.ERROR_OCCURRED_WHEN_PROCESSING_DATA]: "Error Occurred When Processing Data",
    [DATA_PROFILE.PROCEED_WITH_ERROR]: "Proceed With Error",
    [DATA_PROFILE.PROCEED_WITH_ERROR_DESCRIPTION]: "You can continue use the application with error, but the application may not work as expected. If so, please make sure to export your work and unsaved data before proceeding.",
    [DATA_PROFILE.REASON]: "Reason:",
    [DATA_PROFILE.RETRY]: "Retry",
    [DATA_PROFILE.RETRY_AGAIN]: "Retry Again",
};
