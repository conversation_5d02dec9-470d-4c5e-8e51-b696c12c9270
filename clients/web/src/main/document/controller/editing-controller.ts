/**
 * <AUTHOR>
 * @namespace Document_Controller
 * @description Editing Controller
 */

import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { TransferDocument, TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { IImbricateDatabase, IImbricateProperty, IMBRICATE_PROPERTY_TYPE, ImbricatePropertyGenerator } from "@imbricate/core";
import { UUIDVersion1 } from "@sudoo/uuid";
import { rootLogger } from "../../log/logger";
import { flattenPropertyDrafts } from "../../property/draft/flatten-drafts";
import { submitPropertyDrafts } from "../../property/utils/submit-drafts";
import { cloneAndFillDocumentProperties } from "../util/clone-properties";

const logger = rootLogger.fork({
    scopes: [
        "Document",
        "Controller",
        "EditingController",
    ],
});

export type DocumentEditingControllerEditingDocument = {

    readonly document: TransferDocument;
    readonly originalDrafts: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[];

    newDrafts: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[];
};

export class DocumentEditingController {

    public static create(
        origin: ImbricateOriginObject,
        database: IImbricateDatabase,
    ): DocumentEditingController {

        return new DocumentEditingController(origin, database);
    }

    private readonly _origin: ImbricateOriginObject;
    private readonly _database: IImbricateDatabase;

    private readonly _listeningStatusChange: Set<(count: number) => void> = new Set();
    private readonly _listeningVersionChange: Set<() => void> = new Set();
    private readonly _editingDocuments: Map<string, DocumentEditingControllerEditingDocument>;
    private readonly _creatingDocuments: Map<string, TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[]>;

    private constructor(
        origin: ImbricateOriginObject,
        database: IImbricateDatabase,
    ) {

        this._origin = origin;
        this._database = database;

        this._listeningStatusChange = new Set();
        this._listeningVersionChange = new Set();
        this._editingDocuments = new Map();
        this._creatingDocuments = new Map();
    }

    public listenStatusChange(onChange: (count: number) => void): () => void {

        this._listeningStatusChange.add(onChange);
        return () => {
            this._listeningStatusChange.delete(onChange);
        };
    }

    public listenVersionChange(onChange: () => void): () => void {

        this._listeningVersionChange.add(onChange);

        return () => {
            this._listeningVersionChange.delete(onChange);
        };
    }

    public startCreatingDocument(): string {

        const uuid: string = UUIDVersion1.generateString();

        this._creatingDocuments.set(
            uuid,
            cloneAndFillDocumentProperties(
                [],
                this._database.schema,
            ),
        );

        this._notify();

        return uuid;
    }

    public getCreatingDocuments(): Array<[string, TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[]]> {

        return Array.from(this._creatingDocuments.entries());
    }

    public getCreatingDocument(uuid: string): TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[] | null {

        return this._creatingDocuments.get(uuid) ?? null;
    }

    public updateCreatingDocument(
        uuid: string,
        propertyIdentifier: string,
        value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>,
    ): void {

        let creatingDocument: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[] | undefined =
            this._creatingDocuments.get(uuid);
        if (!creatingDocument) {
            return;
        }

        creatingDocument = creatingDocument.map((
            draft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>,
        ) => {

            if (draft.key === propertyIdentifier) {
                return value;
            }
            return draft;
        });

        this._creatingDocuments.set(uuid, creatingDocument);

        this._notify();
    }

    public saveCreatingDocument(
        uuid: string,
    ): void {

        const properties: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[] | undefined =
            this._creatingDocuments.get(uuid);

        if (!properties) {
            return;
        }

        this._database.createDocument(
            (
                generator: ImbricatePropertyGenerator<IMBRICATE_PROPERTY_TYPE>,
            ) => {

                const generatedProperties: IImbricateProperty<IMBRICATE_PROPERTY_TYPE>[] =
                    properties.map((
                        draft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>,
                    ) => {

                        return generator(
                            draft.key,
                            draft.type,
                            draft.value,
                            draft.variant,
                        );
                    });

                return generatedProperties;
            });

        this._creatingDocuments.delete(uuid);

        this._notifyVersionChange();
    }

    public cancelCreatingDocument(
        uuid: string,
    ): void {

        this._creatingDocuments.delete(uuid);
        this._notify();
    }

    public getEditingDocument(
        document: TransferDocument,
    ): DocumentEditingControllerEditingDocument | undefined {

        return this._editingDocuments.get(document.documentUniqueIdentifier);
    }

    public startEditingDocument(document: TransferDocument): void {

        const properties = document.properties;

        if (typeof properties === "symbol") {
            logger.error("Properties is symbol", {
                documentUniqueIdentifier: document.documentUniqueIdentifier,
            });
            return;
        }

        this._editingDocuments.set(
            document.documentUniqueIdentifier,
            {
                document,
                originalDrafts: flattenPropertyDrafts(properties),
                newDrafts: [],
            },
        );

        this._notify();
    }

    public setUpdatingProperty(
        document: TransferDocument,
        propertyIdentifier: string,
        value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>,
    ): void {

        const editingDocument: DocumentEditingControllerEditingDocument | undefined =
            this._editingDocuments.get(document.documentUniqueIdentifier);

        if (!editingDocument) {
            return;
        }

        const currentEditingDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> | undefined =
            editingDocument.newDrafts.find((
                draft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>,
            ) => draft.key === propertyIdentifier);

        if (!currentEditingDraft) {

            editingDocument.newDrafts.push(value);
        } else {

            editingDocument.newDrafts = editingDocument.newDrafts.map((
                draft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>,
            ) => {

                if (draft.key === propertyIdentifier) {
                    return value;
                }
                return draft;
            });
        }

        this._editingDocuments.set(document.documentUniqueIdentifier, editingDocument);

        this._notify();
    }

    public cancelEditingDocument(document: TransferDocument): void {

        this._editingDocuments.delete(document.documentUniqueIdentifier);
        this._notify();
    }

    public async saveEditingDocument(document: TransferDocument): Promise<void> {

        const editingDocument: DocumentEditingControllerEditingDocument | undefined =
            this._editingDocuments.get(document.documentUniqueIdentifier);

        if (!editingDocument) {
            return;
        }

        await submitPropertyDrafts(
            this._origin.origin.uniqueIdentifier,
            this._database.uniqueIdentifier,
            document.documentUniqueIdentifier,
            editingDocument.newDrafts,
        );

        this._editingDocuments.delete(document.documentUniqueIdentifier);
        this._notifyVersionChange();
    }

    public isDocumentEditing(document: TransferDocument): boolean {

        return this._editingDocuments.has(document.documentUniqueIdentifier);
    }

    private _notify(): void {

        for (const listener of this._listeningStatusChange) {
            listener(this._editingDocuments.size);
        }
    }

    private _notifyVersionChange(): void {

        for (const listener of this._listeningVersionChange) {
            listener();
        }
    }
}
