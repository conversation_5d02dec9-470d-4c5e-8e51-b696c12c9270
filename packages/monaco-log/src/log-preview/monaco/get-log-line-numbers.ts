/**
 * <AUTHOR>
 * @namespace Monaco_Log_Preview_Monaco
 * @description Get Log Line Numbers
 */

import { ImbriScriptContainerLog } from "@imbricate-hummingbird/script-core";
import { formatMonacoLogPreviewTime } from "../util/format-time";

export const getLogLineNumberMethod = (
    logs: ImbriScriptContainerLog[],
): ((lineNumber: number) => string) => {

    const lineNumberList: string[] = [];

    for (const log of logs) {

        const logLines = log.message.split("\n");

        lineNumberList.push(`[${formatMonacoLogPreviewTime(log.timestamp)}] ${lineNumberList.length + 1}`);

        for (let i = 1; i < logLines.length; i++) {

            lineNumberList.push(
                String(lineNumberList.length + 1),
            );
        }
    }

    return (lineNumber: number) => {
        return lineNumberList[lineNumber - 1];
    };
};
