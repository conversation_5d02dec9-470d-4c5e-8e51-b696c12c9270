/**
 * <AUTHOR>
 * @namespace Search_Component
 * @description Search Result Body
 */

import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { IMBRICATE_SEARCH_TARGET_TYPE, ImbricateSearchItem } from "@imbricate/core";
import React, { FC } from "react";
import { MarkdownSearchResultBody } from "./markdown-search-result-body";

export type SearchResultBodyProps = {

    readonly origin: ImbricateOriginObject;
    readonly searchItem: ImbricateSearchItem;
};

export const SearchResultBody: FC<SearchResultBodyProps> = (
    props: SearchResultBodyProps,
) => {

    // IMBRICATE_SEARCH_TARGET_TYPE SWITCH
    switch (props.searchItem.target.type) {

        case IMBRICATE_SEARCH_TARGET_TYPE.MARKDOWN: {

            return (<MarkdownSearchResultBody
                origin={props.origin}
                searchItem={props.searchItem}
            />);
        }
    }

    return (<div>
        {props.searchItem.secondary.trim()}
    </div>);
};
