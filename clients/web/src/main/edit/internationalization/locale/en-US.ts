/**
 * <AUTHOR>
 * @namespace Edit_Internationalization_Locale
 * @description En-US
 */

import { EDIT_PROFILE } from "../profile";

export const enUSEditProfile: Record<EDIT_PROFILE, string> = {

    [EDIT_PROFILE.CLOSE_EDITOR]: "Close Editor",
    [EDIT_PROFILE.CLOSE_EDITOR_DESCRIPTION]: "Close the editor",
    [EDIT_PROFILE.EDITOR]: "Editor",
    [EDIT_PROFILE.EXECUTE_IMBRISCRIPT]: "Execute ImbriScript",
    [EDIT_PROFILE.EXECUTE_IMBRISCRIPT_DESCRIPTION]: "Execute the ImbriScript as Action",
    [EDIT_PROFILE.FORMAT_IMBRISCRIPT]: "Format ImbriScript",
    [EDIT_PROFILE.FORMAT_IMBRISCRIPT_DESCRIPTION]: "Format the ImbriScript as JavaScript or TypeScript",
    [EDIT_PROFILE.IMBRISCRIPT]: "ImbriScript",
    [EDIT_PROFILE.IMBRISCRIPT_LENS_PREVIEW]: "ImbriScript Lens Preview",
    [EDIT_PROFILE.IMBRISCRIPT_LENS_PREVIEW_DESCRIPTION]: "Preview the ImbriScript as Lens",
    [EDIT_PROFILE.MARKDOWN]: "Markdown",
    [EDIT_PROFILE.MARKDOWN_PREVIEW]: "Markdown Preview",
    [EDIT_PROFILE.MARKDOWN_PREVIEW_DESCRIPTION]: "Preview the markdown",
    [EDIT_PROFILE.SAVE]: "Save",
    [EDIT_PROFILE.USE_MINIMAP_SCROLL_BAR]: "Use Minimap Scroll Bar",
    [EDIT_PROFILE.USE_MINIMAP_SCROLL_BAR_DESCRIPTION]: "Display scroll bar as minimap style",
    [EDIT_PROFILE.USE_REGULAR_SCROLL_BAR]: "Use Regular Scroll Bar",
    [EDIT_PROFILE.USE_REGULAR_SCROLL_BAR_DESCRIPTION]: "Display scroll bar as regular style",
    [EDIT_PROFILE.VIEW]: "View",
    [EDIT_PROFILE.VIEW_IN_PREVIEW]: "View in Preview",
    [EDIT_PROFILE.VIEW_IN_PREVIEW_DESCRIPTION]: "View the document in preview",
};
