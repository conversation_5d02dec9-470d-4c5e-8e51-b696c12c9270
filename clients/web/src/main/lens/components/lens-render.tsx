/**
 * <AUTHOR>
 * @namespace Lens_Components
 * @description Lens Render
 */

import { UseMagicButtonResult } from "@/common/components/magic-button/hooks/use-magic-button";
import { LENS_CONFIG_SOURCE, LensConfigItem } from "@imbricate-hummingbird/configuration";
import { FC } from "react";
import { RenderImbriscriptLensWrapper } from "./renders/render-imbriscript-wrapper";

export type LensRenderProps = {

    readonly version: number;
    readonly triggerThrottleReload: () => void;

    readonly lensItem: LensConfigItem<LENS_CONFIG_SOURCE>;

    readonly magicButtonResult: UseMagicButtonResult;
};

export const LensRender: FC<LensRenderProps> = (
    props: LensRenderProps,
) => {

    switch (props.lensItem.source) {

        case LENS_CONFIG_SOURCE.IMBRISCRIPT: {

            return (<RenderImbriscriptLensWrapper
                version={props.version}
                triggerThrottleReload={props.triggerThrottleReload}
                lensItem={props.lensItem}
                magicButtonResult={props.magicButtonResult}
            />);
        }
    }
};
