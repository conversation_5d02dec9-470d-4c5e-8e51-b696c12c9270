/**
 * <AUTHOR>
 * @namespace Lens
 * @description Lens View
 */

import { HeaderGroup } from "@/common/components/header-group/header-group";
import { HEADER_GROUP_GROUP } from "@/common/components/header-group/types";
import { MagicButton } from "@/common/components/magic-button/magic-button";
import { LENS_CONFIG_SOURCE, LensConfigItem } from "@imbricate-hummingbird/configuration";
import { useTitleWithAsyncFormat } from "@imbricate-hummingbird/internationalization";
import { getRouteLensEditView } from "@imbricate-hummingbird/navigation-core";
import { UINavbar } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { FaEdit } from "react-icons/fa";
import { Link } from "react-router-dom";
import { LensRender } from "../../components/lens-render";
import { useLensViewMagicButton } from "../../hooks/lens-view/use-lens-view-magic-button";
import { useLensFormat } from "../../internationalization/hook";
import { LENS_PROFILE } from "../../internationalization/profile";

export type LensViewProps = {

    readonly targetLens: LensConfigItem<LENS_CONFIG_SOURCE>;
    readonly version: number;
    readonly updateVersion: () => void;
};

export const LensView: FC<LensViewProps> = (
    props: LensViewProps,
) => {

    const lensFormat = useLensFormat();

    const magicButtonResult = useLensViewMagicButton({
        targetLens: props.targetLens,
        updateVersion: props.updateVersion,
    });

    useTitleWithAsyncFormat(
        lensFormat,
        () => {
            return [
                props.targetLens.lensName,
                lensFormat.get(LENS_PROFILE.LENS),
            ];
        },
        [props.targetLens.lensIdentifier],
    );

    return (<div
        className="flex flex-col gap-2 pb-4 overflow-auto h-full min-h-full"
    >
        <UINavbar
            isFullWidth
            isBordered
            startContent={<HeaderGroup
                group={HEADER_GROUP_GROUP.LENS}
                payload={{
                    lensIdentifier: props.targetLens.lensIdentifier,
                }}
            />}
            coreClassName="flex flex-col gap-0"
            coreContent={<React.Fragment>
                <p className="font-bold text-xl">
                    {props.targetLens.lensName}
                </p>
                <Link
                    className="h-2 text-tiny flex gap-1 items-center"
                    to={getRouteLensEditView(props.targetLens.lensIdentifier)}
                    replace
                >
                    <FaEdit />
                    <div>
                        {lensFormat.get(LENS_PROFILE.EDIT_LENS)}
                    </div>
                </Link>
            </React.Fragment>}
            endContent={<MagicButton
                magicButtonResult={magicButtonResult}
            />}
        />
        <div
            className="pr-2 h-full"
        >
            <LensRender
                triggerThrottleReload={() => {
                    props.updateVersion();
                }}
                version={props.version}
                lensItem={props.targetLens}
                magicButtonResult={magicButtonResult}
            />
        </div>
    </div>);
};
