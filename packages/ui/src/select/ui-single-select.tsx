/**
 * <AUTHOR>
 * @package UI
 * @namespace Select
 * @description Single Select
 */

import { Select, SelectItem, SelectedItems } from "@heroui/select";
import type { ChangeEvent, FC } from "react";
import { UISelectCommonProps, UISelectItemData } from "./types";
import { parseUISelectSelectedItems } from "./util";

export type UISingleSelectProps = {

    readonly defaultSelectedKey?: string;
    readonly selectedKey?: string;
    readonly onSelectedKeyChange?: (newSelectedKey: string) => void;
} & UISelectCommonProps;

export const UISingleSelect: FC<UISingleSelectProps> = (
    props: UISingleSelectProps,
) => {

    return (
        <Select
            aria-label={props.ariaLabel}
            selectionMode="single"

            label={props.label}
            startContent={props.startContent}
            endContent={props.endContent}
            errorMessage={props.errorMessage}

            className={props.className}

            size={props.size}
            color={props.color}

            fullWidth={props.isFullWidth}
            isDisabled={props.isDisabled}
            isLoading={props.isLoading}
            isInvalid={props.isInvalid}

            renderValue={typeof props.renderValue === "function"
                ? (value: SelectedItems) => {

                    return parseUISelectSelectedItems(
                        value,
                        props.renderValue!,
                    );
                }
                : undefined}

            defaultSelectedKeys={typeof props.defaultSelectedKey === "string"
                ? new Set([props.defaultSelectedKey])
                : undefined}
            selectedKeys={typeof props.selectedKey === "string"
                ? new Set([props.selectedKey])
                : undefined}
            onChange={typeof props.onSelectedKeyChange === "function"
                ? (event: ChangeEvent<HTMLSelectElement>) => {
                    props.onSelectedKeyChange!(event.target.value);
                }
                : undefined}
        >
            {props.items.map((
                item: UISelectItemData,
            ) => {

                return (<SelectItem
                    key={item.itemKey}
                    className={item.className}
                    isDisabled={item.isDisabled}
                    textValue={item.textValue}
                    color={item.color}
                    variant={item.variant}
                    startContent={item.startContent}
                    endContent={item.endContent}
                >
                    {item.content}
                </SelectItem>);
            })}
        </Select>
    );
};
