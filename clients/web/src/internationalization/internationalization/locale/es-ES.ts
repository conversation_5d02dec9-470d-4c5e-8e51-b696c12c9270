/**
 * <AUTHOR>
 * @namespace Internationalization_Internationalization_Locale
 * @description Es-ES
 */

import { IETF_LOCALE } from "@sudoo/locale";

// spell-checker: disable
export const esESInternationalizationProfile: Record<IETF_LOCALE, string> = {

    [IETF_LOCALE.CHINESE_SIMPLIFIED]: "Chino (simplificado)",
    [IETF_LOCALE.CHINESE_TRADITIONAL]: "Chino (tradicional)",
    [IETF_LOCALE.ENGLISH_CANADA]: "Inglés (Canadá)",
    [IETF_LOCALE.ENGLISH_UNITED_KINGDOM]: "Inglés (Reino Unido)",
    [IETF_LOCALE.ENGLISH_UNITED_STATES]: "Inglés (Estados Unidos)",
    [IETF_LOCALE.FRENCH_CANADA]: "<PERSON>an<PERSON><PERSON> (Canadá)",
    [IETF_LOCALE.FRENCH_FRANCE]: "Francés (Francia)",
    [IETF_LOCALE.JAPANESE_JAPAN]: "Japon<PERSON>",
    [IETF_LOCALE.KOREAN_KOREA]: "Coreano (Corea)",
    [IETF_LOCALE.MAORI_NEW_ZEALAND]: "Maori",
    [IETF_LOCALE.RUSSIAN_RUSSIA]: "Ruso",
    [IETF_LOCALE.SPANISH_MEXICO]: "Español (México)",
    [IETF_LOCALE.SPANISH_SPAIN]: "Español (España)",
};
// spell-checker: enable
