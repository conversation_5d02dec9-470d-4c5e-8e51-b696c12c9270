/**
 * <AUTHOR>
 * @namespace Edit
 * @description Edit Wrapper
 */

import "../index.css";

import { FullLoadingWrapper, LazyLoadComponent } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const EditApplication = LazyLoadComponent(
    () => import("./edit-application"),
    "Edit Application",
);

export const EditWrapper: FC = () => {

    return (<React.Suspense
        fallback={<FullLoadingWrapper
            debugDescription="Edit Application"
        />}
    >
        <EditApplication />
    </React.Suspense>);
};
