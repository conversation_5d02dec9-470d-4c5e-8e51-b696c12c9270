/**
 * <AUTHOR>
 * @package UI
 * @namespace Modal
 * @description Modal Footer
 */

import { ModalFooter } from "@heroui/modal";
import type { FC, ReactNode } from "react";

export type UIModalFooterProps = {

    readonly className?: string;

    readonly children?: ReactNode;
};

export const UIModalFooter: FC<UIModalFooterProps> = (
    props: UIModalFooterProps,
) => {

    return (<ModalFooter
        className={props.className}
    >
        {props.children}
    </ModalFooter>);
};
