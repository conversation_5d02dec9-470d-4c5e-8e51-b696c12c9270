/**
 * <AUTHOR>
 * @namespace Config_Panels
 * @description Markdown Panel
 */

import React, { FC } from "react";
import { ConfigMarkdownDefaultViewerCard } from "../components/markdown/markdown-default-viewer";

export type ConfigMarkdownPanelProps = {
};

// LAZY LOAD ONLY
const ConfigMarkdownPanel: FC<ConfigMarkdownPanelProps> = (
    _props: ConfigMarkdownPanelProps,
) => {

    return (<div
        className="flex flex-col gap-2 pr-2 py-2 flex-1"
    >
        <ConfigMarkdownDefaultViewerCard />
    </div>);
};
export default ConfigMarkdownPanel;
