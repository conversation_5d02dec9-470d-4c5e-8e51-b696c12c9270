/**
 * <AUTHOR>
 * @namespace Data
 * @description Cache
 */

import { ImbricateDocumentQuery } from "@imbricate/core";

export type DataCache<T> = {

    readonly cache: T;
    readonly storedAt: number;
};

export const buildOriginCacheKey = (
    originUniqueIdentifier: string,
): string => {

    return `origin-${originUniqueIdentifier}`;
};

export const buildOriginOriginActionsCacheKey = (
    originUniqueIdentifier: string,
): string => {

    return `origin-origin-actions-${originUniqueIdentifier}`;
};

export const buildWideOriginCacheKey = (
    databaseUniqueIdentifier: string,
): string => {

    return `wide-origin-${databaseUniqueIdentifier}`;
};

export const buildDocumentCacheKey = (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
): string => {

    return `document-${originUniqueIdentifier}-${databaseUniqueIdentifier}-${documentUniqueIdentifier}`;
};

export const buildQueryDocumentsCacheKey = (
    databaseUniqueIdentifier: string,
    query: ImbricateDocumentQuery,
): string => {

    return `query-documents-${databaseUniqueIdentifier}-${JSON.stringify(query)}`;
};

export const buildWideOriginQueryDocumentsCacheKey = (
    databaseUniqueIdentifier: string,
    query: ImbricateDocumentQuery,
): string => {

    return `wide-origin-query-documents-${databaseUniqueIdentifier}-${JSON.stringify(query)}`;
};

export const buildDatabaseCacheKey = (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
): string => {

    return `database-${originUniqueIdentifier}-${databaseUniqueIdentifier}`;
};

export const buildWideOriginDatabasesCacheKey = (
    originUniqueIdentifier: string,
): string => {

    return `wide-origin-databases-${originUniqueIdentifier}`;
};

export const buildDatabasesCacheKey = (
    originUniqueIdentifier: string,
): string => {

    return `databases-${originUniqueIdentifier}`;
};
