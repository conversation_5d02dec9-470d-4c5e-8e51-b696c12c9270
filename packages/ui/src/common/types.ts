/**
 * <AUTHOR>
 * @package UI
 * @namespace Common
 * @description Types
 */

export type UIColor =
    | "default"
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "danger";

export const isValidUIColor = (
    color?: string,
): color is UIColor => {

    if (typeof color !== "string") {
        return true;
    }

    return ["default", "primary", "secondary", "success", "warning", "danger"]
        .includes(color);
};

export type UISize =
    | "sm"
    | "md"
    | "lg";

export type UIRadius =
    | "none"
    | "sm"
    | "md"
    | "lg"
    | "full";

export type UIShadow =
    | "none"
    | "sm"
    | "md"
    | "lg";
