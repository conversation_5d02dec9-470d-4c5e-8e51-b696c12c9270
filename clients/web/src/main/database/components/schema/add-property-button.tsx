/**
 * <AUTHOR>
 * @namespace Database_Components_Schema
 * @description Add Property Button
 */

import { StyledCard } from "@imbricate-hummingbird/react-components";
import { getDefaultTransferDatabaseSchemaVariant } from "@imbricate-hummingbird/transfer-core";
import { UIButton, UICardHeader } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchema } from "@imbricate/core";
import { UUIDVersion1 } from "@sudoo/uuid";
import { FC } from "react";
import { useDatabaseFormat } from "../../internationalization/hook";
import { DATABASE_PROFILE } from "../../internationalization/profile";

export type DatabaseSchemaAddPropertyButtonProps = {

    readonly schema: ImbricateDatabaseSchema;
    readonly setSchema: (schema: ImbricateDatabaseSchema) => void;
};

export const DatabaseSchemaAddPropertyButton: FC<DatabaseSchemaAddPropertyButtonProps> = (
    props: DatabaseSchemaAddPropertyButtonProps,
) => {

    const databaseFormat = useDatabaseFormat();

    return (<StyledCard>
        <UICardHeader>
            <UIButton
                variant="flat"
                onPress={() => {

                    props.setSchema({
                        properties: [
                            ...props.schema.properties,
                            {
                                propertyIdentifier: UUIDVersion1.generateString(),
                                propertyName: "New Property",
                                propertyType: IMBRICATE_PROPERTY_TYPE.STRING,
                                propertyOptions: {},
                                propertyVariant: getDefaultTransferDatabaseSchemaVariant(
                                    IMBRICATE_PROPERTY_TYPE.STRING,
                                ),
                            },
                        ],
                    });
                }}
            >
                {databaseFormat.get(DATABASE_PROFILE.ADD_PROPERTY)}
            </UIButton>
        </UICardHeader>
    </StyledCard>);
};
