/**
 * <AUTHOR>
 * @namespace Config_PanelsWrapper
 * @description Debug Panel Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const ConfigDebugPanel = LazyLoadComponent(
    () => import("../panels/debug-panel"),
    "Config Debug Panel",
);

export type ConfigDebugPanelWrapperProps = {
};

export const ConfigDebugPanelWrapper: FC<ConfigDebugPanelWrapperProps> = (
    _props: ConfigDebugPanelWrapperProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Config Debug Panel"
            fullHeight
        />}
    >
        <ConfigDebugPanel />
    </React.Suspense>);
};
