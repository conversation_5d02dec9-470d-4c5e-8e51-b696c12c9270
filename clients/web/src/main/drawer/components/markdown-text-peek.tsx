/**
 * <AUTHOR>
 * @namespace Drawer_Components
 * @description Markdown Text Peek
 */

import { UIDrawerBody } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { MarkdownRender } from "../../common/components/markdown-render";

export type DrawerMarkdownTextPeekProps = {

    readonly markdownText: string;
};

// LAZY LOAD ONLY
const DrawerMarkdownTextPeek: FC<DrawerMarkdownTextPeekProps> = (
    props: DrawerMarkdownTextPeekProps,
) => {

    return (<UIDrawerBody
        className="relative"
    >
        <MarkdownRender
            rawText={props.markdownText}
        />
    </UIDrawerBody>);
};
export default DrawerMarkdownTextPeek;
