/**
 * <AUTHOR>
 * @namespace Drawer_Component_Wrappers
 * @description Text Raw Peek Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import { DrawerTextRawPeekProps } from "../components/text-raw-peek";

const DrawerTextRawPeek = LazyLoadComponent(
    () => import("../components/text-raw-peek"),
    "Drawer Text Raw Peek",
);

export const DrawerTextRawPeekWrapper: FC<DrawerTextRawPeekProps> = (
    props: DrawerTextRawPeekProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Drawer Text Raw Peek"
            fullHeight
        />}
    >
        <DrawerTextRawPeek
            {...props}
        />
    </React.Suspense>);
};
