{"name": "@imbricate-hummingbird/react-navigation", "description": "Navigation implementation for React", "private": true, "main": "src/index.ts", "scripts": {"test": "jest", "coverage": "jest --coverage", "compile": "tsc --project ./typescript/tsconfig.compile.json", "lint": "eslint src"}, "dependencies": {"@imbricate-hummingbird/configuration": "workspace:*", "@imbricate-hummingbird/debug": "workspace:*", "@imbricate-hummingbird/global-symbol": "workspace:*", "@imbricate-hummingbird/interceptor-advanced": "workspace:*", "@imbricate-hummingbird/interceptor-core": "workspace:*", "@imbricate-hummingbird/internationalization": "workspace:*", "@imbricate-hummingbird/logger": "workspace:*", "@imbricate-hummingbird/navigation-core": "workspace:*", "@imbricate-hummingbird/origin-central": "workspace:*", "@imbricate-hummingbird/origin-central-web-worker": "workspace:*", "@imbricate-hummingbird/platformless": "workspace:*", "@imbricate-hummingbird/react-common": "workspace:*", "@imbricate-hummingbird/react-components": "workspace:*", "@imbricate-hummingbird/react-origin-central": "workspace:*", "@imbricate-hummingbird/react-store": "workspace:*", "@imbricate-hummingbird/transfer-core": "workspace:*", "@imbricate-hummingbird/ui": "workspace:*", "@sudoo/internationalization": "2.1.0", "@sudoo/internationalization-react": "1.6.0", "clsx": "catalog:", "react-dom": "catalog:", "react-icons": "catalog:", "react-router-dom": "catalog:"}, "devDependencies": {"@imbricate-hummingbird/config": "workspace:*", "@types/react": "catalog:", "@types/react-dom": "catalog:", "eslint": "catalog:", "react": "catalog:"}}