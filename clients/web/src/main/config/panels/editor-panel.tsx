/**
 * <AUTHOR>
 * @namespace Main_Config_Panels_EditorPanel
 * @description Editor Panel
 */

import React, { FC } from "react";
import { ConfigEditorMonacoCard } from "../components/editor/monaco-card";

export type ConfigEditorPanelProps = {
};

export const ConfigEditorPanel: FC<ConfigEditorPanelProps> = (
    _props: ConfigEditorPanelProps,
) => {

    return (<div
        className="flex flex-col gap-2 pr-2 py-2 flex-1"
    >
        <ConfigEditorMonacoCard />
    </div>);
};
