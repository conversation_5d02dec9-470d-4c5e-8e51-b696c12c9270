/**
 * <AUTHOR>
 * @namespace Property_Utils
 * @description Submit Drafts
 */

import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { OriginWorkerDataCentral } from "@imbricate-hummingbird/origin-central-web-worker";
import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { createSubmitPropertyDraftsAction } from "../actions/submit-property-drafts";

export const submitPropertyDrafts = async (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    newDrafts: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[],
): Promise<void> => {

    await ActionCentral.getInstance().executeAction(
        async (
            actionIdentifier: string,
            recordIdentifier: string,
        ) => {

            await OriginWorkerDataCentral.getInstance().documentMergeProperties(
                actionIdentifier,
                recordIdentifier,
                originUniqueIdentifier,
                databaseUniqueIdentifier,
                documentUniqueIdentifier,
                newDrafts,
            );
        },
        createSubmitPropertyDraftsAction(
            {
                originUniqueIdentifier,
                databaseUniqueIdentifier,
                documentUniqueIdentifier,
                newDrafts,
            },
            {
                executer: import.meta.url,
            },
        ),
    );
};
