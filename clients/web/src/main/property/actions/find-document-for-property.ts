/**
 * <AUTHOR>
 * @namespace Property_Actions
 * @description Find Document For Property
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createFindDocumentForPropertyAction = (
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    propertyUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Find Document For Property",
        actionDescription: `Find the document from [${databaseUniqueIdentifier}] with identifier [${documentUniqueIdentifier}] and property [${propertyUniqueIdentifier}]`,
        executerMetadata: metadata,
    };
};
