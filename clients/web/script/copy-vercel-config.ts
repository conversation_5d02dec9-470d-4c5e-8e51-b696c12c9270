/**
 * <AUTHOR>
 * @namespace Script
 * @description Copy Vercel Config
 */

import * as Path from "node:path";
import * as Fs from "node:fs";

(() => {

    const vercelConfigPath = Path.join(__dirname, "..", "config", "vercel.json");
    const vercelConfigContent = Fs.readFileSync(vercelConfigPath, "utf-8");

    const appConfigPath = Path.join(__dirname, "..", "app", "vercel.json");
    Fs.writeFileSync(appConfigPath, vercelConfigContent);
})();
