/**
 * <AUTHOR>
 * @package UI
 * @namespace Dropdown
 * @description Util
 */

import { UIDropdownItemCategory, UIDropdownItemData } from "./types";

export const findUIDropdownItemByItemKey = (
    categories: UIDropdownItemCategory[],
    itemKey: string,
): UIDropdownItemData | null => {

    for (const category of categories) {

        for (const item of category.items) {

            if (item.itemKey === itemKey) {
                return item;
            }
        }
    }

    return null;
};

export const concludeSelectedItemKeys = (
    categories: UIDropdownItemCategory[],
): Set<string> => {

    const selectedKeys: Set<string> = new Set();

    for (const category of categories) {

        for (const item of category.items) {

            if (item.isSelected) {
                selectedKeys.add(item.itemKey);
            }
        }
    }

    return selectedKeys;
};
