/**
 * <AUTHOR>
 * @namespace Database_Components_Schema_CardOptions
 * @description Reference
 */

import { useDatabaseFormat } from "@/main/database/internationalization/hook";
import { DATABASE_PROFILE } from "@/main/database/internationalization/profile";
import { FormattedText, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import { UIButton, UICheckbox, UIDropdown, createUIButton } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchema, ImbricateDatabaseSchemaProperty, ImbricateDatabaseSchemaPropertyOptionsReference, ImbricateDatabaseSchemaPropertyOptionsReferenceDatabase } from "@imbricate/core";
import React, { FC } from "react";
import { FaPlus } from "react-icons/fa";
import { MdDelete } from "react-icons/md";
import { TbWorld } from "react-icons/tb";
import { rootLogger } from "../../../../log/logger";
import { ImbricateDatabasesObject, S_UseDatabasesLoading, UseDatabasesResponse, UseDatabasesResponseSymbol, useDatabases } from "../../../hooks/use-databases";

const logger = rootLogger.fork({
    scopes: [
        "Database",
        "Components",
        "Schema",
        "CardOptions",
        "Reference",
    ],
});

export type DatabaseSchemaPropertyCardOptionsReferenceProps = {

    readonly property: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE.REFERENCE>;
    readonly schema: ImbricateDatabaseSchema;
    readonly setSchema: (schema: ImbricateDatabaseSchema) => void;
};

export const DatabaseSchemaPropertyCardOptionsReference: FC<DatabaseSchemaPropertyCardOptionsReferenceProps> = (
    props: DatabaseSchemaPropertyCardOptionsReferenceProps,
) => {

    const databaseFormat = useDatabaseFormat();

    const databases: UseDatabasesResponse | UseDatabasesResponseSymbol =
        useDatabases();

    if (databases === S_UseDatabasesLoading) {

        return (<LoadingWrapper
            debugDescription="Database Schema Property Card Options Reference"
            color="default"
        />);
    }

    const currentSchema = props.schema.properties.find((
        each: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>,
    ) => {
        return each.propertyIdentifier === props.property.propertyIdentifier;
    });

    if (!currentSchema
        || typeof databases === "symbol"
    ) {
        return null;
    }

    const options: ImbricateDatabaseSchemaPropertyOptionsReference =
        currentSchema.propertyOptions as ImbricateDatabaseSchemaPropertyOptionsReference;

    const allowMultiple: boolean = options.allowMultiple ?? false;
    const databaseOptions: ImbricateDatabaseSchemaPropertyOptionsReferenceDatabase[] =
        options.databases ?? [];

    return (<div
        className="w-full flex flex-col gap-2"
    >
        <div
            className="flex justify-center items-center"
        >
            <div
                className="flex-1"
            >
                <UICheckbox
                    aria-label={databaseFormat.get(DATABASE_PROFILE.ALLOW_MULTIPLE)}
                    className="flex-1"
                    defaultSelected={allowMultiple}
                    onValueChange={(newSelected: boolean) => {

                        props.setSchema({
                            ...props.schema,
                            properties: props.schema.properties.map((each) => {
                                if (each.propertyIdentifier === props.property.propertyIdentifier) {
                                    return {
                                        ...each,
                                        propertyOptions: {
                                            ...options,
                                            allowMultiple: newSelected,
                                        },
                                    };
                                }
                                return each;
                            }),
                        });
                    }}
                >
                    <FormattedText
                        usedFormats={[
                            databaseFormat,
                        ]}
                    >
                        {databaseFormat.get(DATABASE_PROFILE.ALLOW_MULTIPLE)}
                    </FormattedText>
                </UICheckbox>
            </div>
            <UIDropdown
                ariaLabel="select-database"
                backdrop="blur"
                variant="flat"
                trigger={createUIButton({
                    variant: "flat",
                    color: "primary",
                    radius: "sm",
                    children: <React.Fragment>
                        <FaPlus />
                        <FormattedText
                            usedFormats={[
                                databaseFormat,
                            ]}
                        >
                            {databaseFormat.get(DATABASE_PROFILE.SELECT_DATABASE)}
                        </FormattedText>
                    </React.Fragment>,
                })}
                categories={[
                    {
                        categoryKey: "select-database",
                        items: databases.databases.map((
                            database: ImbricateDatabasesObject,
                        ) => {

                            return {
                                itemKey: database.database.uniqueIdentifier,
                                textValue: database.database.databaseName,
                                content: database.database.databaseName,
                                description: (<div
                                    className="flex items-center gap-1"
                                >
                                    <TbWorld />{database.originUniqueIdentifier}
                                </div>),
                                onPress: () => {

                                    logger.debug("Add Database", database.database.uniqueIdentifier);

                                    const newOptions: ImbricateDatabaseSchemaPropertyOptionsReferenceDatabase[] = [
                                        ...databaseOptions,
                                        {
                                            originUniqueIdentifier: database.originUniqueIdentifier,
                                            databaseUniqueIdentifier: database.database.uniqueIdentifier,
                                        },
                                    ];

                                    props.setSchema({
                                        ...props.schema,
                                        properties: props.schema.properties.map((each) => {
                                            if (each.propertyIdentifier === props.property.propertyIdentifier) {
                                                return {
                                                    ...each,
                                                    propertyOptions: {
                                                        ...options,
                                                        databases: newOptions,
                                                    },
                                                };
                                            }
                                            return each;
                                        }),
                                    });
                                },
                            };
                        }),
                    },
                ]}
            />
        </div>
        {databaseOptions.length > 0 && <div
            className="flex flex-col gap-1"
        >
            {databaseOptions.map((databaseItem: ImbricateDatabaseSchemaPropertyOptionsReferenceDatabase) => {

                const targetDatabase = databases.databases.find((
                    each: ImbricateDatabasesObject,
                ) => each.database.uniqueIdentifier === databaseItem.databaseUniqueIdentifier);

                if (!targetDatabase) {
                    return null;
                }

                return (<div
                    key={databaseItem.databaseUniqueIdentifier}
                    className="flex items-center"
                >
                    <div
                        className="flex-1"
                    >
                        {targetDatabase.database.databaseName}
                        <div
                            className="flex items-center gap-1 text-tiny text-gray-600"
                        >
                            <TbWorld />{targetDatabase.originUniqueIdentifier}
                        </div>
                    </div>
                    <UIButton
                        variant="flat"
                        color="danger"
                        isIconOnly
                        radius="sm"
                        size="lg"
                        onPress={() => {

                            const newOptions: ImbricateDatabaseSchemaPropertyOptionsReferenceDatabase[] =
                                databaseOptions.filter((each) => each.databaseUniqueIdentifier !== databaseItem.databaseUniqueIdentifier);

                            props.setSchema({
                                ...props.schema,
                                properties: props.schema.properties.map((each) => {
                                    if (each.propertyIdentifier === props.property.propertyIdentifier) {
                                        return {
                                            ...each,
                                            propertyOptions: {
                                                ...options,
                                                databases: newOptions,
                                            },
                                        };
                                    }
                                    return each;
                                }),
                            });
                        }}
                    >
                        <MdDelete
                            className="text-large"
                        />
                    </UIButton>
                </div>);
            })}
        </div>}
    </div>);
};
