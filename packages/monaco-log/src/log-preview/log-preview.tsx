/**
 * <AUTHOR>
 * @package Monaco Log
 * @namespace LogPreview
 * @description Log Preview
 */

import { ImbriScriptContainerLog } from "@imbricate-hummingbird/script-core";
import { useDarkMode } from "@imbricate-hummingbird/theme-core";
import * as monaco from "monaco-editor";
import React, { FC, useEffect, useMemo } from "react";
import { createMonacoForLogPreview } from "./monaco/create-monaco";
import { getLogLineNumberMethod } from "./monaco/get-log-line-numbers";
import { stringifyLogForMonaco } from "./monaco/stringify-log-for-monaco";
import { useMonacoLogPreviewFormat } from "./internationalization/hook";

export type MonacoLogPreviewProps = {

    readonly logs: ImbriScriptContainerLog[];

    readonly progressing: boolean;
};

// LAZY LOAD ONLY
const MonacoLogPreview: FC<MonacoLogPreviewProps> = (
    props: MonacoLogPreviewProps,
) => {

    const monacoLogPreviewFormat = useMonacoLogPreviewFormat();

    const darkMode = useDarkMode();

    const memorizedContainerId = useMemo(() => {

        const randomId = Math.random().toString(36).substring(2, 15);
        return `log-view-monaco-${randomId}`;
    }, []);

    const editorRef = React.useRef<monaco.editor.IStandaloneCodeEditor | null>(null);
    const onboardedRef = React.useRef(false);

    useEffect(() => {

        if (editorRef.current) {

            const editor = editorRef.current;
            if (!editor) {
                return;
            }

            editor.updateOptions({
                theme: darkMode ? "vs-dark" : "vs",
            });
        }
    }, [darkMode]);

    useEffect(() => {

        if (editorRef.current) {

            const editor = editorRef.current;
            if (!editor) {
                return;
            }

            const logString = stringifyLogForMonaco(props.logs, props.progressing);
            editor.setValue(logString);

            const lineNumberMethod = getLogLineNumberMethod(props.logs);
            editor.updateOptions({
                lineNumbers: lineNumberMethod,
            });
        }
    }, [props.logs.length, props.progressing]);

    useEffect(() => {

        const container = document.getElementById(memorizedContainerId);
        if (!container) {
            return;
        }

        onboardedRef.current = true;

        const editorResponse = createMonacoForLogPreview(
            container,
            props.logs,
            monacoLogPreviewFormat,
            props.progressing,
            darkMode,
        );

        const editor = editorResponse.editor;

        editorRef.current = editor;

        return () => {

            editorResponse.disposables.forEach((
                disposable: monaco.IDisposable,
            ) => disposable.dispose());

            editor.dispose();

            editorRef.current = null;
            onboardedRef.current = false;
        };
    }, []);

    return (<React.Fragment>
        <div
            id={memorizedContainerId}
            className="flex-1"
        />
    </React.Fragment>);
};
export default MonacoLogPreview;
