/**
 * <AUTHOR>
 * @namespace Common_Ongoing
 * @description Ongoing Manager
 */

import { RecentActions } from "@imbricate-hummingbird/interceptor-core";
import { WORKER_WEB_WORKER } from "@imbricate-hummingbird/web-worker-core";
import { rootLogger } from "../../log/logger";

const logger = rootLogger.fork({
    scopes: [
        "Common",
        "Ongoing",
        "OngoingManager",
    ],
});

export enum OngoingSource {

    DEDUPLICATE = "DEDUPLICATE",
    EXECUTION = "EXECUTION",
}

export type OngoingResponse<T> = {

    result: T;
    source: OngoingSource;
};

export class OngoingManager {

    private static _instance: OngoingManager;

    public static getInstance(): OngoingManager {

        if (!this._instance) {
            this._instance = new OngoingManager();
        }
        return this._instance;
    }

    private readonly _ongoingOperations: Map<string, PromiseLike<any>>;

    private constructor() {

        this._ongoingOperations = new Map();
    }

    public async execute<T>(
        identifier: string,
        execute: () => PromiseLike<T>,
        _actionIdentifier: string,
        recordIdentifier: string,
    ): Promise<T> {

        const ongoing: PromiseLike<any> | undefined = this._ongoingOperations.get(identifier);

        if (ongoing) {

            const recentActions: RecentActions<WORKER_WEB_WORKER> =
                RecentActions.getInstance<WORKER_WEB_WORKER>();

            recentActions.setMerged(recordIdentifier, true);

            const result = await ongoing;

            logger.debug(`(DEDUPLICATED) ${identifier}`);
            return result;
        }

        const promise: PromiseLike<T> = execute();
        this._ongoingOperations.set(identifier, promise);

        const result: T = await promise;
        this._ongoingOperations.delete(identifier);

        logger.debug(`(EXECUTED) ${identifier}`);
        return result;
    }
}
