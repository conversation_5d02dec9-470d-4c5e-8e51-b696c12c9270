/**
 * <AUTHOR>
 * @namespace Navigation_Hooks
 * @description Use Routes
 */

import { RECENT_TYPE, addRecentItem } from "@imbricate-hummingbird/configuration";
import { NavigateOptions, useNavigate } from "react-router-dom";

export const useReloadApplication = (): (
    options?: NavigateOptions,
) => void => {

    const navigateRoot = useNavigateRoot();

    return (
        options?: NavigateOptions,
    ) => {
        navigateRoot({
            replace: true,
            ...options,
        });
        self.location.reload();
    };
};

export const useNavigateRoot = (): (
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        options?: NavigateOptions,
    ) => navigate("/", options);
};

export const useNavigateConfigView = (): (
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        options?: NavigateOptions,
    ) => navigate("/config", options);
};

export const useNavigateConfigConfigurationExportView = (): (
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        options?: NavigateOptions,
    ) => navigate("/config/import-export/export", options);
};

export const useNavigateConfigConfigurationImportView = (): (
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        options?: NavigateOptions,
    ) => navigate("/config/import-export/import", options);
};

export const useNavigateSearchView = (): (
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        options?: NavigateOptions,
    ) => navigate("/search", options);
};

export const useNavigateOriginNewView = (): (
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        options?: NavigateOptions,
    ) => navigate("/origin-new", options);
};

export const useNavigateOriginView = (): (
    originUniqueIdentifier: string,
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        originUniqueIdentifier: string,
        options?: NavigateOptions,
    ) => navigate(`/origin/${originUniqueIdentifier}`, options);
};

export const useNavigateOriginsView = (): (
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        options?: NavigateOptions,
    ) => navigate("/origins", options);
};

export const useNavigateLensNewView = (): (
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        options?: NavigateOptions,
    ) => navigate("/lens-new", options);
};

export const useNavigateLensView = (): (
    lensUniqueIdentifier: string,
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        lensUniqueIdentifier: string,
        options?: NavigateOptions,
    ) => {
        addRecentItem(RECENT_TYPE.LENS, {
            lensIdentifier: lensUniqueIdentifier,
        });
        navigate(`/lens/${lensUniqueIdentifier}`, options);
    };
};

export const useNavigateLensEditView = (): (
    lensUniqueIdentifier: string,
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        lensUniqueIdentifier: string,
        options?: NavigateOptions,
    ) => {
        navigate(`/lens/${lensUniqueIdentifier}/edit`, options);
    };
};

export const useNavigateDatabasesView = (): (
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        options?: NavigateOptions,
    ) => {
        navigate("/databases", options);
    };
};

export const useNavigateDatabaseDocumentsView = (): (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        originUniqueIdentifier: string,
        databaseUniqueIdentifier: string,
        options?: NavigateOptions,
    ) => {

        addRecentItem(RECENT_TYPE.DATABASE, {
            originUniqueIdentifier,
            databaseUniqueIdentifier,
        });
        navigate(`/database/${databaseUniqueIdentifier}/documents`, options);
    };
};

export const useNavigateDocumentView = (): (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        originUniqueIdentifier: string,
        databaseUniqueIdentifier: string,
        documentUniqueIdentifier: string,
        options?: NavigateOptions,
    ) => {

        addRecentItem(RECENT_TYPE.DOCUMENT, {
            originUniqueIdentifier,
            databaseUniqueIdentifier,
            documentUniqueIdentifier,
        });
        navigate(`/database/${databaseUniqueIdentifier}/document/${documentUniqueIdentifier}`, options);
    };
};

export const useNavigateDocumentEditHistoryView = (): (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        originUniqueIdentifier: string,
        databaseUniqueIdentifier: string,
        documentUniqueIdentifier: string,
        options?: NavigateOptions,
    ) => {

        addRecentItem(RECENT_TYPE.DOCUMENT, {
            originUniqueIdentifier,
            databaseUniqueIdentifier,
            documentUniqueIdentifier,
        });
        navigate(`/database/${databaseUniqueIdentifier}/document/${documentUniqueIdentifier}/edit-history`, options);
    };
};

export const useNavigateDatabaseSchemaView = (): (
    databaseUniqueIdentifier: string,
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        databaseUniqueIdentifier: string,
        options?: NavigateOptions,
    ) => navigate(`/database/${databaseUniqueIdentifier}/schema`, options);
};

export const useNavigateViewView = (): (
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    propertyUniqueIdentifier: string,
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        databaseUniqueIdentifier: string,
        documentUniqueIdentifier: string,
        propertyUniqueIdentifier: string,
        options?: NavigateOptions,
    ) => navigate(`/view/${databaseUniqueIdentifier}/document/${documentUniqueIdentifier}/property/${propertyUniqueIdentifier}`, options);
};

export const useNavigateEditView = (): (
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    propertyUniqueIdentifier: string,
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        databaseUniqueIdentifier: string,
        documentUniqueIdentifier: string,
        propertyUniqueIdentifier: string,
        options?: NavigateOptions,
    ) => navigate(`/edit/${databaseUniqueIdentifier}/document/${documentUniqueIdentifier}/property/${propertyUniqueIdentifier}`, options);
};

export const useNavigateStaticsView = (): (
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        options?: NavigateOptions,
    ) => navigate("/statics", options);
};
