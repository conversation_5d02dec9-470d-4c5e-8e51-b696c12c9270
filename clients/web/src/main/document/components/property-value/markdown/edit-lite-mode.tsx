/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyValue_Markdown
 * @description Edit Lite Mode
 */

import { openEditWindow, openViewWindow } from "@imbricate-hummingbird/navigation-core";
import { DRAWER_TYPE, useDrawerAction } from "@imbricate-hummingbird/react-store";
import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIButton, UIButtonGroup } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { FaEye } from "react-icons/fa6";
import { MdEdit } from "react-icons/md";
import { TbLayoutSidebarRightExpandFilled } from "react-icons/tb";
import { useDocumentFormat } from "../../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../../internationalization/profile";

export type DocumentMarkdownValueEditLiteModeProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.MARKDOWN>;
    readonly updateProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.MARKDOWN>) => void;
};

export const DocumentMarkdownValueEditLiteMode: FC<DocumentMarkdownValueEditLiteModeProps> = (
    props: DocumentMarkdownValueEditLiteModeProps,
) => {

    const documentFormat = useDocumentFormat();

    const drawerActions = useDrawerAction();

    return (<div
        className="flex flex-col gap-1 w-full"
    >
        <UIButtonGroup>
            <UIButton
                isFullWidth
                startContent={<MdEdit />}
                color="secondary"
                variant="flat"
                onPress={() => {

                    if (
                        !props.originUniqueIdentifier
                        || !props.databaseUniqueIdentifier
                        || !props.documentUniqueIdentifier) {
                        throw new Error("[Imbricate] Database or document unique identifier not found");
                    }

                    openEditWindow(
                        props.originUniqueIdentifier,
                        props.databaseUniqueIdentifier,
                        props.documentUniqueIdentifier,
                        props.propertyKey,
                    );
                }}
            >
                {documentFormat.get(DOCUMENT_PROFILE.EDIT_MARKDOWN_DOCUMENT)}
            </UIButton>
            <UIButton
                isFullWidth
                startContent={<FaEye />}
                onPress={() => {

                    if (
                        !props.originUniqueIdentifier
                        || !props.databaseUniqueIdentifier
                        || !props.documentUniqueIdentifier) {
                        throw new Error("[Imbricate] Database or document unique identifier not found");
                    }

                    openViewWindow(
                        props.originUniqueIdentifier,
                        props.databaseUniqueIdentifier,
                        props.documentUniqueIdentifier,
                        props.propertyKey,
                    );
                }}
            >
                {documentFormat.get(DOCUMENT_PROFILE.VIEW_MARKDOWN_DOCUMENT)}
            </UIButton>
            <UIButton
                isFullWidth
                isIconOnly
                startContent={<TbLayoutSidebarRightExpandFilled
                    className="text-large"
                />}
                onPress={() => {

                    drawerActions.openDrawer({
                        type: DRAWER_TYPE.TEXT_RAW_PEEK,
                        title: "Markdown Document Preview",

                        payload: {
                            originUniqueIdentifier: props.originUniqueIdentifier,
                            textUniqueIdentifier: props.property.value,
                        },
                    });
                }}
            />
        </UIButtonGroup>
    </div>);
};
