/**
 * <AUTHOR>
 * @namespace Database_Components_Table
 * @description Database Pagination
 */

import { UIPagination } from "@imbricate-hummingbird/ui";
import { FC } from "react";

export type DatabaseDatabasePaginationProps = {

    readonly total: number;
    readonly current: number;
    readonly onChange: (page: number) => void;
};

export const DatabaseDatabasePagination: FC<DatabaseDatabasePaginationProps> = (
    props: DatabaseDatabasePaginationProps,
) => {

    return (<div
        className="pointer-events-auto"
    >
        <UIPagination
            className="p-2 m-0"
            itemClassName="cursor-pointer bg-default-50"
            current={props.current}
            total={props.total}
            color="primary"
            variant="bordered"
            onChange={(
                newPage: number,
            ) => {
                props.onChange(newPage);
            }}
        />
    </div>);
};
