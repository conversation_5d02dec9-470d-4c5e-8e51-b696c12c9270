/**
 * <AUTHOR>
 * @namespace Data_Internationalization
 * @description Profile
 */

export enum DATA_PROFILE {

    CONFIRM_PROCEED_WITH_ERROR = "CONFIRM_PROCEED_WITH_ERROR",
    ERROR_OCCURRED = "ERROR_OCCURRED",
    ERROR_OCCURRED_WHEN_PROCESSING_DATA = "ERROR_OCCURRED_WHEN_PROCESSING_DATA",
    PROCEED_WITH_ERROR = "PROCEED_WITH_ERROR",
    PROCEED_WITH_ERROR_DESCRIPTION = "PROCEED_WITH_ERROR_DESCRIPTION",
    REASON = "REASON",
    RETRY = "RETRY",
    RETRY_AGAIN = "RETRY_AGAIN",
}
