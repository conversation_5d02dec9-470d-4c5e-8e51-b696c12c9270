/**
 * <AUTHOR>
 * @namespace Internationalization_Internationalization_Locale
 * @description Fr-FR
 */

import { IETF_LOCALE } from "@sudoo/locale";

// spell-checker: disable
export const frFRInternationalizationProfile: Record<IETF_LOCALE, string> = {

    [IETF_LOCALE.CHINESE_SIMPLIFIED]: "Chinois simplifié",
    [IETF_LOCALE.CHINESE_TRADITIONAL]: "Chinois traditionnel",
    [IETF_LOCALE.ENGLISH_CANADA]: "<PERSON><PERSON><PERSON> (Canadien)",
    [IETF_LOCALE.ENGLISH_UNITED_KINGDOM]: "<PERSON><PERSON><PERSON> (Royaume-Uni)",
    [IETF_LOCALE.ENGLISH_UNITED_STATES]: "<PERSON><PERSON><PERSON> (États-Unis)",
    [IETF_LOCALE.FRENCH_CANADA]: "Français (Canada)",
    [IETF_LOCALE.FRENCH_FRANCE]: "Français (France)",
    [IETF_LOCALE.JAPANESE_JAPAN]: "<PERSON>apon<PERSON>",
    [IETF_LOCALE.KOREAN_KOREA]: "Co<PERSON><PERSON>",
    [IETF_LOCALE.MAORI_NEW_ZEALAND]: "Maori",
    [IETF_LOCALE.RUSSIAN_RUSSIA]: "Russe",
    [IETF_LOCALE.SPANISH_MEXICO]: "Espagnol (Mexique)",
    [IETF_LOCALE.SPANISH_SPAIN]: "Espagnol (Espagne)",
};
// spell-checker: enable
