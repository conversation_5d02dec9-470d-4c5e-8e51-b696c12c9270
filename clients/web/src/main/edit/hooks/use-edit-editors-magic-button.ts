/**
 * <AUTHOR>
 * @namespace Main_Edit_Hooks_UseEditEditorsMagicButton
 * @description Use Edit Editors Magic Button
 */

import { useMagicButtonInitialItems } from "@/common/components/magic-button/hooks/use-initial-items";
import { UseMagicButtonResult, useMagicButton } from "@/common/components/magic-button/hooks/use-magic-button";
import { isDefaultEmptyFormat } from "@imbricate-hummingbird/internationalization";
import { openViewWindow } from "@imbricate-hummingbird/navigation-core";
import { FaEye, FaTimes } from "react-icons/fa";
import { useEditFormat } from "../internationalization/hook";
import { EDIT_PROFILE } from "../internationalization/profile";

export type UseEditEditorsMagicButtonProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;
    readonly propertyUniqueIdentifier: string;

    readonly isLoading: boolean;

    readonly valueChanged: boolean;
    readonly saveProperty: () => Promise<void>;
};

export const useEditEditorsMagicButton = (
    props: UseEditEditorsMagicButtonProps,
): UseMagicButtonResult => {

    const editFormat = useEditFormat();

    const magicButtonResult = useMagicButton([
        {
            categoryKey: "view",
            categoryTitle: editFormat.get(EDIT_PROFILE.VIEW),
            categoryOrder: 1,
        },
        {
            categoryKey: "markdown",
            categoryTitle: editFormat.get(EDIT_PROFILE.MARKDOWN),
            categoryOrder: 2,
        },
        {
            categoryKey: "imbriscript",
            categoryTitle: editFormat.get(EDIT_PROFILE.IMBRISCRIPT),
            categoryOrder: 3,
        },
        {
            categoryKey: "editor",
            categoryTitle: editFormat.get(EDIT_PROFILE.EDITOR),
            categoryOrder: 5,
        },
    ]);

    useMagicButtonInitialItems(
        magicButtonResult,
        !isDefaultEmptyFormat(editFormat),
        [
            {
                categoryKey: "view",
                itemKey: "view-in-preview",
                icon: FaEye,
                title: editFormat.get(EDIT_PROFILE.VIEW_IN_PREVIEW),
                description: editFormat.get(EDIT_PROFILE.VIEW_IN_PREVIEW_DESCRIPTION),
                externalIndicator: true,
                onPress: () => {
                    openViewWindow(
                        props.originUniqueIdentifier,
                        props.databaseUniqueIdentifier,
                        props.documentUniqueIdentifier,
                        props.propertyUniqueIdentifier,
                    );
                },
            },
            {
                categoryKey: "editor",
                itemKey: "close-editor",
                order: 50,
                icon: FaTimes,
                title: editFormat.get(EDIT_PROFILE.CLOSE_EDITOR),
                description: editFormat.get(EDIT_PROFILE.CLOSE_EDITOR_DESCRIPTION),
                onPress: async () => {

                    if (props.valueChanged) {
                        await props.saveProperty();
                    }
                    self.close();
                },
                color: "danger",
            },
        ],
    );

    return magicButtonResult;
};
