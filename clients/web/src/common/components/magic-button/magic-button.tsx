/**
 * <AUTHOR>
 * @namespace Common_Components_MagicButton
 * @description Magic Button
 */

import { useCommonFormat } from "@/common/internationalization/hook";
import { commonLogger } from "@/common/util/logger";
import { HUMMINGBIRD_DISMISSED_GUIDANCE_ITEM, useGuidanceItem } from "@imbricate-hummingbird/configuration";
import { UIButton, UIButtonGroup, UIButtonVariant, UIColor, UIDropdown, createUIButton } from "@imbricate-hummingbird/ui";
import { FC, useMemo } from "react";
import { TfiMoreAlt } from "react-icons/tfi";
import { useMagicButtonControlledButton } from "./hooks/use-controlled-button";
import { UseMagicButtonResult } from "./hooks/use-magic-button";
import { getMagicButtonItems } from "./magic-button-items";
import { concludeMagicButtonCategories } from "./util/conclude";

const logger = commonLogger.fork({
    scopes: [
        "MagicButton",
    ],
});

export type MagicButtonProps = {

    readonly magicButtonResult: UseMagicButtonResult;

    readonly variant?: UIButtonVariant;
    readonly color?: UIColor;
};

export const MagicButton: FC<MagicButtonProps> = (
    props: MagicButtonProps,
) => {

    const commonFormat = useCommonFormat();

    const [opened, setOpened] = useMagicButtonControlledButton();

    const dismissedMagicButtonShortcut = useGuidanceItem(
        HUMMINGBIRD_DISMISSED_GUIDANCE_ITEM.MAGIC_BUTTON_SHORTCUT,
    );

    const concluded = useMemo(() => {

        return concludeMagicButtonCategories(
            props.magicButtonResult.items,
            props.magicButtonResult.categories,
        );
    }, [props.magicButtonResult.items.map((item) => {
        return item.itemKey;
    }).join(",")]);

    return (<div>
        <UIButtonGroup>
            {props.magicButtonResult.lastExecutedItem && <UIButton
                color={props.magicButtonResult.lastExecutedItem.color}
                variant="flat"
                startContent={<props.magicButtonResult.lastExecutedItem.icon />}
                onPress={() => {

                    if (!props.magicButtonResult.lastExecutedItem) {

                        logger.error("Unable to find last executed item");
                        return;
                    }

                    props.magicButtonResult.lastExecutedItem.onPress();
                    props.magicButtonResult.onExecuteItem(props.magicButtonResult.lastExecutedItem.itemKey);
                }}
                aria-description={props.magicButtonResult.lastExecutedItem.description}
            >
                {props.magicButtonResult.lastExecutedItem.title}
            </UIButton>}
            <UIDropdown
                baseClassName="before:bg-default-200"
                contentClassName="py-1 px-1 border border-default-200 bg-gradient-to-br from-white to-default-250 dark:from-default-300 dark:to-black min-w-xs"
                isOpen={opened}
                onOpenChange={setOpened}
                trigger={createUIButton({
                    isIconOnly: true,
                    variant: props.variant ?? "solid",
                    color: props.color ?? "success",
                    children: (<TfiMoreAlt
                        className="text-large"
                    />),
                })}
                categories={getMagicButtonItems(
                    props.magicButtonResult,
                    concluded,
                    dismissedMagicButtonShortcut,
                    commonFormat,
                )}
            />
        </UIButtonGroup>
    </div>);
};
