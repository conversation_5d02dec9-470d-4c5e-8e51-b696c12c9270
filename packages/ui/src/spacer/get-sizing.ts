/**
 * <AUTHOR>
 * @package UI
 * @namespace Spacer
 * @description Get Sizing
 */

import { SpacerProps } from "@heroui/spacer";
import { UISize } from "../common/types";

export const getUISpacerSizing = (
    size: UISize,
): SpacerProps["x"] | undefined => {

    switch (size) {
        case "sm":
            return 4;
        case "md":
            return 8;
        case "lg":
            return 16;
    }

    return undefined;
};
