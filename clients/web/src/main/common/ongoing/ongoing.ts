/**
 * <AUTHOR>
 * @namespace Common_Ongoing
 * @description Ongoing
 */

import { rootLogger } from "../../log/logger";
import { OngoingManager } from "./ongoing-manager";

const logger = rootLogger.fork({
    scopes: [
        "Common",
        "Ongoing",
        "executeDeduplicate",
    ],
});

export const executeDeduplicate = async <T>(
    identifier: string,
    execute: () => PromiseLike<T>,
    actionIdentifier: string,
    recordIdentifier: string,
): Promise<T> => {

    const ongoingManager: OngoingManager = OngoingManager.getInstance();

    try {

        return ongoingManager.execute<T>(
            identifier,
            execute,
            actionIdentifier,
            recordIdentifier,
        );
    } catch (err) {

        logger.error(err);
        throw err;
    }
};
