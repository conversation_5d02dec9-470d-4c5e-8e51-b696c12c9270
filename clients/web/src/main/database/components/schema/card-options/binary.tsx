/**
 * <AUTHOR>
 * @namespace Database_Components_Schema_Card_Options
 * @description Binary
 */

import { useDatabaseFormat } from "@/main/database/internationalization/hook";
import { DATABASE_PROFILE } from "@/main/database/internationalization/profile";
import { FormattedText } from "@imbricate-hummingbird/react-components";
import { UICheckbox } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchema, ImbricateDatabaseSchemaProperty, ImbricateDatabaseSchemaPropertyOptionsBinary } from "@imbricate/core";
import { FC } from "react";

export type DatabaseSchemaPropertyCardOptionsBinaryProps = {

    readonly property: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE.BINARY>;
    readonly schema: ImbricateDatabaseSchema;
    readonly setSchema: (schema: ImbricateDatabaseSchema) => void;
};

export const DatabaseSchemaPropertyCardOptionsBinary: FC<DatabaseSchemaPropertyCardOptionsBinaryProps> = (
    props: DatabaseSchemaPropertyCardOptionsBinaryProps,
) => {

    const databaseFormat = useDatabaseFormat();

    const currentSchema = props.schema.properties.find((each) => {
        return each.propertyIdentifier === props.property.propertyIdentifier;
    });

    if (!currentSchema) {
        return null;
    }

    const options: ImbricateDatabaseSchemaPropertyOptionsBinary =
        currentSchema.propertyOptions as ImbricateDatabaseSchemaPropertyOptionsBinary;

    const allowMultiple: boolean = options.allowMultiple ?? false;

    return (<div
        className="w-full flex flex-col gap-2"
    >
        <div
            className="flex justify-center items-center"
        >
            <div
                className="flex-1"
            >
                <UICheckbox
                    aria-label={databaseFormat.get(DATABASE_PROFILE.ALLOW_MULTIPLE)}
                    className="flex-1"
                    defaultSelected={allowMultiple}
                    onValueChange={(newSelected: boolean) => {

                        props.setSchema({
                            ...props.schema,
                            properties: props.schema.properties.map((each) => {
                                if (each.propertyIdentifier === props.property.propertyIdentifier) {
                                    return {
                                        ...each,
                                        propertyOptions: {
                                            ...options,
                                            allowMultiple: newSelected,
                                        },
                                    };
                                }
                                return each;
                            }),
                        });
                    }}
                >
                    <FormattedText
                        usedFormats={[
                            databaseFormat,
                        ]}
                    >
                        {databaseFormat.get(DATABASE_PROFILE.ALLOW_MULTIPLE)}
                    </FormattedText>
                </UICheckbox>
            </div>
        </div>
    </div>);
};
