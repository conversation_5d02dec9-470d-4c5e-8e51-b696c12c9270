/**
 * <AUTHOR>
 * @namespace Edit
 * @description Edit Application
 */

import { useDarkMode } from "@imbricate-hummingbird/theme-core";
import { FC } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { MainWrapper } from "../common/components/main-wrapper";
import { useWideOrigin } from "../origin/hooks/use-wide-origin";
import { EditView } from "./edit-view";

// LAZY LOAD ONLY
const EditApplication: FC = () => {

    const darkMode = useDarkMode();

    const params = useParams();
    const [search] = useSearchParams();

    const databaseUniqueIdentifier: string = params["database-unique-identifier"] as string;
    const documentUniqueIdentifier: string = params["document-unique-identifier"] as string;
    const propertyUniqueIdentifier: string = params["property-unique-identifier"] as string;

    const startLineNumber: string | null = search.get("line");

    const wideOrigin = useWideOrigin(databaseUniqueIdentifier);

    if (!wideOrigin) {
        return null;
    }

    return (<MainWrapper
        className="flex flex-col overflow-hidden"
    >
        <EditView
            darkMode={darkMode}
            originUniqueIdentifier={wideOrigin.origin.uniqueIdentifier}
            databaseUniqueIdentifier={databaseUniqueIdentifier}
            documentUniqueIdentifier={documentUniqueIdentifier}
            propertyUniqueIdentifier={propertyUniqueIdentifier}
            startLineNumber={startLineNumber ? Number(startLineNumber) : undefined}
        />
    </MainWrapper>);
};

export default EditApplication;
