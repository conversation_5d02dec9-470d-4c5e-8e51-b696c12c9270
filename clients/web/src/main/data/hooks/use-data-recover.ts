/**
 * <AUTHOR>
 * @namespace Data_Hooks
 * @description Use Data Recover
 */

import { ActionCentral, ActionItem } from "@imbricate-hummingbird/interceptor-core";
import { useCallback, useEffect } from "react";
import { useForceUpdate } from "../../common/hooks/use-version";

export type UseDataRecoverResponse = {

    readonly recoverableItems: ActionItem<any>[];
};

export const useDataRecover = (): UseDataRecoverResponse => {

    const forceUpdate = useForceUpdate();

    const actionCentral = ActionCentral.getInstance();

    const updateFunction = useCallback(() => {
        forceUpdate();
    }, [forceUpdate]);

    useEffect(() => {

        actionCentral.addListener(updateFunction);
        return () => {
            actionCentral.removeListener(updateFunction);
        };
    }, []);

    return {
        recoverableItems: actionCentral.getRecoverableItems(),
    };
};
