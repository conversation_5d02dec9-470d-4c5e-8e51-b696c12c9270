/**
 * <AUTHOR>
 * @namespace Database_Hooks
 * @description Use Displayed Properties
 */

import { HUMMINGBIRD_MANAGED_CONFIG_ITEM, HummingbirdManagedConfigDatabaseViewDisplayingProperties, ManagedConfigController } from "@imbricate-hummingbird/configuration";
import { ReactDependency } from "@imbricate-hummingbird/react-common";
import { useCallback, useEffect, useState } from "react";

export type UseDisplayedPropertiesResult = {

    readonly displayedProperties: string[];
    readonly setDisplayedProperties: (displayedProperties: string[]) => void;
}

export const useDisplayedProperties = (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
    dependencies: ReactDependency[],
): UseDisplayedPropertiesResult => {

    const getInitialDisplayedProperties = useCallback(() => {

        const config: HummingbirdManagedConfigDatabaseViewDisplayingProperties[] =
            ManagedConfigController.getManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.DATABASE_VIEW_DISPLAYING_PROPERTIES);

        const targetConfig: HummingbirdManagedConfigDatabaseViewDisplayingProperties | undefined =
            config.find((config: HummingbirdManagedConfigDatabaseViewDisplayingProperties) => {
                return config.originUniqueIdentifier === originUniqueIdentifier
                    && config.databaseUniqueIdentifier === databaseUniqueIdentifier;
            });

        if (!targetConfig) {
            return [];
        }

        return targetConfig.displayedProperties;
    }, [originUniqueIdentifier, databaseUniqueIdentifier, ...dependencies]);

    const [displayedProperties, setDisplayedProperties] = useState<string[]>(() => {
        return getInitialDisplayedProperties();
    });

    useEffect(() => {

        setDisplayedProperties(getInitialDisplayedProperties());
    }, [originUniqueIdentifier, databaseUniqueIdentifier, ...dependencies]);

    const setDisplayedPropertiesFunction = useCallback((
        displayedProperties: string[],
    ) => {

        setDisplayedProperties(displayedProperties);
        ManagedConfigController.updateManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.DATABASE_VIEW_DISPLAYING_PROPERTIES, (
            current: HummingbirdManagedConfigDatabaseViewDisplayingProperties[],
        ) => {

            let updated: boolean = false;
            const updatedConfigs: HummingbirdManagedConfigDatabaseViewDisplayingProperties[] = current.map((config: HummingbirdManagedConfigDatabaseViewDisplayingProperties) => {

                if (config.originUniqueIdentifier === originUniqueIdentifier
                    && config.databaseUniqueIdentifier === databaseUniqueIdentifier) {

                    updated = true;

                    return {
                        ...config,
                        displayedProperties,
                    };
                }

                return config;
            });

            if (updated) {
                return updatedConfigs;
            }

            return [
                ...current,
                {
                    originUniqueIdentifier,
                    databaseUniqueIdentifier,
                    displayedProperties,
                },
            ];
        });
    }, [originUniqueIdentifier, databaseUniqueIdentifier]);

    return {
        displayedProperties,
        setDisplayedProperties: setDisplayedPropertiesFunction,
    };
};
