/**
 * <AUTHOR>
 * @namespace Log
 * @description Log
 */

import { checkIsDebugOrDevelopmentMode } from "@imbricate-hummingbird/debug-environment";
import { LOG_LEVEL, LOG_LEVEL_TYPE, SudooLog } from "@sudoo/log";

const debugMode = checkIsDebugOrDevelopmentMode();

export const rootLogger: SudooLog = SudooLog.create(
    debugMode
        ? LOG_LEVEL.ALL
        : LOG_LEVEL.INFO,
    {
        capitalizeScope: false,
        tty: false,
        levelType: LOG_LEVEL_TYPE.EMOJI_PREFIX,
    },
);
