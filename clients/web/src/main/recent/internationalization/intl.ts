/**
 * <AUTHOR>
 * @namespace Recent_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooLazyInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { RECENT_PROFILE } from "./profile";

export const recentInternationalization: SudooLazyInternationalization<RECENT_PROFILE> =
    SudooLazyInternationalization.create<RECENT_PROFILE>(
        DEFAULT_LOCALE,
    );

recentInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    import("./locale/zh-CN").then(
        (module) => module.zhCNRecentProfile,
    ),
);

recentInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/en-US").then(
        (module) => module.enUSRecentProfile,
    ),
);
