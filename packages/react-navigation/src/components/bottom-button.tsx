/**
 * <AUTHOR>
 * @namespace Navigation_Components
 * @description Navigation Bottom Button
 */

import { HUMMINGBIRD_PREFERENCE_ITEM, useDynamicPreferenceItem } from "@imbricate-hummingbird/configuration";
import { UIButtonGroup } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { useNavigationFormat } from "../internationalization/hook";
import { NavigationConfigButton } from "./navigation-config-button";
import { NavigationDropdown } from "./navigation-dropdown";

export type NavigationBottomButtonProps = {
};

export const NavigationBottomButton: FC<NavigationBottomButtonProps> = (
    _props: NavigationBottomButtonProps,
) => {

    const navigationFormat = useNavigationFormat();

    const navigationBottomButtonsDisplayMode = useDynamicPreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE,
    );

    return (<UIButtonGroup
        isFullWidth
        className="flex"
        variant="light"
    >
        <NavigationDropdown
            navigationFormat={navigationFormat}
            displayMode={navigationBottomButtonsDisplayMode.preference}
        />
        <NavigationConfigButton
            navigationFormat={navigationFormat}
            displayMode={navigationBottomButtonsDisplayMode.preference}
        />
    </UIButtonGroup>);
};
