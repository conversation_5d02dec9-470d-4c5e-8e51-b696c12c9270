/**
 * <AUTHOR>
 * @namespace Data_Components_DataRecoverWrapper
 * @description Data Recover Wrapper
 */

import { LazyLoadComponent } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import { DataRecoverProps } from "./data-recover";

const DataRecover = LazyLoadComponent(
    () => import("./data-recover"),
    "Data Recover",
);

export const DataRecoverWrapper: FC<DataRecoverProps> = (
    props: DataRecoverProps,
) => {

    if (props.dataRecover.recoverableItems.length === 0) {
        return null;
    }

    return (<React.Suspense>
        <DataRecover
            {...props}
        />
    </React.Suspense>);
};
