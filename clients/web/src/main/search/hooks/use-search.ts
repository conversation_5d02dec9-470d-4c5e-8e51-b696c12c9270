/**
 * <AUTHOR>
 * @namespace Search_Hooks
 * @description Use Search
 */

import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { IMBRICATE_ORIGIN_FEATURE, ImbricateOriginSearchOutcome, checkImbricateOriginFeatureSupported } from "@imbricate/core";
import React from "react";
import { createPerformSearchAction } from "../actions/perform-search";
import { HummingbirdSearchResult } from "../types/search";

export type UseSearchResult = {

    searchResults: HummingbirdSearchResult[];
    loading: boolean;
    error: string | undefined;
    performSearch: (
        keyword: string,
    ) => Promise<void>;
};

export const useSearch = (
    origins: ImbricateOriginObject[],
): UseSearchResult => {

    const [searchResults, setSearchResults] = React.useState<HummingbirdSearchResult[]>([]);

    const [loading, setLoading] = React.useState<boolean>(false);
    const [error, setError] = React.useState<string | undefined>(undefined);

    const performSearchAction = React.useCallback(async (keyword: string) => {

        const searchResults: HummingbirdSearchResult[] = [];

        for (const origin of origins) {

            const isSupported = checkImbricateOriginFeatureSupported(
                origin.origin.supportedFeatures,
                IMBRICATE_ORIGIN_FEATURE.ORIGIN_SEARCH,
            );

            if (!isSupported) {
                continue;
            }

            const result: ImbricateOriginSearchOutcome = await origin.origin.search(keyword);

            if (typeof result === "symbol") {
                return result;
            }

            searchResults.push({
                origin: origin,
                items: result.items,
            });
        }

        return searchResults;
    }, []);

    const performSearch = async (
        keyword: string,
    ) => {

        if (typeof keyword !== "string"
            || keyword.length === 0
        ) {
            setError("Keyword is required");
            return;
        }

        setLoading(true);
        setSearchResults([]);

        try {

            const searchResults = await ActionCentral.getInstance().executeAction(
                () => {
                    return performSearchAction(keyword);
                },
                createPerformSearchAction(
                    keyword,
                    {
                        executer: import.meta.url,
                    },
                ),
            );

            setSearchResults(searchResults);
            setError(undefined);
            setLoading(false);
        } catch (error) {

            const errorMessage = error as string;

            setError(errorMessage);
            setLoading(false);
        }
    };

    return {
        searchResults,
        loading,
        error,
        performSearch,
    };
};
