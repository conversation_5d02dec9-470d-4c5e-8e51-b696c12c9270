/**
 * <AUTHOR>
 * @namespace Favorite_Hooks
 * @description Use Favorite
 */

import React, { useEffect } from "react";
import { ManagedConfigController } from "../../managed-config-controller";
import { HUMMINGBIRD_MANAGED_CONFIG_ITEM } from "../../types/managed-config/enum";
import { FavoriteDefinition } from "../types/favorite-definition";

export const useFavorite = (): FavoriteDefinition | null => {

    const [favorite, setFavorite] = React.useState<FavoriteDefinition | null>(null);

    useEffect(() => {

        const favoriteRecords: FavoriteDefinition = ManagedConfigController.getInstance()
            .getManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.FAVORITE);

        setFavorite(favoriteRecords);
    }, []);

    return favorite;
};
