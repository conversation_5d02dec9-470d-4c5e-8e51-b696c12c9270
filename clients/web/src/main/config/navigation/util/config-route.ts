/**
 * <AUTHOR>
 * @namespace Main_Config_Navigation_Util_ConfigRoute
 * @description Config Route
 */

export const getConfigGeneralRoute = (): string => {

    return "/config/general";
};

export const getConfigAppearanceRoute = (): string => {

    return "/config/appearance";
};

export const getConfigNavigationRoute = (): string => {

    return "/config/navigation";
};

export const getConfigEditorRoute = (): string => {

    return "/config/editor";
};

export const getConfigMarkdownRoute = (): string => {

    return "/config/markdown";
};

export const getConfigImbriScriptRoute = (): string => {

    return "/config/imbriscript";
};

export const getConfigImbricateScriptOutlookRoute = (): string => {

    return "/config/imbriscript/outlook";
};

export const getConfigEditorMonacoRoute = (): string => {

    return "/config/editor/monaco";
};

export const getConfigGuidanceRoute = (): string => {

    return "/config/guidance";
};

export const getConfigUsageHistoryRoute = (): string => {

    return "/config/usage-history";
};

export const getConfigImportExportRoute = (): string => {

    return "/config/import-export";
};

export const getConfigImportExportExportRoute = (): string => {

    return "/config/import-export/export";
};

export const getConfigImportExportImportRoute = (): string => {

    return "/config/import-export/import";
};

export const getConfigDebugRoute = (): string => {

    return "/config/debug";
};

export const getConfigAboutRoute = (): string => {

    return "/config/about";
};
