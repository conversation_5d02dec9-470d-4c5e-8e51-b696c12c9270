/**
 * <AUTHOR>
 * @namespace Data_Internationalization
 * @description Hook
 */

import { defaultEmptyFormat, useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { useEffect, useState } from "react";
import { dataInternationalization } from "./intl";
import { DATA_PROFILE } from "./profile";

export const useDataFormat = (): SudooFormat<DATA_PROFILE> => {

    const locale = useLocale();

    const [format, setFormat] = useState<SudooFormat<DATA_PROFILE>>(defaultEmptyFormat);

    useEffect(() => {

        const execute = async () => {

            const format = await dataInternationalization.format(locale);
            setFormat(format);
        };

        execute();
    }, [locale]);

    return format;
};
