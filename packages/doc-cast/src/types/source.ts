/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Types
 * @description Source
 */

export enum DOC_CAST_SOURCE_TYPE {

    DOCUMENT_STACK_IDENTIFIER = "DOCUMENT_STACK_IDENTIFIER",
    TEXT_PIECE_MARKDOWN = "TEXT_PIECE_MARKDOWN",
}

export type DocCastSourceDocumentStackIdentifierPayload = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;
};

export type DocCastSourceTextPieceMarkdownPayload = {

    readonly title: string;
    readonly markdown: string;
};

export type DocCastSourcePayloadSwitch<T extends DOC_CAST_SOURCE_TYPE> =
    T extends DOC_CAST_SOURCE_TYPE.DOCUMENT_STACK_IDENTIFIER ? DocCastSourceDocumentStackIdentifierPayload
    : T extends DOC_CAST_SOURCE_TYPE.TEXT_PIECE_MARKDOWN ? DocCastSourceTextPieceMarkdownPayload
    : never;

export type DocCastSource<T extends DOC_CAST_SOURCE_TYPE> = {

    readonly type: T;
    readonly payload: DocCastSourcePayloadSwitch<T>;
};
