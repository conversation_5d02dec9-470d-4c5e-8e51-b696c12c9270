/**
 * <AUTHOR>
 * @namespace Config_Panels
 * @description Root Panel
 */

import React, { FC } from "react";
import { Navigate } from "react-router-dom";
import { getConfigGeneralRoute } from "../navigation/util/config-route";

export type ConfigRootPanelProps = {
};

export const ConfigRootPanel: FC<ConfigRootPanelProps> = (
    _props: ConfigRootPanelProps,
) => {

    return (<Navigate
        to={getConfigGeneralRoute()}
        replace={true}
    />);
};
