/**
 * <AUTHOR>
 * @package UI
 * @namespace Button
 * @description Button Group
 */

import { ButtonGroup } from "@heroui/button";
import { UIButtonVariant } from "../button/types";
import { UIColor, UISize } from "../common/types";

export type UIButtonGroupProps = {

    readonly className?: string;

    readonly isFullWidth?: boolean;

    readonly color?: UIColor;
    readonly size?: UISize;
    readonly variant?: UIButtonVariant;

    readonly children?: React.ReactNode;
};

export const UIButtonGroup: React.FC<UIButtonGroupProps> = (
    props: UIButtonGroupProps,
) => {

    return (<ButtonGroup
        className={props.className}

        fullWidth={props.isFullWidth}

        color={props.color}
        size={props.size}
        variant={props.variant}
    >
        {props.children}
    </ButtonGroup>);
};
