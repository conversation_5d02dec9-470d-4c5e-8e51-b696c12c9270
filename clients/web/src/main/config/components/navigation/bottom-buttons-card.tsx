/**
 * <AUTHOR>
 * @namespace Main_Config_Components_Navigation
 * @description Bottom Buttons Card
 */

import { HUMMINGBIRD_NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE, HUMMINGBIRD_PREFERENCE_ITEM, usePreferenceItem } from "@imbricate-hummingbird/configuration";
import { FormattedRadioGroup, FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { TbLayoutBottombarFilled } from "react-icons/tb";
import { useConfigFormat } from "../../internationalization/hook";
import { CONFIG_PROFILE } from "../../internationalization/profile";

export type ConfigNavigationBottomButtonsCardProps = {
};

export const ConfigNavigationBottomButtonsCard: FC<ConfigNavigationBottomButtonsCardProps> = (
    _props: ConfigNavigationBottomButtonsCardProps,
) => {

    const configFormat = useConfigFormat();

    const navigationBottomButtonsDisplayMode = usePreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE,
    );

    return (<StyledCard>
        <UICardHeader>
            <TbLayoutBottombarFilled />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.BOTTOM_BUTTONS)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-2"
        >
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                    className="text-sm text-gray-500"
                >
                    {configFormat.get(CONFIG_PROFILE.BOTTOM_BUTTONS_DISPLAY_MODE_DESCRIPTION)}
                </FormattedText>
            </div>
            <FormattedRadioGroup
                label={configFormat.get(CONFIG_PROFILE.BOTTOM_BUTTONS_DISPLAY_MODE)}
                selected={navigationBottomButtonsDisplayMode.preference.toString()}
                onSelectedChange={(newSelected: string) => {

                    navigationBottomButtonsDisplayMode.updatePreference(
                        newSelected as HUMMINGBIRD_NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE,
                    );
                }}
                format={configFormat}
                items={[
                    {
                        itemKey: HUMMINGBIRD_NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE.TEXT,
                        itemProfileItem: CONFIG_PROFILE.TEXT,
                    },
                    {
                        itemKey: HUMMINGBIRD_NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE.ICON,
                        itemProfileItem: CONFIG_PROFILE.ICON,
                    },
                ]}
            />
        </UICardBody>
    </StyledCard>);
};
