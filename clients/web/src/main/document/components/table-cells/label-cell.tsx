/**
 * <AUTHOR>
 * @namespace Document_Components_TableCells
 * @description Label Cell
 */

import { LABEL_COLOR } from "@/common/database/label/label-color";
import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIChip, UISelect } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaPropertyOptionsLabel, ImbricateDatabaseSchemaPropertyOptionsLabelOption } from "@imbricate/core";
import { FC } from "react";
import { getLabelColorClassName, getLabelColorDot, getLabelColorTextClassNameReverse } from "../../../database/utils/label-color";
import { DocumentTableCellContent } from "./cell-content";

export type DocumentTableLabelCellProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.LABEL>;
    readonly getEditingProperty: () => TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.LABEL> | undefined;
    readonly updateEditingProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.LABEL>) => void;
    readonly editing: boolean;

    readonly options: ImbricateDatabaseSchemaPropertyOptionsLabel;
};

export const DocumentTableLabelCell: FC<DocumentTableLabelCellProps> = (
    props: DocumentTableLabelCellProps,
) => {

    if (props.editing) {

        const updatedProperty = props.getEditingProperty();

        if (typeof updatedProperty === "undefined") {
            throw new Error("[Imbricate] Updated property value not found");
        }

        return (<UISelect
            ariaLabel="Select label"
            selectionMode={props.options.allowMultiple ? "multiple" : "single"}
            selectedKeys={updatedProperty.value}
            onSelectedKeysChange={(
                newSelection: string[],
            ) => {

                props.updateEditingProperty({
                    ...updatedProperty,
                    value: newSelection,
                });
            }}
            items={props.options.labelOptions.map((
                each: ImbricateDatabaseSchemaPropertyOptionsLabelOption,
            ) => {

                return {
                    itemKey: each.labelIdentifier,
                    startContent: getLabelColorDot(each.labelColor as LABEL_COLOR),
                    content: each.labelName,
                };
            })}
        />);
    }

    return (<DocumentTableCellContent
        schemaType={IMBRICATE_PROPERTY_TYPE.LABEL}
        property={props.property}
        render={(
            propertyDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> | null,
        ) => {

            if (typeof propertyDraft === "undefined" || propertyDraft === null) {
                return null;
            }

            const value: string[] = propertyDraft.value as string[];

            if (!Array.isArray(value)) {
                return null;
            }

            const fixedValue: string[] = value as string[];

            return (<div className="flex gap-1 flex-wrap">
                {fixedValue
                    .map((each: string) => {
                        const targetOption = props.options.labelOptions.find((option) => option.labelIdentifier === each);

                        if (!targetOption) {
                            return null;
                        }

                        return targetOption;
                    })
                    .filter((each) => each !== null)
                    .map((each: ImbricateDatabaseSchemaPropertyOptionsLabelOption) => {
                        return (<UIChip
                            key={each.labelIdentifier}
                            size="sm"
                            baseClassName={getLabelColorClassName(
                                each.labelColor as LABEL_COLOR,
                            ) ?? ""}
                            contentClassName={getLabelColorTextClassNameReverse(
                                each.labelColor as LABEL_COLOR,
                            ) ?? ""}
                        >
                            {each.labelName}
                        </UIChip>);
                    })}
            </div>);
        }}
    />);
};
