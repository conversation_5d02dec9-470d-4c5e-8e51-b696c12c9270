{"name": "@imbricate-hummingbird/react-cross-section-core", "description": "Cross Section Core implementation for React", "private": true, "main": "src/index.ts", "scripts": {"test": "jest", "coverage": "jest --coverage", "compile": "tsc --project ./typescript/tsconfig.compile.json", "lint": "eslint src"}, "dependencies": {"@imbricate-hummingbird/flow-common": "workspace:*", "@imbricate-hummingbird/platformless": "workspace:*", "@imbricate-hummingbird/theme-core": "workspace:*", "@imbricate-hummingbird/ui": "workspace:*", "@xyflow/react": "catalog:", "clsx": "catalog:", "react": "catalog:", "react-dom": "catalog:", "react-icons": "catalog:"}, "devDependencies": {"@imbricate-hummingbird/config": "workspace:*", "@imbricate-hummingbird/storybook-common": "workspace:*", "@storybook/react": "8.6.14", "@types/react": "catalog:", "@types/react-dom": "catalog:", "eslint": "catalog:"}}