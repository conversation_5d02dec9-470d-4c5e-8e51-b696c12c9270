/**
 * <AUTHOR>
 * @namespace Table_Editor
 * @description Markdown Table Editor
 */

import { stringifyMarkdownTable } from "@imbricate-hummingbird/monaco-markdown";
import { UIButton, UIDivider, UIModal, UIModalBody, UIModalFooter, UIModalHeader, useUIDisclosure } from "@imbricate-hummingbird/ui";
import React, { FC, useEffect, useRef, useState } from "react";
import { DynamicOpener } from "../../../common/controller/dynamic-opener";
import { EditMarkdownTable } from "./editable-table";

export const markdownTableEditorDynamicOpener = DynamicOpener.create<MarkdownTableEditorModalProps>();

export type MarkdownTableEditorModalProps = {

    readonly data: string[][];

    readonly onInject: (data: string) => void;
};

export const MarkdownTableEditorModal: FC = () => {

    const onInjectRef = useRef<(data: string) => void>(null);

    const [data, setData] = useState<string[][]>([]);
    const { isOpen, onOpen, onOpenChange } = useUIDisclosure();

    useEffect(() => {

        const listener = (props: MarkdownTableEditorModalProps) => {
            onOpen();
            setData(props.data);
            onInjectRef.current = props.onInject;
        };

        markdownTableEditorDynamicOpener.attachListener(listener);

        return () => {
            markdownTableEditorDynamicOpener.detachListener(listener);
        };
    }, []);

    return (<UIModal
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        size="3xl"
        isDismissable={false}
        isKeyboardDismissDisabled={true}
    >
        {(onClose: () => void) => {
            return (<React.Fragment>
                <UIModalHeader className="flex flex-col gap-1">
                    Markdown Table Editor
                </UIModalHeader>
                <UIDivider />
                <UIModalBody
                    className="p-0"
                >
                    <EditMarkdownTable
                        data={data}
                        setData={setData}
                    />
                </UIModalBody>
                <UIDivider />
                <UIModalFooter>
                    <UIButton
                        size="sm"
                        color="primary"
                        variant="flat"
                        onPress={() => {
                            onClose();
                            onInjectRef.current?.(stringifyMarkdownTable(data));
                        }}
                    >
                        Inject
                    </UIButton>
                </UIModalFooter>
            </React.Fragment>);
        }}
    </UIModal>);
};
