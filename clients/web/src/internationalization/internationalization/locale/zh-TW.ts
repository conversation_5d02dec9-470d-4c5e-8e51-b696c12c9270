/**
 * <AUTHOR>
 * @namespace Internationalization_Internationalization_Locale
 * @description Zh-TW
 */

import { IETF_LOCALE } from "@sudoo/locale";

// spell-checker: disable
export const zhTWInternationalizationProfile: Record<IETF_LOCALE, string> = {

    [IETF_LOCALE.CHINESE_SIMPLIFIED]: "簡體中文",
    [IETF_LOCALE.CHINESE_TRADITIONAL]: "正體字中文",
    [IETF_LOCALE.ENGLISH_CANADA]: "加拿大英文",
    [IETF_LOCALE.ENGLISH_UNITED_KINGDOM]: "英國英文",
    [IETF_LOCALE.ENGLISH_UNITED_STATES]: "美式英文",
    [IETF_LOCALE.FRENCH_CANADA]: "加拿大法文",
    [IETF_LOCALE.FRENCH_FRANCE]: "法文",
    [IETF_LOCALE.JAPANESE_JAPAN]: "日文",
    [IETF_LOCALE.KOREAN_KOREA]: "韓文",
    [IETF_LOCALE.MAORI_NEW_ZEALAND]: "毛利文",
    [IETF_LOCALE.RUSSIAN_RUSSIA]: "俄文",
    [IETF_LOCALE.SPANISH_MEXICO]: "墨西哥西班牙文",
    [IETF_LOCALE.SPANISH_SPAIN]: "西班牙文",
};
// spell-checker: enable
