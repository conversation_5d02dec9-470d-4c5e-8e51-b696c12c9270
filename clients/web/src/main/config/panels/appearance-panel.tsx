/**
 * <AUTHOR>
 * @namespace Config_Panels
 * @description Appearance Panel
 */

import React, { FC } from "react";
import { ConfigAnimationCard } from "../components/appearance/animation-card";
import { ConfigThemeCard } from "../components/appearance/theme-card";
import { ConfigMagicButton } from "../components/appearance/magic-button";

export type ConfigAppearancePanelProps = {
};

// LAZY LOAD ONLY
const ConfigAppearancePanel: FC<ConfigAppearancePanelProps> = (
    _props: ConfigAppearancePanelProps,
) => {

    return (<div
        className="flex flex-col gap-2 pr-2 py-2 flex-1"
    >
        <ConfigMagicButton />
        <ConfigThemeCard />
        <ConfigAnimationCard />
    </div>);
};
export default ConfigAppearancePanel;
