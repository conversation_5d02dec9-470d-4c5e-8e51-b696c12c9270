/**
 * <AUTHOR>
 * @namespace Common_Components
 * @description Footer
 */

import { ApplicationVersion } from "@/common/types/app-version";
import { UISpacer } from "@imbricate-hummingbird/ui";
import clsx from "clsx";
import { FC, useState } from "react";

export type BrandFooterProps = {

    readonly noBranding?: boolean;
    readonly onBrandEasterEgg?: () => void;
};

export const BrandFooter: FC<BrandFooterProps> = (
    props: BrandFooterProps,
) => {

    const [brandClickedTimes, setBrandClickedTimes] = useState(0);

    const copyRightYear: number = new Date().getFullYear();

    return (<div
        className="flex pl-1 pr-3 pb-1.5 justify-center items-center"
    >
        <div
            className={clsx("text-tiny", {
                "cursor-pointer": typeof props.onBrandEasterEgg === "function",
            })}
            onClick={typeof props.onBrandEasterEgg === "function" ? () => {

                if (typeof props.onBrandEasterEgg !== "function") {
                    return;
                }

                if (brandClickedTimes >= 4) {
                    props.onBrandEasterEgg();
                }
                setBrandClickedTimes(brandClickedTimes + 1);
            } : undefined}
        >
            <div
                className="font-mono font-semibold"
            >
                v{ApplicationVersion}
            </div>
            <div
                className="font-mono"
            >
                Imbricate Hummingbird Web
            </div>
        </div>
        <UISpacer
            isFlexFill
        />
        {!props.noBranding && <div
            className="text-sm font-semibold text-gray-900 dark:text-white"
        >
            <a
                href="https://sudosaurus.com"
                target="_blank"
            >
                &copy; {copyRightYear} <span className="font-mono">
                    SUDOSAURUS
                </span>
            </a>
        </div>}
    </div>);
};
