/**
 * <AUTHOR>
 * @namespace Common_Components_HeaderGroup
 * @description Header Group
 */

import { useCommonFormat } from "@/common/internationalization/hook";
import { FormattedText } from "@imbricate-hummingbird/react-components";
import { UIPopover, createUIButton } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { IconType } from "react-icons";
import { getHeaderGroupIcon, getHeaderGroupTitle } from "./group";
import { getHeaderGroupOriginData } from "./origin-data";
import { HEADER_GROUP_GROUP, HeaderGroupProps } from "./types";

export const HeaderGroup: FC<HeaderGroupProps<HEADER_GROUP_GROUP>> = (
    props: HeaderGroupProps<HEADER_GROUP_GROUP>,
) => {

    const commonFormat = useCommonFormat();

    const HeaderIcon: IconType = getHeaderGroupIcon(props.group);

    return (<div
        className="flex items-center gap-1"
    >
        <UIPopover
            contentClassName="p-0"
            trigger={createUIButton({
                isIconOnly: true,
                variant: "ghost",
                children: (<HeaderIcon
                    className="text-2xl"
                />),
            })}
        >
            {getHeaderGroupOriginData({
                ...props,
                commonFormat,
            })}
        </UIPopover>
        <div
            className="font-mono"
        >
            <FormattedText
                usedFormats={[commonFormat]}
            >
                {getHeaderGroupTitle(props.group, commonFormat)}
            </FormattedText>
        </div>
    </div>);
};
