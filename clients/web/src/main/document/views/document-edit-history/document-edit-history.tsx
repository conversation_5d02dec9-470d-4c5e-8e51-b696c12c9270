/**
 * <AUTHOR>
 * @namespace Document_Views_DocumentEditHistory
 * @description Document Edit History
 */

import { getRouteDatabaseDocumentsView, getRouteDocumentView } from "@imbricate-hummingbird/navigation-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { useAsyncTitle } from "@imbricate-hummingbird/react-common";
import { LoadingWrapper } from "@imbricate-hummingbird/react-components";
import { useNavigateDocumentView } from "@imbricate-hummingbird/react-navigation";
import { UIBreadcrumbs, UIButton, UINavbar, UISpacer } from "@imbricate-hummingbird/ui";
import { IImbricateDatabase, IImbricateDocument } from "@imbricate/core";
import React, { FC } from "react";
import { IoIosDocument } from "react-icons/io";
import { MdOutlineHistoryEdu } from "react-icons/md";
import { useParams } from "react-router-dom";
import { ErrorScreen } from "../../../common/components/error-screen";
import { UseDatabaseResponseSymbol, useDatabase } from "../../../database/hooks/use-database";
import { rootLogger } from "../../../log/logger";
import { useWideOrigin } from "../../../origin/hooks/use-wide-origin";
import { DocumentsEditHistory } from "../../components/edit-history";
import { S_UseDocumentErrored, UseDocumentResponseSymbol, useOldDocument } from "../../hooks/use-document";
import { useDocumentFormat } from "../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../internationalization/profile";
import { getDocumentPrimary } from "../../util/primary";

const logger = rootLogger.fork({
    scopes: [
        "Document",
        "DocumentEditHistory",
    ],
});

// LAZY LOAD ONLY
const DocumentEditHistoryView: FC = () => {

    const documentFormat = useDocumentFormat();

    const params = useParams();
    const databaseUniqueIdentifier: string =
        params["database-unique-identifier"] as string;
    const documentUniqueIdentifier: string =
        params["document-unique-identifier"] as string;

    const origin: ImbricateOriginObject | null =
        useWideOrigin(databaseUniqueIdentifier);

    const database: IImbricateDatabase | UseDatabaseResponseSymbol =
        useDatabase(databaseUniqueIdentifier);

    const document: IImbricateDocument | UseDocumentResponseSymbol =
        useOldDocument(
            databaseUniqueIdentifier,
            documentUniqueIdentifier,
        );

    const navigateToDocumentView = useNavigateDocumentView();

    useAsyncTitle(
        () => typeof document !== "symbol" && typeof database !== "symbol",
        () => {

            if (typeof document === "symbol" || typeof database === "symbol") {
                return [];
            }

            const properties = document.getProperties();

            if (typeof properties === "symbol") {
                logger.error("Properties is symbol", {
                    databaseUniqueIdentifier,
                    documentUniqueIdentifier,
                });
                return [];
            }

            const primary: string | null = getDocumentPrimary(
                database.schema,
                properties.properties,
            );

            return [
                primary ?? document.uniqueIdentifier,
                "Edit History",
                "Document",
            ];
        },
        [typeof document],
    );

    if (document === S_UseDocumentErrored) {

        return (<ErrorScreen
            color="warning"
            title="Unable to load document"
            message={<div>
                <div>Database: {databaseUniqueIdentifier}</div>
                <div>Document: {documentUniqueIdentifier}</div>
            </div>}
        />);
    }

    if (!origin
        || typeof document === "symbol"
        || typeof database === "symbol") {

        return (<LoadingWrapper
            debugDescription="Document Edit History View"
            fullHeight
        />);
    }

    const properties = document.getProperties();

    if (typeof properties === "symbol") {
        logger.error("Properties is symbol", {
            databaseUniqueIdentifier,
            documentUniqueIdentifier,
        });
        return [];
    }

    const primary: string | null = getDocumentPrimary(
        database.schema,
        properties.properties,
    ) ?? document.uniqueIdentifier;

    return (<div
        className="h-full overflow-auto flex flex-col"
    >
        <UINavbar
            isFullWidth
            isBordered
            startContent={<React.Fragment>
                <IoIosDocument
                    className="text-2xl"
                />
                <UISpacer />
                <p
                    className="font-mono"
                >
                    {documentFormat.get(DOCUMENT_PROFILE.DOCUMENT)}
                </p>
            </React.Fragment>}
            coreContent={<p className="font-bold text-xl">
                {primary}
            </p>}
            endContent={<UIButton
                startContent={<MdOutlineHistoryEdu />}
                variant="flat"
                onPress={() => {
                    navigateToDocumentView(
                        origin.origin.uniqueIdentifier,
                        database.uniqueIdentifier,
                        document.uniqueIdentifier,
                        {
                            replace: true,
                        },
                    );
                }}
            >
                {documentFormat.get(DOCUMENT_PROFILE.DOCUMENT_DETAILS)}
            </UIButton>}
        />
        <UIBreadcrumbs
            items={[
                {
                    label: database.databaseName,
                    href: getRouteDatabaseDocumentsView(database.uniqueIdentifier),
                },
                {
                    label: document.uniqueIdentifier,
                    href: getRouteDocumentView(
                        database.uniqueIdentifier,
                        document.uniqueIdentifier,
                    ),
                },
                {
                    label: documentFormat.get(DOCUMENT_PROFILE.EDIT_HISTORY),
                },
            ]}
        />
        <DocumentsEditHistory
            originUniqueIdentifier={origin.origin.uniqueIdentifier}
            databaseUniqueIdentifier={database.uniqueIdentifier}
            documentUniqueIdentifier={document.uniqueIdentifier}
        />
    </div>);
};
export default DocumentEditHistoryView;
