/**
 * <AUTHOR>
 * @namespace Drawer
 * @description Drawer Render
 */

import { DrawerSliceState, useDrawerAction } from "@imbricate-hummingbird/react-store";
import { UIButton, UIButtonLink, UIDivider, U<PERSON>rawer, UIDrawerBody, <PERSON><PERSON><PERSON>er<PERSON>ooter, UIDrawerHeader, UITooltip } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { FaAngleDoubleRight, FaExternalLinkAlt } from "react-icons/fa";
import { IoCaretBackCircle, IoCaretForwardCircle } from "react-icons/io5";
import { DrawerProviderBody } from "./drawer-body";
import { useDrawerFormat } from "./internationalization/hook";
import { DRAWER_PROFILE } from "./internationalization/profile";

export type DrawerRenderProps = {

    readonly drawerSlice: DrawerSliceState;
};

// LAZY LOAD ONLY
const DrawerRender: FC<DrawerRenderProps> = (
    props: DrawerRenderProps,
) => {

    const drawerFormat = useDrawerFormat();

    const drawerSlice = props.drawerSlice;
    const drawerActions = useDrawerAction();

    if (drawerSlice.currentDrawerStatusIndex === null) {
        return null;
    }

    const drawerStatus = drawerSlice.drawerStatuses[drawerSlice.currentDrawerStatusIndex];

    if (!drawerStatus) {
        return null;
    }

    const shouldShowPreviousButton = drawerSlice.currentDrawerStatusIndex > 0;
    const shouldShowNextButton = drawerSlice.currentDrawerStatusIndex < drawerSlice.drawerStatuses.length - 1;

    return (<UIDrawer
        radius="sm"
        isOpen={drawerSlice.drawerOpen}
        size="2xl"
        onOpenChange={(isOpen) => {
            if (!isOpen) {
                drawerActions.closeDrawer();
            }
        }}
    >
        {(
            onClose: () => void,
        ) => (
            <React.Fragment>
                <UIDrawerHeader
                    className="flex gap-2 items-center"
                >
                    <UITooltip
                        content="Close"
                    >
                        <UIButton
                            isIconOnly
                            variant="flat"
                            onPress={onClose}
                        >
                            <FaAngleDoubleRight
                                className="text-2xl"
                            />
                        </UIButton>
                    </UITooltip>
                    <div
                        className="flex-1"
                    >
                        {drawerStatus.title}
                    </div>
                    {drawerStatus.fullScreenLink && <div>
                        <UIButtonLink
                            variant="flat"
                            href={drawerStatus.fullScreenLink}
                            hrefInNewTab
                            isIconOnly
                        >
                            <FaExternalLinkAlt />
                        </UIButtonLink>
                    </div>}
                </UIDrawerHeader>
                <UIDivider />
                {drawerStatus
                    ? <DrawerProviderBody
                        drawerStatus={drawerStatus}
                    />
                    : <UIDrawerBody>
                        No Content
                    </UIDrawerBody>}
                <UIDivider />
                <UIDrawerFooter
                    className="flex gap-2 items-center"
                >
                    <UIButton
                        size="lg"
                        isIconOnly
                        color={shouldShowPreviousButton ? "primary" : "default"}
                        variant="flat"
                        isDisabled={!shouldShowPreviousButton}
                        onPress={drawerActions.previousDrawer}
                    >
                        <IoCaretBackCircle
                            className="text-2xl"
                        />
                    </UIButton>
                    <UIButton
                        size="lg"
                        isIconOnly
                        color={shouldShowNextButton ? "primary" : "default"}
                        variant="flat"
                        isDisabled={!shouldShowNextButton}
                        onPress={drawerActions.nextDrawer}
                    >
                        <IoCaretForwardCircle
                            className="text-2xl"
                        />
                    </UIButton>
                    <div
                        className="flex-1"
                    />
                    <UIButton
                        variant="light"
                        onPress={onClose}
                    >
                        {drawerFormat.get(DRAWER_PROFILE.CLOSE)}
                    </UIButton>
                </UIDrawerFooter>
            </React.Fragment>
        )}
    </UIDrawer>);
};
export default DrawerRender;
