/**
 * <AUTHOR>
 * @namespace Lens_Components_Renders
 * @description Render Pending
 */

import { ExecutionAction } from "@/script/components/execution-action";
import { ExecutionStatus } from "@/script/components/execution-status";
import { ImbriScriptResult } from "@imbricate-hummingbird/script-core";
import { FC } from "react";

export type RenderPendingProps = {

    readonly result: ImbriScriptResult | null;
};

export const RenderPending: FC<RenderPendingProps> = (
    props: RenderPendingProps,
) => {

    if (!props.result) {
        return null;
    }

    return (<div
        className="h-full flex flex-col gap-2 justify-center"
    >
        <ExecutionStatus
            result={props.result}
        />
        <ExecutionAction
            result={props.result}
        />
    </div>);
};
