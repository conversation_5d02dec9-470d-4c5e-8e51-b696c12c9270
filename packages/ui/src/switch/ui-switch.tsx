/**
 * <AUTHOR>
 * @package UI
 * @namespace Switch
 * @description Switch
 */

import { Switch } from "@heroui/switch";
import type { FC, ReactNode } from "react";
import { UIColor, UISize } from "../common/types";

export type UISwitchProps = {

    readonly className?: string;
    readonly thumbIconClassName?: string;

    readonly isDisabled?: boolean;

    readonly color?: UIColor;
    readonly size?: UISize;

    readonly width?: string;
    readonly height?: string;

    readonly isSelected?: boolean;
    readonly onSelectedChange?: (newSelected: boolean) => void;

    readonly children?: ReactNode;

    readonly thumbIcon?: ReactNode;
};

export const UISwitch: FC<UISwitchProps> = (
    props: UISwitchProps,
) => {

    return (<Switch
        className={props.className}
        classNames={{
            thumbIcon: props.thumbIconClassName,
        }}

        isDisabled={props.isDisabled}

        color={props.color}
        size={props.size}

        width={props.width}
        height={props.height}

        isSelected={props.isSelected}
        onValueChange={props.onSelectedChange}

        thumbIcon={props.thumbIcon}
    >
        {props.children}
    </Switch>);
};
