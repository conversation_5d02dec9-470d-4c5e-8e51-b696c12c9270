/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Flow_Node
 * @description Source Node
 */

import { DocCastSourceNodeColumn } from "./source-column";
import { DocCastSourceNodeData } from "./source-node-data";

export type DocCastSourceNodeProps = {

    readonly data: DocCastSourceNodeData;
};

export const DocCastSourceNode: React.FC<DocCastSourceNodeProps> = (
    _props: DocCastSourceNodeProps,
) => {

    return (<div
        className="bg-background border border-border"
    >
        <div
            className="py-2 px-1 flex items-center gap-1"
        >
            <DocCastSourceNodeColumn />
        </div>
    </div>);
};
