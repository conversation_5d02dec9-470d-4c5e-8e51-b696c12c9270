/**
 * <AUTHOR>
 * @namespace Lens_Components_Renders
 * @description Render Imbriscript Error
 */

import { ErrorScreen } from "@/main/common/components/error-screen";
import { ExecutionLogsArea } from "@/script/components/log/logs-area";
import { ImbriScriptResult } from "@imbricate-hummingbird/script-core";
import { UIButton } from "@imbricate-hummingbird/ui";
import { FC, useState } from "react";

export type RenderImbriscriptErrorProps = {

    readonly result: ImbriScriptResult;
};

export const RenderImbriscriptError: FC<RenderImbriscriptErrorProps> = (
    props: RenderImbriscriptErrorProps,
) => {

    const [showLogs, setShowLogs] = useState(false);

    if (showLogs) {
        return (<div
            className="h-full w-full flex justify-center items-center"
        >
            <ExecutionLogsArea
                result={props.result}
                progressing={false}
            />
        </div>);
    }

    return (<div
        className="h-full"
    >
        <ErrorScreen
            title="No Export from Imbriscript"
            message={<div>
                The default export is not an object
                <UIButton
                    isFullWidth
                    variant="flat"
                    color="primary"
                    onPress={() => {
                        setShowLogs(true);
                    }}
                >
                    Show Execution Logs
                </UIButton>
            </div>}
        />
    </div>);
};
