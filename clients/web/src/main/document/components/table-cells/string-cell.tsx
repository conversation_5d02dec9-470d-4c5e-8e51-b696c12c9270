/**
 * <AUTHOR>
 * @namespace Document_Components_TableCells
 * @description String Cell
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIInput } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { DocumentTableCellContent } from "./cell-content";

export type DocumentTableStringCellProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.STRING>;
    readonly getEditingProperty: () => TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.STRING> | undefined;
    readonly updateEditingProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.STRING>) => void;
    readonly editing: boolean;
};

export const DocumentTableStringCell: FC<DocumentTableStringCellProps> = (
    props: DocumentTableStringCellProps,
) => {

    if (props.editing) {

        const updatedProperty = props.getEditingProperty();

        if (typeof updatedProperty === "undefined") {
            throw new Error("[Imbricate] Updated property value not found");
        }

        return (<UIInput
            value={updatedProperty.value}
            isFullWidth
            onValueChange={(
                newValue: string,
            ) => {

                props.updateEditingProperty(
                    {
                        ...updatedProperty,
                        value: newValue,
                    },
                );
            }}
        />);
    }

    return (<DocumentTableCellContent
        schemaType={IMBRICATE_PROPERTY_TYPE.STRING}
        property={props.property}
    />);
};
