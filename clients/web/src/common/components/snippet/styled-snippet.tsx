/**
 * <AUTHOR>
 * @namespace Common_Components_Snippet
 * @description Styled Snippet
 */

import { useCommonFormat } from "@/common/internationalization/hook";
import { COMMON_PROFILE } from "@/common/internationalization/profile";
import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { UIColor, UISize, UISnippet, UISnippetVariant } from "@imbricate-hummingbird/ui";
import clsx from "clsx";
import React, { FC } from "react";

export type StyledSnippetProps = {

    readonly children: React.ReactNode;

    readonly size?: UISize;
    readonly color?: UIColor;
    readonly variant?: UISnippetVariant;

    readonly symbol?: string;
    readonly className?: string;
};

export const StyledSnippet: FC<StyledSnippetProps> = (
    props: StyledSnippetProps,
) => {

    const commonFormat = useCommonFormat();

    if (isFormatLoading(commonFormat)) {
        return null;
    }

    return (<UISnippet
        copyButtonTooltipContent={commonFormat.get(COMMON_PROFILE.COPY_TO_CLIPBOARD)}
        copyButtonTooltipPlacement="right"
        size={props.size}
        color={props.color}
        variant={props.variant}
        leadingSymbol={props.symbol}
        className={clsx(
            "w-full",
            props.className,
        )}
    >
        {props.children}
    </UISnippet>);
};
