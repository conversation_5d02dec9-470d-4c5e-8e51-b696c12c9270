/**
 * <AUTHOR>
 * @namespace Origin_Views_NewOriginView
 * @description New Origin View
 */

import { useTitle } from "@imbricate-hummingbird/react-common";
import { UINavbar, UISpacer } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { TbWorld } from "react-icons/tb";
import { NewOriginCard } from "../../components/origin-new/origin-new-card";

export type NewOriginViewProps = {
};

// LAZY LOAD ONLY
const NewOriginView: FC<NewOriginViewProps> = (
    _props: NewOriginViewProps,
) => {

    useTitle([
        "New Origin",
    ], []);

    return (<div
        className="flex flex-col gap-2"
    >
        <UINavbar
            isFullWidth
            isBordered
            startContent={<React.Fragment>
                <TbWorld
                    className="text-2xl"
                />
                <UISpacer />
                <p
                    className="font-mono"
                >
                    Origin
                </p>
            </React.Fragment>}
            coreContent={<p className="font-bold text-xl">
                Add Origin
            </p>}
        />
        <div
            className="flex flex-col gap-2 pr-2"
        >
            <NewOriginCard />
        </div>
    </div>);
};
export default NewOriginView;
