/**
 * <AUTHOR>
 * @namespace CrossSection_Hooks
 * @description Use React Flow
 */

import { useFlowLayout } from "@imbricate-hummingbird/flow-common";
import { Connection, Edge, EdgeChange, Node, NodeChange, ReactFlowInstance } from "@xyflow/react";
import { useRef, useState } from "react";
import { mapCrossSectionEdges } from "../edge/util/map-cross-section-edge";
import { mapCrossSectionNodes } from "../node/util/map-cross-section-node";
import { CrossSection } from "../types/cross-section";
import { useCrossSectionConnection } from "./use-connect";
import { useCrossSectionEdgesChange } from "./use-edges-change";
import { useCrossSectionNodesChange } from "./use-nodes-change";
import { useCrossSectionReconnect } from "./use-reconnect";
import { useCrossSectionReconnectEnd } from "./use-reconnect-end";
import { useCrossSectionReconnectStart } from "./use-reconnect-start";

export type UseCrossSectionReactFlowResult = {

    readonly nodes: Node[];
    readonly edges: Edge[];

    readonly onNodesChange: (changes: NodeChange[]) => void;
    readonly onEdgesChange: (changes: EdgeChange[]) => void;

    readonly onConnect: (connection: Connection) => void;

    readonly onReconnectStart: (event: React.MouseEvent, edge: Edge) => void;
    readonly onReconnectEnd: (event: MouseEvent | TouchEvent, edge: Edge) => void;
    readonly onReconnect: (oldEdge: Edge, newConnection: Connection) => void;

    readonly onLayout: (direction: string) => void;
};

export const useCrossSectionReactFlow = (
    crossSection: CrossSection,
    reactFlow: ReactFlowInstance,
) => {

    const edgeReconnectSuccessful = useRef(true);

    const [parsedNodes, setParsedNodes] = useState(() => {
        return mapCrossSectionNodes(crossSection.nodes);
    });

    const [parsedEdges, setParsedEdges] = useState(() => {
        return mapCrossSectionEdges(crossSection.edges);
    });

    const onNodesChange = useCrossSectionNodesChange(setParsedNodes);
    const onEdgesChange = useCrossSectionEdgesChange(setParsedEdges);

    const onConnect = useCrossSectionConnection(setParsedEdges);

    const onReconnectStart = useCrossSectionReconnectStart(
        edgeReconnectSuccessful,
    );
    const onReconnectEnd = useCrossSectionReconnectEnd(
        setParsedEdges,
        edgeReconnectSuccessful,
    );
    const onReconnect = useCrossSectionReconnect(
        setParsedEdges,
        edgeReconnectSuccessful,
    );

    const onLayout = useFlowLayout(
        parsedNodes,
        parsedEdges,
        setParsedNodes,
        reactFlow,
    );

    return {

        nodes: parsedNodes,
        edges: parsedEdges,
        onNodesChange,
        onEdgesChange,
        onConnect,
        onReconnectStart,
        onReconnectEnd,
        onReconnect,
        onLayout,
    };
};
