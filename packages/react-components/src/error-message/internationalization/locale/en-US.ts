/**
 * <AUTHOR>
 * @package React Components
 * @namespace ErrorMessage_Internationalization_Locale
 * @description En-US
 */

import { REACT_COMPONENTS_ERROR_MESSAGE_PROFILE } from "../profile";

export const enUSReactComponentsErrorMessageProfile: Record<REACT_COMPONENTS_ERROR_MESSAGE_PROFILE, string> = {

    [REACT_COMPONENTS_ERROR_MESSAGE_PROFILE.DEFAULT_ERROR_MESSAGE]: "There's something wrong with the application. Please try again later. or check the trouble shoot guide.",
    [REACT_COMPONENTS_ERROR_MESSAGE_PROFILE.DEFAULT_ERROR_TITLE]: "Something went wrong",
    [REACT_COMPONENTS_ERROR_MESSAGE_PROFILE.TROUBLESHOOT_GUIDE]: "Trouble Shoot Guide",
};
