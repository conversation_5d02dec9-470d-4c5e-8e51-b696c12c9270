/**
 * <AUTHOR>
 * @namespace Debug_Components
 * @description Debug Action Bar
 */

import React, { FC } from "react";
import { DebugActionBarTab } from "./debug-action-bar-tab";
import { DebugActionButton } from "./debug-actions/debug-action-button";
import { DebugSettingButton } from "./debug-settings/debug-setting-button";
import { DebugTabsButton } from "./debug-tabs/debug-tabs-button";
import { RecentActionTrackerWrapper } from "./recent-action-tracker/recent-action-tracker-wrapper";
import { WorkerActivitiesWrapper } from "./worker-activities/worker-activities-wrapper";

// LAZY LOAD ONLY
const DebugActionBar: FC = () => {

    const [showingTabs, setShowingTabs] = React.useState<boolean>(false);

    return (<div
        className="fixed bottom-10 right-10 z-10 flex flex-col gap-4"
    >
        {showingTabs && <div
            className="self-end"
        >
            <DebugActionBarTab />
        </div>}
        <div
            className="flex flex-row gap-2 self-end"
        >
            <RecentActionTrackerWrapper />
            <WorkerActivitiesWrapper />
            <DebugSettingButton />
            <DebugActionButton />
            <DebugTabsButton
                onToggle={() => {
                    setShowingTabs(!showingTabs);
                }}
            />
        </div>
    </div>);
};

export default DebugActionBar;
