/**
 * <AUTHOR>
 * @namespace Document_Components_TableCells
 * @description Number Cell
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIInput } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { DocumentTableCellContent } from "./cell-content";

export type DocumentTableNumberCellProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.NUMBER>;
    readonly getEditingProperty: () => TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.NUMBER> | undefined;
    readonly updateEditingProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.NUMBER>) => void;
    readonly editing: boolean;
};

export const DocumentTableNumberCell: FC<DocumentTableNumberCellProps> = (
    props: DocumentTableNumberCellProps,
) => {

    if (props.editing) {

        const updatedProperty = props.getEditingProperty();

        if (typeof updatedProperty === "undefined") {
            throw new Error("[Imbricate] Updated property value not found");
        }

        return (<UIInput
            aria-label="document-number-cell"
            type="number"
            value={String(updatedProperty.value)}
            onValueChange={(
                newValue: string,
            ) => {

                props.updateEditingProperty(
                    {
                        ...updatedProperty,
                        value: Number(newValue),
                    },
                );
            }}
        />);
    }

    return (<DocumentTableCellContent
        schemaType={IMBRICATE_PROPERTY_TYPE.NUMBER}
        property={props.property}
    />);
};
