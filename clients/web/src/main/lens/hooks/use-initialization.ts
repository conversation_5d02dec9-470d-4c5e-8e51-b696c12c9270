/**
 * <AUTHOR>
 * @namespace Lens_Hooks
 * @description Use Initialization
 */

import { HUMMINGBIRD_MANAGED_CONFIG_ITEM, ManagedConfigController } from "@imbricate-hummingbird/configuration";
import { LensSlice } from "@imbricate-hummingbird/react-store";
import { useEffect } from "react";
import { useDispatch } from "react-redux";

export const useLensInitialization = () => {

    const dispatch = useDispatch();

    useEffect(() => {

        const lensConfig = ManagedConfigController.getInstance()
            .getManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.LENS_CONFIG);

        dispatch(
            LensSlice.actions.setLensConfig(lensConfig),
        );
    }, []);
};
