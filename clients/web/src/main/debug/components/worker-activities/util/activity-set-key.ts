/**
 * <AUTHOR>
 * @namespace Main_Debug_Components_WorkerActivities_Util
 * @description Activity Set Key
 */

import { WorkerActivityItem } from "@imbricate-hummingbird/bridge-core";

export const buildActivitySetKey = (
    activitySet: [WorkerActivityItem | null, WorkerActivityItem | null],
): string => {

    return activitySet.map((
        activity: WorkerActivityItem | null,
    ) => {

        if (!activity) {
            return "NO_ACTIVITY";
        }

        const keyList = [
            activity.initiator,
            activity.emitAt,
            activity.worker,
            activity.direction,
            activity.responseIdentifier ?? JSON.stringify(activity.payload),
        ];

        return keyList.join("-");
    }).join("<->");
};
