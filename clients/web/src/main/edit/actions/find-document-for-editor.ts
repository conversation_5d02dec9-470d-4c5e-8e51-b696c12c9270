/**
 * <AUTHOR>
 * @namespace Edit_Actions
 * @description Find Document For Editor
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createFindDocumentForEditorAction = (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Find Document For Editor",
        actionDescription: `Find the document for [${originUniqueIdentifier}], database [${databaseUniqueIdentifier}], document [${documentUniqueIdentifier}]`,
        executerMetadata: metadata,
    };
};
