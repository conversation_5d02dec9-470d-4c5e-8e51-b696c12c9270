/**
 * <AUTHOR>
 * @namespace Database_Internationalization
 * @description Hook
 */

import { defaultEmptyFormat, useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { useEffect, useState } from "react";
import { databaseInternationalization } from "./intl";
import { DATABASE_PROFILE } from "./profile";

export const useDatabaseFormat = (): SudooFormat<DATABASE_PROFILE> => {

    const locale = useLocale();

    const [format, setFormat] = useState<SudooFormat<DATABASE_PROFILE>>(defaultEmptyFormat);

    useEffect(() => {

        const execute = async () => {

            const format = await databaseInternationalization.format(locale);
            setFormat(format);
        };

        execute();
    }, [locale]);

    return format;
};
