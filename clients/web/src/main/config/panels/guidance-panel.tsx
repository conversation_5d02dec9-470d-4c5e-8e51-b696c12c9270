/**
 * <AUTHOR>
 * @namespace Config_Panels
 * @description Guidance Panel
 */

import React, { FC } from "react";
import { ConfigGuidanceDismissedGuidanceCard } from "../components/guidance/dismissed-guidance";
import { ConfigGuidanceResetDismissedGuidanceCard } from "../components/guidance/reset-dismissed-guidance";

export type ConfigGuidancePanelProps = {
};

// LAZY LOAD ONLY
const ConfigGuidancePanel: FC<ConfigGuidancePanelProps> = (
    _props: ConfigGuidancePanelProps,
) => {

    return (<div
        className="flex flex-col gap-2 pr-2 py-2 flex-1"
    >
        <ConfigGuidanceDismissedGuidanceCard />
        <ConfigGuidanceResetDismissedGuidanceCard />
    </div>);
};
export default ConfigGuidancePanel;
