/**
 * <AUTHOR>
 * @namespace Recent_Components_RecentCard
 * @description Document Card
 */

import { useOldDocument } from "@/common/transfer/hooks/use-document";
import { RECENT_TYPE, RecentItem } from "@imbricate-hummingbird/configuration";
import { $LOADING, HookSymbol } from "@imbricate-hummingbird/global-symbol";
import { TransferDocument, TransferDocumentProperty, findPrimaryTransferDocumentProperty } from "@imbricate-hummingbird/transfer-core";
import { UICardHeader } from "@imbricate-hummingbird/ui";
import { IImbricateDatabase } from "@imbricate/core";
import React, { FC } from "react";
import { FaDatabase } from "react-icons/fa";
import { IoIosDocument } from "react-icons/io";
import { UseDatabaseResponseSymbol, useDatabase } from "../../../database/hooks/use-database";
import { createGetDocumentForRecentAction } from "../../actions/get-document";
import { RecentCardLoading } from "./loading-card";
import { RecentCardUnknown } from "./unknown-card";

export type RecentCardDocumentProps = {

    readonly item: RecentItem<RECENT_TYPE.DOCUMENT>;
};

export const RecentCardDocument: FC<RecentCardDocumentProps> = (
    props: RecentCardDocumentProps,
) => {

    const document: TransferDocument | HookSymbol =
        useOldDocument(
            props.item.payload.originUniqueIdentifier,
            props.item.payload.databaseUniqueIdentifier,
            props.item.payload.documentUniqueIdentifier,
            createGetDocumentForRecentAction(
                props.item.payload.originUniqueIdentifier,
                props.item.payload.databaseUniqueIdentifier,
                props.item.payload.documentUniqueIdentifier,
                {
                    executer: import.meta.url,
                },
            ),
        );

    const database: IImbricateDatabase | UseDatabaseResponseSymbol =
        useDatabase(props.item.payload.databaseUniqueIdentifier);

    if (typeof database === "symbol" ||
        document === $LOADING
    ) {

        return (<RecentCardLoading />);
    }

    if (typeof document === "symbol") {

        return (<RecentCardUnknown
            item={props.item}
        />);
    }

    const properties = document.properties;

    if (typeof properties === "symbol") {
        return null;
    }

    const primaryProperty: TransferDocumentProperty | null =
        findPrimaryTransferDocumentProperty(
            database.schema,
            document.properties,
        );

    const primaryValueText: string = primaryProperty
        ? String(primaryProperty.propertyValue)
        : document.documentUniqueIdentifier;

    return (<React.Fragment>
        <UICardHeader
            className="flex flex-row items-center gap-3"
        >
            <IoIosDocument
                className="text-2xl"
            />
            <div
                className="flex-1 flex flex-col items-start text-left"
            >
                <div>
                    {primaryValueText}
                </div>
                <div
                    className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-1"
                >
                    <FaDatabase /> {database.databaseName}
                </div>
            </div>
            <div
                className="self-end text-xs text-gray-600 dark:text-gray-400"
            >
                {new Date(props.item.accessedAt).toLocaleString()}
            </div>
        </UICardHeader>
    </React.Fragment>);
};
