/**
 * <AUTHOR>
 * @package React Store
 * @namespace Lens
 * @description Feature
 */

import { LensConfig } from "@imbricate-hummingbird/configuration";
import { createSlice } from "@reduxjs/toolkit";
import { useSelector } from "react-redux";

export type LensSliceState = {

    readonly lensConfig: LensConfig;
};

export type LensSliceReducers = {

    readonly setLensConfig: (state: LensSliceState, action: {
        payload: LensConfig,
    }) => LensSliceState;
};

export const LensSlice = createSlice<
    LensSliceState,
    LensSliceReducers,
    "lens",
    any
>({
    name: "lens",
    initialState: {
        lensConfig: {
            items: [],
        },
    },
    reducers: {
        setLensConfig: (state: LensSliceState, action: {
            payload: LensConfig,
        }) => {
            return {
                ...state,
                lensConfig: action.payload,
            };
        },
    },
});

export const useLensSlice = (): LensSliceState => {

    const lensState: LensSliceState = useSelector((state: any) => {
        return state.lens;
    });

    return lensState;
};
