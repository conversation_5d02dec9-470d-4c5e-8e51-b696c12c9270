/**
 * <AUTHOR>
 * @package UI
 * @namespace ScrollShadow
 * @description Scroll Shadow
 */

import { ScrollShadow } from "@heroui/scroll-shadow";
import clsx from "clsx";

export type UIScrollShadowProps = {

    readonly className?: string;

    readonly overflowYAuto?: boolean;
    readonly overflowXAuto?: boolean;

    readonly children?: React.ReactNode;
};

export const UIScrollShadow: React.FC<UIScrollShadowProps> = (
    props: UIScrollShadowProps,
) => {

    return (<ScrollShadow
        className={clsx(
            props.overflowYAuto && "overflow-y-auto",
            props.overflowXAuto && "overflow-x-auto",
            props.className,
        )}
    >
        {props.children}
    </ScrollShadow>);
};
