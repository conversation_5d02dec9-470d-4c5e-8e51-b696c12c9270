/**
 * <AUTHOR>
 * @namespace Property_Draft
 * @description Merge Drafts
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { IMBRICATE_PROPERTY_TYPE, ImbricatePropertyKey } from "@imbricate/core";

export const mergePropertyDrafts = (
    originalDrafts: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[],
    newDrafts: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[],
    deletedDrafts: ImbricatePropertyKey[],
): TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[] => {

    const toBeEdited: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[] = [
        ...originalDrafts,
    ];

    const newDraftsMap: Map<string, TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>> = new Map();
    const toBeAdded: Set<string> = new Set();
    for (const draft of newDrafts) {
        newDraftsMap.set(draft.key, draft);

        if (deletedDrafts.includes(draft.key)) {
            continue;
        }

        toBeAdded.add(draft.key);
    }

    const updatedDrafts = toBeEdited
        .filter((draft) => !deletedDrafts.includes(draft.key))
        .map((draft) => {

            if (newDraftsMap.has(draft.key)) {
                return newDraftsMap.get(draft.key)!;
            }
            return draft;
        });

    for (const toBeAddedKey of toBeAdded) {
        updatedDrafts.push(newDraftsMap.get(toBeAddedKey)!);
    }

    return updatedDrafts;
};
