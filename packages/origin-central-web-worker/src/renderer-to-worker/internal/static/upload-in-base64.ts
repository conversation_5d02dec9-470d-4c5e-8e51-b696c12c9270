/**
 * <AUTHOR>
 * @namespace WorkerO<PERSON><PERSON>_Renderer_To_Worker_Internal_Static
 * @description Upload Static In Base64
 */
// ONLY RUN IN WORKER

import { ActionDescription } from "@imbricate-hummingbird/interceptor-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { IMBRICATE_STATIC_MIME_TYPE, ImbricateStaticManagerCreateStaticOutcome } from "@imbricate/core";
import { navigateSubRecordIdentifier, resolveSubRecordIdentifier } from "../util/subRecord";

const createUploadStaticInBase64Action = (
    originUniqueIdentifier: string,
    base64String: string,
    mimeType: IMBRICATE_STATIC_MIME_TYPE,
): ActionDescription => {

    return {
        actionName: "Upload Static In Base64",
        actionDescription: `Upload static in base64 in origin ${originUniqueIdentifier}, with size of ${base64String.length}`,
        actionPayload: {
            originUniqueIdentifier,
            base64SLength: base64String.length,
            mimeType,
        },
        executerMetadata: {
            executer: import.meta.url,
        },
    };
};

export const workerInternalExecuteUploadStaticInBase64 = async (
    origin: ImbricateOriginObject,
    base64String: string,
    mimeType: IMBRICATE_STATIC_MIME_TYPE,
    actionIdentifier: string,
    recordIdentifier: string,
    isSubOperation: boolean,
): Promise<ImbricateStaticManagerCreateStaticOutcome> => {


    const fixedRecordIdentifier = await navigateSubRecordIdentifier(
        actionIdentifier,
        recordIdentifier,
        isSubOperation,
        createUploadStaticInBase64Action(
            origin.origin.uniqueIdentifier,
            base64String,
            mimeType,
        ),
    );

    const result = await origin.origin.getStaticManager().createInBase64(
        base64String,
        mimeType,
    );

    await resolveSubRecordIdentifier(
        fixedRecordIdentifier,
        isSubOperation,
    );

    return result;
};