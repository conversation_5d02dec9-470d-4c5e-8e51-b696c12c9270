/**
 * <AUTHOR>
 * @namespace Database_Utils
 * @description Find Databases
 */

import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { IMBRICATE_ORIGIN_FEATURE, checkImbricateOriginFeatureSupported } from "@imbricate/core";
import { OldDataCentral } from "../../data/old-data-central";
import { createFindDatabasesAction } from "../actions/find-databases";
import { ImbricateDatabasesObject } from "../hooks/use-databases";

export const findDatabases = async (
    origins: ImbricateOriginObject[],
    originUniqueIdentifier?: string,
): Promise<ImbricateDatabasesObject[]> => {

    const response: ImbricateDatabasesObject[] = [];

    const targetOrigins: ImbricateOriginObject[] = originUniqueIdentifier
        ? origins.filter((
            origin: ImbricateOriginObject,
        ) => origin.origin.uniqueIdentifier === originUniqueIdentifier)
        : origins;

    for (const origin of targetOrigins) {

        const originSupported: boolean = checkImbricateOriginFeatureSupported(
            origin.origin.supportedFeatures,
            IMBRICATE_ORIGIN_FEATURE.ORIGIN_DATABASE_MANAGER,
        );

        if (!originSupported) {
            continue;
        }

        const databases = await ActionCentral.getInstance().executeAction(
            async (
                actionIdentifier: string,
                recordIdentifier: string,
            ) => {

                return await OldDataCentral.getInstance().getDatabases(
                    origin.origin.uniqueIdentifier,
                    actionIdentifier,
                    recordIdentifier,
                );
            },
            createFindDatabasesAction(
                origin.origin.uniqueIdentifier,
                {
                    executer: import.meta.url,
                },
            ),
        );

        if (typeof databases === "symbol") {
            continue;
        }

        for (const database of databases.databases) {

            response.push({
                originUniqueIdentifier: origin.origin.uniqueIdentifier,
                originName: origin.originName,
                database,
            });
        }
    }

    return response;
};
