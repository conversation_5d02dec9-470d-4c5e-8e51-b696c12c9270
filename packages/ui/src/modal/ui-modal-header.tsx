/**
 * <AUTHOR>
 * @package UI
 * @namespace Modal
 * @description Modal Header
 */

import { ModalHeader } from "@heroui/modal";
import type { FC, ReactNode } from "react";

export type UIModalHeaderProps = {

    readonly className?: string;

    readonly children?: ReactNode;
};

export const UIModalHeader: FC<UIModalHeaderProps> = (
    props: UIModalHeaderProps,
) => {

    return (<ModalHeader
        className={props.className}
    >
        {props.children}
    </ModalHeader>);
};
