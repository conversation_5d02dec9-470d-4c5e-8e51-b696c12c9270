/**
 * <AUTHOR>
 * @namespace Origin
 * @description Origin Storage
 */

import { HUMMINGBIRD_MANAGED_CONFIG_ITEM, ManagedConfigController } from "@imbricate-hummingbird/configuration";
import { OriginStorageInstance } from "@imbricate-hummingbird/origin-central";

export const putOriginStorageInstance = (instance: OriginStorageInstance): void => {

    ManagedConfigController.getInstance()
        .setManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.ORIGIN_CONFIG, instance);
};

export const getOriginStorageInstance = (): OriginStorageInstance => {

    return ManagedConfigController.getInstance()
        .getManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.ORIGIN_CONFIG);
};

export const clearOriginStorageInstance = (): void => {

    ManagedConfigController.getInstance()
        .deleteManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.ORIGIN_CONFIG);
};
