/**
 * <AUTHOR>
 * @namespace Drawer_Component_Wrappers
 * @description Document View Wrapper
 */

import { useWideOrigin } from "@/main/origin/hooks/use-wide-origin";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const DrawerDocumentView = LazyLoadComponent(
    () => import("../components/document-view"),
    "Drawer Document View",
);

export type DrawerDocumentViewWrapperProps = {

    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;
};

export const DrawerDocumentViewWrapper: FC<DrawerDocumentViewWrapperProps> = (
    props: DrawerDocumentViewWrapperProps,
) => {

    const origin: ImbricateOriginObject | null =
        useWideOrigin(props.databaseUniqueIdentifier);

    if (origin === null) {
        return null;
    }

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Drawer Document View"
            label={["document"]}
            fullHeight
        />}
    >
        <DrawerDocumentView
            origin={origin}
            {...props}
        />
    </React.Suspense>);
};
