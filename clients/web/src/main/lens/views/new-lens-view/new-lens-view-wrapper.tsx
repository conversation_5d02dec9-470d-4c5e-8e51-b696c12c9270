/**
 * <AUTHOR>
 * @namespace Lens
 * @description New Lens View Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const NewLensViewApplication = LazyLoadComponent(
    () => import("./new-lens-view"),
    "New Lens View Application",
);

export const NewLensViewWrapper: FC = () => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="New Lens View Application"
            fullHeight
        />}
    >
        <NewLensViewApplication />
    </React.Suspense>);
};
