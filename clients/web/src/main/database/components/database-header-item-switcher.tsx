/**
 * <AUTHOR>
 * @namespace Database_Components_Header_Item_Switcher
 * @description Item Switcher
 */

import { UIButtonGroup } from "@imbricate-hummingbird/ui";
import { ImbricateDatabaseSchema } from "@imbricate/core";
import { FC } from "react";
import { DatabaseHeaderItemDisplayedPropertiesSwitch } from "./header/displayed-properties-switch";
import { DatabaseHeaderItemItemsPerPageSwitch } from "./header/items-per-page-switch";

export type DatabaseHeaderItemSwitcherProps = {

    readonly itemsPerPage: number;
    readonly onItemsPerPageChange: (itemsPerPage: number) => void;

    readonly databaseSchema: ImbricateDatabaseSchema;
    readonly displayedProperties: string[];
    readonly onDisplayedPropertiesChange: (displayedProperties: string[]) => void;
};

export const DatabaseHeaderItemSwitcher: FC<DatabaseHeaderItemSwitcherProps> = (
    props: DatabaseHeaderItemSwitcherProps,
) => {

    return (<UIButtonGroup>
        <DatabaseHeaderItemItemsPerPageSwitch
            itemsPerPage={props.itemsPerPage}
            onItemsPerPageChange={props.onItemsPerPageChange}
        />
        <DatabaseHeaderItemDisplayedPropertiesSwitch
            databaseSchema={props.databaseSchema}
            displayedProperties={props.displayedProperties}
            onDisplayedPropertiesChange={props.onDisplayedPropertiesChange}
        />
    </UIButtonGroup>);
};
