/**
 * <AUTHOR>
 * @namespace Common_Components
 * @description Markdown Render
 */

import { LoadingWrapper } from "@imbricate-hummingbird/react-components";
import { transformMarkdown, useHighlightStyle, useMarkdownStyle, useMermaid } from "@imbricate-hummingbird/react-markdown";
import { useDarkMode } from "@imbricate-hummingbird/theme-core";
import React, { FC } from "react";

export type MarkdownRenderProps = {

    readonly rawText: string;
};

export const MarkdownRender: FC<MarkdownRenderProps> = (
    props: MarkdownRenderProps,
) => {

    const darkMode = useDarkMode();
    const [rendered, setRendered] = React.useState<string | null>(null);

    useMermaid(() => typeof rendered === "string", [rendered]);
    useMarkdownStyle(() => typeof rendered === "string", darkMode, [rendered]);
    useHighlightStyle({
        darkMode,
    });

    React.useEffect(() => {

        const execute = async () => {

            const markdown = await transformMarkdown(props.rawText);
            setRendered(markdown);
        };

        execute();
    }, [props.rawText]);

    if (!rendered) {
        return (<LoadingWrapper
            debugDescription="Markdown Render"
            fullHeight
        />);
    }

    return (<div
        dangerouslySetInnerHTML={{
            __html: rendered,
        }}
        className="markdown-body w-full h-full"
    />);
};
