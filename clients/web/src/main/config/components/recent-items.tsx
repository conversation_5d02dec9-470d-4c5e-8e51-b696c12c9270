/**
 * <AUTHOR>
 * @namespace Config_Components
 * @description Recent Items Card
 */

import { HUMMINGBIRD_PREFERENCE_ITEM, usePreferenceItem } from "@imbricate-hummingbird/configuration";
import { FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UICard<PERSON>ody, UICardHeader, UIDivider, UIDropdown, UISpacer, create<PERSON><PERSON>utton } from "@imbricate-hummingbird/ui";
import { FaChevronDown } from "react-icons/fa6";
import { MdViewTimeline } from "react-icons/md";
import { useConfigFormat } from "../internationalization/hook";
import { CONFIG_PROFILE } from "../internationalization/profile";

export const ConfigRecentItemsCard = () => {

    const configFormat = useConfigFormat();

    const recentItem = usePreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.APPLICATION_USAGE_HISTORY_RECENT_ITEMS_MAXIMUM_LENGTH,
    );

    return (<StyledCard>
        <UICardHeader>
            <MdViewTimeline />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.RECENT_ITEMS)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-2"
        >
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                    placeholderClassName="h-4 rounded-md"
                >
                    {configFormat.get(CONFIG_PROFILE.RECENT_ITEMS_MAXIMUM_LENGTH)}
                </FormattedText>
            </div>
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                    className="text-sm text-gray-500"
                >
                    {configFormat.get(CONFIG_PROFILE.RECENT_ITEMS_MAXIMUM_LENGTH_DESCRIPTION)}
                </FormattedText>
            </div>
            <UIDropdown
                trigger={createUIButton({
                    endContent: (<FaChevronDown />),
                    children: (<FormattedText
                        usedFormats={[
                            configFormat,
                        ]}
                    >
                        {configFormat.get(CONFIG_PROFILE.DISPLAY_RECENT_$1_ITEMS, recentItem.preference)}
                    </FormattedText>),
                })}
                selectionMode="single"
                categories={[
                    {
                        categoryKey: "recent-items",
                        items: [8, 12, 16, 20, 24, 32].map((item) => {

                            return {
                                itemKey: String(item),
                                textValue: String(item),
                                isSelected: item === recentItem.preference,
                                content: (<FormattedText
                                    usedFormats={[
                                        configFormat,
                                    ]}
                                >
                                    {configFormat.get(CONFIG_PROFILE.DISPLAY_RECENT_$1_ITEMS, item)}
                                </FormattedText>),
                                onPress: () => {
                                    recentItem.updatePreference(item);
                                },
                            };
                        }),
                    },
                ]}
            />
        </UICardBody>
    </StyledCard>);
};
