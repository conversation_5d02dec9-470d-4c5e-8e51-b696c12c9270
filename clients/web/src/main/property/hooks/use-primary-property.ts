/**
 * <AUTHOR>
 * @namespace Property_Hooks
 * @description Use Primary Property
 */

import { IImbricateDatabase, IImbricateDocument, IImbricateProperty, IMBRICATE_PROPERTY_TYPE, findPrimaryProperty } from "@imbricate/core";
import { S_UseDatabaseErrored, S_UseDatabaseLoading, S_UseDatabaseNotFound, UseDatabaseResponseSymbol, useDatabase } from "../../database/hooks/use-database";
import { S_UseDocumentErrored, S_UseDocumentLoading, S_UseDocumentNotFound, UseDocumentResponseSymbol, useOldDocument } from "../../document/hooks/use-document";

export const S_UsePrimaryPropertyLoading: unique symbol = Symbol("UsePrimaryPropertyLoading");
export const S_UsePrimaryPropertyNotFound: unique symbol = Symbol("UsePrimaryPropertyNotFound");
export const S_UsePrimaryPropertyErrored: unique symbol = Symbol("UsePrimaryPropertyErrored");

export type UsePrimaryPropertyResponseSymbol =
    | typeof S_UsePrimaryPropertyLoading
    | typeof S_UsePrimaryPropertyNotFound
    | typeof S_UsePrimaryPropertyErrored;

export const usePrimaryProperty = (
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
): IImbricateProperty<IMBRICATE_PROPERTY_TYPE> | UsePrimaryPropertyResponseSymbol => {

    const document: IImbricateDocument | UseDocumentResponseSymbol =
        useOldDocument(
            databaseUniqueIdentifier,
            documentUniqueIdentifier,
        );

    const database: IImbricateDatabase | UseDatabaseResponseSymbol =
        useDatabase(databaseUniqueIdentifier);

    if (document === S_UseDocumentLoading || database === S_UseDatabaseLoading) {
        return S_UsePrimaryPropertyLoading;
    }

    if (document === S_UseDocumentNotFound || database === S_UseDatabaseNotFound) {
        return S_UsePrimaryPropertyNotFound;
    }

    if (document === S_UseDocumentErrored || database === S_UseDatabaseErrored) {
        return S_UsePrimaryPropertyErrored;
    }

    const properties = document.getProperties();

    if (typeof properties === "symbol") {
        return S_UsePrimaryPropertyNotFound;
    }

    const primaryProperty = findPrimaryProperty(
        database.schema,
        properties.properties,
    );

    if (!primaryProperty) {
        return S_UsePrimaryPropertyNotFound;
    }

    return primaryProperty;
};
