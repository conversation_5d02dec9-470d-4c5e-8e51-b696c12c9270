/**
 * <AUTHOR>
 * @package React Components
 * @namespace FormattedRadioGroup
 * @description Formatted Radio Group
 */

import { UIRadioGroup, UIRadioGroupItem, UIRadioGroupProps } from "@imbricate-hummingbird/ui";
import { SudooFormat } from "@sudoo/internationalization";
import { FormattedText } from "../formatted-text/formatted-text";
import { FormattedRadioGroupItem } from "./types";

export type FormattedRadioGroupProps<PROFILE extends string> = {

    readonly format: SudooFormat<PROFILE>

    readonly items: FormattedRadioGroupItem<PROFILE>[];
} & Omit<UIRadioGroupProps, "items">;

export const FormattedRadioGroup = <PROFILE extends string,>(
    props: FormattedRadioGroupProps<PROFILE>,
) => {

    return (<UIRadioGroup
        {...props}
        items={props.items.map((item: FormattedRadioGroupItem<PROFILE>) => {

            return ({
                ...item,
                itemContent: (<FormattedText
                    usedFormats={[props.format]}
                >
                    {props.format.get(item.itemProfileItem)}
                </FormattedText>),
            } as UIRadioGroupItem);
        })}
    />);
};
