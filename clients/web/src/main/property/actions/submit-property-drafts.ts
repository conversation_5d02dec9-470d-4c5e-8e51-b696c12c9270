/**
 * <AUTHOR>
 * @namespace Property_Actions
 * @description Submit Property Drafts
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createSubmitPropertyDraftsAction = (
    actionPayload: any,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Submit Property Drafts",
        actionDescription: "Submit the property drafts",
        actionPayload,
        executerMetadata: metadata,
    };
};
