/**
 * <AUTHOR>
 * @package React Store
 * @namespace Drawer
 * @description Feature
 */

import { createSlice } from "@reduxjs/toolkit";
import { useSelector } from "react-redux";
import { DRAWER_TYPE, DrawerStatus } from "./types/status";

export type DrawerSliceState = {

    readonly drawerOpen: boolean;
    readonly drawerStatuses: DrawerStatus<DRAWER_TYPE>[];
    readonly currentDrawerStatusIndex: number | null;
};

export type DrawerSliceReducers = {

    readonly openDrawer: (
        state: DrawerSliceState,
        action: {
            readonly payload: DrawerStatus<DRAWER_TYPE>;
        },
    ) => DrawerSliceState;
    readonly closeDrawer: (state: DrawerSliceState) => DrawerSliceState;

    readonly previousDrawer: (state: DrawerSliceState) => DrawerSliceState;
    readonly nextDrawer: (state: DrawerSliceState) => DrawerSliceState;
};

export const DrawerSlice = createSlice<
    DrawerSliceState,
    DrawerSliceReducers,
    "drawer",
    any
>({
    name: "drawer",
    initialState: {
        drawerOpen: false,
        drawerStatuses: [],
        currentDrawerStatusIndex: null,
    },
    reducers: {
        openDrawer: (state: DrawerSliceState, action: {
            readonly payload: DrawerStatus<DRAWER_TYPE>;
        }) => {

            const newStatuses = [
                ...state.drawerStatuses.slice(
                    0,
                    typeof state.currentDrawerStatusIndex === "number"
                        ? state.currentDrawerStatusIndex + 1
                        : 0,
                ),
                action.payload,
            ];

            return {
                ...state,
                drawerOpen: true,
                drawerStatuses: newStatuses,
                currentDrawerStatusIndex: newStatuses.length - 1,
            };
        },
        closeDrawer: (state: DrawerSliceState) => {
            return {
                ...state,
                drawerOpen: false,
                drawerStatuses: [],
                currentDrawerStatusIndex: null,
            };
        },
        previousDrawer: (state: DrawerSliceState) => {
            return {
                ...state,
                currentDrawerStatusIndex: state.currentDrawerStatusIndex! - 1,
            };
        },
        nextDrawer: (state: DrawerSliceState) => {
            return {
                ...state,
                currentDrawerStatusIndex: state.currentDrawerStatusIndex! + 1,
            };
        },
    },
});

export const useDrawerSlice = (): DrawerSliceState => {

    const drawerState: DrawerSliceState = useSelector((state: any) => {
        return state.drawer;
    });

    return drawerState;
};
