/**
 * <AUTHOR>
 * @namespace Main_Config_Components_Editor_Monaco_MinimapCard
 * @description Minimap Card
 */

import { useConfigFormat } from "@/main/config/internationalization/hook";
import { CONFIG_PROFILE } from "@/main/config/internationalization/profile";
import { HUMMINGBIRD_EDITOR_MONACO_SCROLLBAR_MINIMAP_SIZE, HUMMINGBIRD_PREFERENCE_ITEM, PreferenceController } from "@imbricate-hummingbird/configuration";
import { FormattedRadioGroup, FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody, UICardHeader, UIDivider, UISpacer, UISwitch } from "@imbricate-hummingbird/ui";
import { FC, useState } from "react";
import { FaMap } from "react-icons/fa6";

export type ConfigEditorMonacoMinimapCardProps = {
};

export const ConfigEditorMonacoMinimapCard: FC<ConfigEditorMonacoMinimapCardProps> = (
    _props: ConfigEditorMonacoMinimapCardProps,
) => {

    const [minimapEnablement, setMinimapEnablement] = useState(() => {

        return PreferenceController.getPreference(
            HUMMINGBIRD_PREFERENCE_ITEM.EDITOR_MONACO_SCROLLBAR_MINIMAP_ENABLEMENT,
        );
    });

    const [minimapSize, setMinimapSize] = useState(() => {

        return PreferenceController.getPreference(
            HUMMINGBIRD_PREFERENCE_ITEM.EDITOR_MONACO_SCROLLBAR_MINIMAP_SIZE,
        );
    });

    const configFormat = useConfigFormat();

    return (<StyledCard>
        <UICardHeader>
            <FaMap />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.SCROLLBAR)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody>
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                >
                    {configFormat.get(CONFIG_PROFILE.USE_MINIMAP_AS_SCROLLBAR_DESCRIPTION)}
                </FormattedText>
            </div>
            <UISwitch
                aria-label="Minimap Enablement"
                isSelected={minimapEnablement}
                onSelectedChange={(newSelected: boolean) => {

                    setMinimapEnablement(newSelected);

                    PreferenceController.setPreference(
                        HUMMINGBIRD_PREFERENCE_ITEM.EDITOR_MONACO_SCROLLBAR_MINIMAP_ENABLEMENT,
                        newSelected,
                    );
                }}
            >
                {configFormat.get(CONFIG_PROFILE.USE_MINIMAP_AS_SCROLLBAR)}
            </UISwitch>
        </UICardBody>
        <UIDivider />
        <UICardBody>
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                >
                    {configFormat.get(CONFIG_PROFILE.MINIMAP_SIZE_DESCRIPTION)}
                </FormattedText>
            </div>
            <FormattedRadioGroup
                label={configFormat.get(CONFIG_PROFILE.MINIMAP_SIZE)}
                selected={minimapSize}
                onSelectedChange={(newSelected: string) => {

                    setMinimapSize(newSelected as HUMMINGBIRD_EDITOR_MONACO_SCROLLBAR_MINIMAP_SIZE);

                    PreferenceController.setPreference(
                        HUMMINGBIRD_PREFERENCE_ITEM.EDITOR_MONACO_SCROLLBAR_MINIMAP_SIZE,
                        newSelected as HUMMINGBIRD_EDITOR_MONACO_SCROLLBAR_MINIMAP_SIZE,
                    );
                }}
                format={configFormat}
                items={[
                    {
                        itemKey: "proportional",
                        itemProfileItem: CONFIG_PROFILE.PROPORTIONAL,
                    },
                    {
                        itemKey: "fit",
                        itemProfileItem: CONFIG_PROFILE.FIT,
                    },
                    {
                        itemKey: "fill",
                        itemProfileItem: CONFIG_PROFILE.FILL,
                    },
                ]}
            />
        </UICardBody>
    </StyledCard>);
};
