/**
 * <AUTHOR>
 * @namespace Config_Components_Guidance
 * @description Reset DismissedGuidance
 */

import { DismissedGuidanceController } from "@imbricate-hummingbird/configuration";
import { FormattedText, StyledCard, openSuccessToaster } from "@imbricate-hummingbird/react-components";
import { UIButton, UICardBody, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { FaBookDead } from "react-icons/fa";
import { TiDocumentDelete } from "react-icons/ti";
import { useConfigFormat } from "../../internationalization/hook";
import { CONFIG_PROFILE } from "../../internationalization/profile";

export const ConfigGuidanceResetDismissedGuidanceCard = () => {

    const configFormat = useConfigFormat();

    return (<StyledCard>
        <UICardHeader>
            <FaBookDead />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.RESET_TUTORIAL)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-2"
        >
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                    placeholderClassName="h-4 rounded-md"
                >
                    {configFormat.get(CONFIG_PROFILE.RESET_TUTORIAL_DESCRIPTION)}
                </FormattedText>
            </div>
            <UIButton
                radius="sm"
                variant="flat"
                color="danger"
                startContent={<TiDocumentDelete />}
                onPress={() => {

                    DismissedGuidanceController.getInstance()
                        .resetDismissedGuidance();

                    openSuccessToaster(
                        configFormat.get(CONFIG_PROFILE.RESET_TUTORIAL_SUCCESS),
                    );
                }}
            >
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                >
                    {configFormat.get(CONFIG_PROFILE.RESET_TUTORIAL_ACTION)}
                </FormattedText>
            </UIButton>
        </UICardBody>
    </StyledCard>);
};
