/**
 * <AUTHOR>
 * @namespace Main_Config_Hooks_UseConfigRoute
 * @description Use Config Route
 */

import { NavigateOptions, useNavigate } from "react-router-dom";
import { getConfigEditorMonacoRoute, getConfigImbricateScriptOutlookRoute } from "../util/config-route";

export const useNavigateConfigEditorMonacoRoute = (): (
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        options?: NavigateOptions,
    ) => navigate(getConfigEditorMonacoRoute(), options);
};

export const useNavigateConfigImbricateScriptOutlookRoute = (): (
    options?: NavigateOptions,
) => void => {

    const navigate = useNavigate();

    return (
        options?: NavigateOptions,
    ) => navigate(getConfigImbricateScriptOutlookRoute(), options);
};
