/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyDisplay
 * @description Binary Display
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIButton } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { useDocumentFormat } from "../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../internationalization/profile";

export type DocumentBinaryDisplayProps = {

    readonly originUniqueIdentifier: string;

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY>;
};

export const DocumentBinaryDisplay: FC<DocumentBinaryDisplayProps> = (
    props: DocumentBinaryDisplayProps,
) => {

    const documentFormat = useDocumentFormat();

    const fileAlreadyExists = props.property.value?.length > 0;

    if (fileAlreadyExists) {

        return (<div
            className="flex gap-2 flex-wrap items-center"
        >
            <UIButton
                size="sm"
                variant="ghost"
                onPress={() => {
                }}
            >
                Download Binary
            </UIButton>
            <div
                className="text-xs text-gray-600 dark:text-gray-400"
            >
                {props.property.value}
            </div>
        </div>);
    }

    return (<div>
        {documentFormat.get(DOCUMENT_PROFILE.NO_DATA)}
    </div>);
};
