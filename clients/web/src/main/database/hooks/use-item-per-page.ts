/**
 * <AUTHOR>
 * @namespace Main_Database_Hooks_UseItemPerPage
 * @description Use Item Per Page
 */

import { HUMMINGBIRD_MANAGED_CONFIG_ITEM, HummingbirdManagedConfigDatabaseViewItemPerPage, ManagedConfigController } from "@imbricate-hummingbird/configuration";
import { useCallback, useState } from "react";

export type UseItemPerPageResult = {

    readonly itemPerPage: number;
    readonly setItemPerPage: (itemPerPage: number) => void;
}

export const useItemPerPage = (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
): UseItemPerPageResult => {

    const [itemPerPage, setItemPerPage] = useState<number>(() => {

        const config: HummingbirdManagedConfigDatabaseViewItemPerPage[] =
            ManagedConfigController.getManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.DATABASE_VIEW_ITEM_PER_PAGE);

        const targetConfig: HummingbirdManagedConfigDatabaseViewItemPerPage | undefined =
            config.find((config: HummingbirdManagedConfigDatabaseViewItemPerPage) => {
                return config.originUniqueIdentifier === originUniqueIdentifier
                    && config.databaseUniqueIdentifier === databaseUniqueIdentifier;
            });

        if (!targetConfig) {
            return 25;
        }

        return targetConfig.itemPerPage;
    });

    const setItemPerPageFunction = useCallback((
        itemPerPage: number,
    ) => {

        setItemPerPage(itemPerPage);
        ManagedConfigController.updateManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.DATABASE_VIEW_ITEM_PER_PAGE, (
            current: HummingbirdManagedConfigDatabaseViewItemPerPage[],
        ) => {

            let updated: boolean = false;
            const updatedConfigs: HummingbirdManagedConfigDatabaseViewItemPerPage[] = current.map((config: HummingbirdManagedConfigDatabaseViewItemPerPage) => {

                if (config.originUniqueIdentifier === originUniqueIdentifier
                    && config.databaseUniqueIdentifier === databaseUniqueIdentifier) {

                    updated = true;

                    return {
                        ...config,
                        itemPerPage,
                    };
                }

                return config;
            });

            if (updated) {
                return updatedConfigs;
            }

            return [
                ...current,
                {
                    originUniqueIdentifier,
                    databaseUniqueIdentifier,
                    itemPerPage,
                },
            ];
        });
    }, [originUniqueIdentifier, databaseUniqueIdentifier]);

    return {
        itemPerPage,
        setItemPerPage: setItemPerPageFunction,
    };
};
