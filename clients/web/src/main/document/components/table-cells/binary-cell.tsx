/**
 * <AUTHOR>
 * @namespace Document_Components_Table_Cells
 * @description Binary Cell
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIButton, UIPopover, createUIButton } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaPropertyOptionsBinary } from "@imbricate/core";
import { FC } from "react";
import { rootLogger } from "../../../log/logger";
import { useDocumentFormat } from "../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../internationalization/profile";
import { DocumentBinaryValueBinaryPopover } from "../property-value/binary/binary-popover";
import { DocumentBinaryValueSelectedReference } from "../property-value/binary/selected-reference";
import { DocumentTableCellContent } from "./cell-content";

const logger = rootLogger.fork({
    scopes: [
        "Document",
        "Components",
        "TableCells",
        "BinaryCell",
    ],
});

export type DocumentTableBinaryCellProps = {

    readonly originUniqueIdentifier: string;

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY>;
    readonly getEditingProperty: () => TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY> | undefined;
    readonly updateEditingProperty: (
        value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY>,
    ) => void;
    readonly editing: boolean;

    readonly options: ImbricateDatabaseSchemaPropertyOptionsBinary;
};

export const DocumentTableBinaryCell: FC<DocumentTableBinaryCellProps> = (
    props: DocumentTableBinaryCellProps,
) => {

    const documentFormat = useDocumentFormat();

    const renderChip = (
        propertyDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY> | undefined,
    ) => {

        const value: string[] = propertyDraft?.value ?? [];

        if (!Array.isArray(value)) {
            return null;
        }

        const fixedValue: string[] =
            Array.isArray(value)
                ? value
                : [];

        if (fixedValue.length > 0) {

            return (<UIPopover
                trigger={createUIButton({
                    color: "primary",
                    size: "sm",
                    variant: "flat",
                    children: (fixedValue.length > 1
                        ? documentFormat.get(
                            DOCUMENT_PROFILE.$1_BINARIES,
                            fixedValue.length,
                        )
                        : documentFormat.get(
                            DOCUMENT_PROFILE.ONE_BINARY,
                        )),
                })}
                contentClassName="p-3 flex flex-col gap-1"
            >
                {fixedValue.map((item: string) => {

                    return (<DocumentBinaryValueSelectedReference
                        key={item}
                        size="sm"
                        radius="sm"
                        originUniqueIdentifier={props.originUniqueIdentifier}
                        reference={item}
                        onDelete={props.editing ? () => {

                            logger.debug("Delete Binary", item);

                            const newValue: string[] =
                                fixedValue.filter((value) => {
                                    return value !== item;
                                });

                            const fixedPropertyValue: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY> = {
                                key: props.property.key,
                                type: props.property.type,
                                value: newValue,
                                variant: props.property.variant,
                            };

                            props.updateEditingProperty(fixedPropertyValue);
                        } : void 0}
                    />);
                })}
            </UIPopover>);
        }

        return (<UIButton
            color="warning"
            size="sm"
            variant="flat"
            isDisabled
        >
            {documentFormat.get(
                DOCUMENT_PROFILE.NO_BINARY,
            )}
        </UIButton>);
    };

    if (props.editing) {

        const updatedProperty = props.getEditingProperty();

        if (typeof updatedProperty === "undefined") {
            throw new Error("[Imbricate] Updated property value not found");
        }

        return (<div
            className="flex gap-1 items-center"
        >
            {renderChip(updatedProperty)}
            <DocumentBinaryValueBinaryPopover
                size="sm"
                radius="full"
                iconOnly
                originUniqueIdentifier={props.originUniqueIdentifier}
                propertyKey={props.propertyKey}
                currentProperty={{
                    key: props.propertyKey,
                    type: props.property
                        ? props.property.type
                        : IMBRICATE_PROPERTY_TYPE.BINARY,
                    value: updatedProperty.value,
                    variant: updatedProperty.variant,
                }}
                updateProperty={(newProperty) => {
                    props.updateEditingProperty({
                        ...updatedProperty,
                        value: newProperty.value,
                    });
                }}
                options={props.options}
            />
        </div>);
    }

    return (<DocumentTableCellContent
        schemaType={IMBRICATE_PROPERTY_TYPE.BINARY}
        property={props.property}
        render={(
            propertyDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> | null,
        ) => {

            return renderChip(
                propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY>,
            );
        }}
    />);
};
