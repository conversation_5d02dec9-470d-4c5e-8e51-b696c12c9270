/**
 * <AUTHOR>
 * @namespace Database_Views_SchemaView
 * @description Schema View
 */

import { clearCache } from "@/main/common/cache/cache";
import { DATABASE_CACHE_IDENTIFIER } from "@/main/common/cache/static";
import { useTitleWithAsyncFormat } from "@imbricate-hummingbird/internationalization";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { useNavigateDatabaseDocumentsView } from "@imbricate-hummingbird/react-navigation";
import { UIButton } from "@imbricate-hummingbird/ui";
import { IImbricateDatabase, IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchema, ImbricateDatabaseSchemaProperty } from "@imbricate/core";
import React, { FC } from "react";
import { DatabaseSchemaHeader } from "../../components/database-schema-header";
import { DatabaseSchemaAddPropertyButton } from "../../components/schema/add-property-button";
import { DatabaseSchemaPropertyCard } from "../../components/schema/property-card";
import { useDatabaseSchemaMagicButton } from "../../hooks/use-database-schema-magic-button";
import { useDatabaseFormat } from "../../internationalization/hook";
import { DATABASE_PROFILE } from "../../internationalization/profile";
import { cloneImbricateSchema } from "../../utils/clone-schema";

export type DatabasesSchemaViewProps = {

    readonly origin: ImbricateOriginObject;
    readonly database: IImbricateDatabase;
};

export const DatabasesSchemaView: FC<DatabasesSchemaViewProps> = (
    props: DatabasesSchemaViewProps,
) => {

    const databaseFormat = useDatabaseFormat();

    const [schema, setSchema] =
        React.useState<ImbricateDatabaseSchema>(() => {
            return cloneImbricateSchema(props.database.schema);
        });

    const editedRef = React.useRef(false);

    const navigateToDocuments = useNavigateDatabaseDocumentsView();

    const [saving, setSaving] = React.useState<boolean>(false);

    const magicButtonResult = useDatabaseSchemaMagicButton(
        props.origin.origin.uniqueIdentifier,
        props.database.uniqueIdentifier,
    );

    useTitleWithAsyncFormat(
        databaseFormat,
        () => {
            return [
                props.database.databaseName,
                databaseFormat.get(DATABASE_PROFILE.DATABASE_SCHEMA),
            ];
        },
        [props.database.databaseName],
    );

    const setSchemaMethod = (newSchema: ImbricateDatabaseSchema) => {

        editedRef.current = true;
        setSchema(newSchema);
    };

    return (<div
        className="flex flex-col h-full"
    >
        <div>
            <DatabaseSchemaHeader
                originUniqueIdentifier={props.origin.origin.uniqueIdentifier}
                database={props.database}
                magicButtonResult={magicButtonResult}
            />
        </div>
        <div
            className="flex-1 min-h-0 min-w-0 overflow-auto"
        >
            <div
                className="flex flex-col gap-2 py-2 pr-2"
            >
                {schema.properties.map((
                    property: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>,
                ) => {

                    return (<DatabaseSchemaPropertyCard
                        key={property.propertyIdentifier}
                        property={property}
                        schema={schema}
                        setSchema={setSchemaMethod}
                    />);
                })}
                <DatabaseSchemaAddPropertyButton
                    schema={schema}
                    setSchema={setSchemaMethod}
                />
            </div>
        </div>
        <div
            className="mb-2"
        >
            {editedRef.current && <UIButton
                isDisabled={saving}
                variant="flat"
                color="primary"
                onPress={async () => {

                    setSaving(true);

                    clearCache(DATABASE_CACHE_IDENTIFIER);
                    await props.database.putSchema(schema);

                    navigateToDocuments(
                        props.origin.origin.uniqueIdentifier,
                        props.database.uniqueIdentifier,
                        {
                            replace: true,
                        },
                    );
                }}
            >
                {databaseFormat.get(DATABASE_PROFILE.SAVE)}
            </UIButton>}
        </div>
    </div>);
};
