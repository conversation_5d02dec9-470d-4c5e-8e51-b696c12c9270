/**
 * <AUTHOR>
 * @namespace Document_Actions
 * @description Get Static Uri For Download
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createGetStaticUriForDownloadAction = (
    metadata: ActionDescriptionExecuterMetadata,
    staticUniqueIdentifier: string,
): ActionDescription => {

    return {
        actionName: "Get Static Uri For Download",
        actionDescription: "Get the static object uri for download",
        executerMetadata: metadata,
        actionPayload: {
            staticUniqueIdentifier,
        },
    };
};
