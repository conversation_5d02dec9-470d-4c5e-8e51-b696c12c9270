/**
 * <AUTHOR>
 * @namespace Common_Components_HeaderTitle
 * @description Header Title Secondary
 */

import { FormattedText } from "@imbricate-hummingbird/react-components";
import { SudooFormat } from "@sudoo/internationalization";
import { FC } from "react";
import { Link } from "react-router-dom";
import { HEADER_TITLE_SECONDARY_ITEM_TYPE, HeaderTitleSecondaryItem } from "./types";

export type HeaderTitleSecondaryProps = {

    readonly secondary?: HeaderTitleSecondaryItem;
    readonly formatDependencies?: SudooFormat[];
};

export const HeaderTitleSecondary: FC<HeaderTitleSecondaryProps> = (
    props: HeaderTitleSecondaryProps,
) => {

    if (!props.secondary) {
        return null;
    }

    if (props.secondary.type === HEADER_TITLE_SECONDARY_ITEM_TYPE.LINK) {

        return (<Link
            className="h-2 text-tiny flex gap-1 items-center cursor-pointer"
            to={props.secondary.link}
            replace
        >
            <props.secondary.icon />
            <FormattedText
                usedFormats={props.formatDependencies ?? []}
            >
                {props.secondary.title}
            </FormattedText>
        </Link>);
    }

    return null;
};
