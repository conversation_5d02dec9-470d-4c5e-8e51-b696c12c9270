/**
 * <AUTHOR>
 * @namespace Navigation
 * @description Navigation Dropdown
 */

import { HUMMINGBIRD_NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE } from "@imbricate-hummingbird/configuration";
import { UIDropdown } from "@imbricate-hummingbird/ui";
import { SudooFormat } from "@sudoo/internationalization";
import { FC } from "react";
import { MdOutlineAttachFile } from "react-icons/md";
import { TbWorld } from "react-icons/tb";
import { WiDaySunnyOvercast } from "react-icons/wi";
import { NAVIGATION_PROFILE } from "../internationalization/profile";
import { createNavigationDropdownTrigger } from "./navigation-dropdown-trigger";
import { NAVIGATION_DROPDOWN_KEY } from "./navigation-dropdown/dropdown-key";
import { useExecuteNavigationBottomButtonDropdownAction } from "./navigation-dropdown/use-dropdown-action";

export type NavigationDropdownProps = {

    readonly displayMode: HUMMINGBIRD_NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE;
    readonly navigationFormat: SudooFormat<NAVIGATION_PROFILE>;
};

export const NavigationDropdown: FC<NavigationDropdownProps> = (
    props: NavigationDropdownProps,
) => {

    const executeNavigationBottomButtonDropdownAction = useExecuteNavigationBottomButtonDropdownAction();

    return (<UIDropdown
        placement="top-start"
        trigger={createNavigationDropdownTrigger({
            displayMode: props.displayMode,
            navigationFormat: props.navigationFormat,
        })}
        categories={[
            {
                categoryKey: "navigation",
                title: props.navigationFormat.get(NAVIGATION_PROFILE.MORE),
                items: [
                    {
                        itemKey: NAVIGATION_DROPDOWN_KEY.SURPRISE_ME,
                        description: props.navigationFormat.get(NAVIGATION_PROFILE.SURPRISE_ME_DESCRIPTION),
                        content: props.navigationFormat.get(NAVIGATION_PROFILE.SURPRISE_ME),
                        startContent: (<WiDaySunnyOvercast
                            className="text-large"
                        />),
                        onPress: () => {
                            executeNavigationBottomButtonDropdownAction(NAVIGATION_DROPDOWN_KEY.SURPRISE_ME);
                        },
                    },
                    {
                        itemKey: NAVIGATION_DROPDOWN_KEY.MANAGE_ORIGIN,
                        description: props.navigationFormat.get(NAVIGATION_PROFILE.MANAGE_ORIGIN_DESCRIPTION),
                        content: props.navigationFormat.get(NAVIGATION_PROFILE.MANAGE_ORIGIN),
                        startContent: (<TbWorld
                            className="text-large"
                        />),
                        onPress: () => {
                            executeNavigationBottomButtonDropdownAction(NAVIGATION_DROPDOWN_KEY.MANAGE_ORIGIN);
                        },
                    },
                    {
                        itemKey: NAVIGATION_DROPDOWN_KEY.STATIC_DATA,
                        description: props.navigationFormat.get(NAVIGATION_PROFILE.STATIC_DATA_DESCRIPTION),
                        content: props.navigationFormat.get(NAVIGATION_PROFILE.STATIC_DATA),
                        startContent: (<MdOutlineAttachFile
                            className="text-large"
                        />),
                        onPress: () => {
                            executeNavigationBottomButtonDropdownAction(NAVIGATION_DROPDOWN_KEY.STATIC_DATA);
                        },
                    },
                ],
            },
        ]}
    />);
};
