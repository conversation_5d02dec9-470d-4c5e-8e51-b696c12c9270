/**
 * <AUTHOR>
 * @package UI
 * @namespace Breadcrumbs
 * @description Breadcrumbs
 */

import { BreadcrumbItem, Breadcrumbs } from "@heroui/breadcrumbs";
import React from "react";
import { UIBreadcrumbsItem } from "./types";

export type UIBreadcrumbsProps = {

    readonly className?: string;

    readonly items: UIBreadcrumbsItem[];
};

export const UIBreadcrumbs: React.FC<UIBreadcrumbsProps> = (
    props: UIBreadcrumbsProps,
) => {

    return (
        <Breadcrumbs
            className={props.className}
        >
            {props.items.map((
                item: UIBreadcrumbsItem,
                index: number,
            ) => {

                const hashKey: string = `${item.label}-${index}`;

                return (<BreadcrumbItem
                    key={hashKey}
                    href={item.href}
                >
                    {item.label}
                </BreadcrumbItem>);
            })}
        </Breadcrumbs>
    );
};
