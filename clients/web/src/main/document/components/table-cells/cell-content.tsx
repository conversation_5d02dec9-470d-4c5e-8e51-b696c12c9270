/**
 * <AUTHOR>
 * @namespace Document_Components_TableCells
 * @description Cell Content
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIPopover, createUIButton } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import React, { FC } from "react";
import { IoIosWarning } from "react-icons/io";

export type DocumentTableCellContentProps = {

    readonly schemaType: IMBRICATE_PROPERTY_TYPE;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>;

    readonly render?: (
        value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> | null,
    ) => React.ReactNode;
};

export const DocumentTableCellContent: FC<DocumentTableCellContentProps> = (
    props: DocumentTableCellContentProps,
) => {

    const propertyValue: any = (props.property && typeof props.property.value !== "undefined")
        ? props.property.value
        : null;

    const propsPropertyType: IMBRICATE_PROPERTY_TYPE = props.property
        ? props.property.type
        : props.schemaType;

    const isDiff: boolean = props.schemaType !== propsPropertyType;

    return (<div
        className="select-text flex gap-1 items-center"
    >
        {isDiff && <UIPopover
            placement="right"
            trigger={createUIButton({
                isIconOnly: true,
                color: "primary",
                variant: "light",
                size: "sm",
                children: (<IoIosWarning />),
            })}
        >
            <div>
                {`Type Mismatch: ${props.schemaType} !== ${propsPropertyType}`}
            </div>
        </UIPopover>}
        {props.render
            ? props.render(props.property ?? null)
            : propertyValue}
    </div>);
};
