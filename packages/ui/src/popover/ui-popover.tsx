/**
 * <AUTHOR>
 * @package UI
 * @namespace Popover
 * @description Popover
 */

import { Popover, PopoverContent, PopoverTrigger } from "@heroui/popover";
import type { FC, ReactNode } from "react";
import { UIPopoverBackdrop, UIPopoverPlacement } from "./types";

export type UIPopoverProps = {

    readonly className?: string;
    readonly contentClassName?: string;

    readonly placement?: UIPopoverPlacement;

    readonly backdrop?: UIPopoverBackdrop;

    readonly isOpen?: boolean;
    readonly onOpenChange?: (isOpen: boolean) => void;

    readonly trigger: ReactNode;

    readonly children: ReactNode;
};

export const UIPopover: FC<UIPopoverProps> = (
    props: UIPopoverProps,
) => {

    return (<Popover
        className={props.className}

        placement={props.placement}

        isOpen={props.isOpen}
        onOpenChange={props.onOpenChange}
    >
        <PopoverTrigger>
            {props.trigger}
        </PopoverTrigger>
        <PopoverContent
            className={props.contentClassName}
        >
            {props.children}
        </PopoverContent>
    </Popover>);
};
