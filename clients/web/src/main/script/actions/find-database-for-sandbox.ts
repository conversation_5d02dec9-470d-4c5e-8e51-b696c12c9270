/**
 * <AUTHOR>
 * @namespace Script_Actions
 * @description Find Database For Sandbox
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createFindDatabaseForSandboxAction = (
    originUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Find Database For Sandbox",
        actionDescription: `Find the database for sandbox from [${originUniqueIdentifier}]`,
        executerMetadata: metadata,
    };
};
