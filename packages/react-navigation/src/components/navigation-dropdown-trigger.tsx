/**
 * <AUTHOR>
 * @namespace Navigation_Components
 * @description Navigation Dropdown Trigger
 */

import { HUMMINGBIRD_NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE } from "@imbricate-hummingbird/configuration";
import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { createUIButton } from "@imbricate-hummingbird/ui";
import { SudooFormat } from "@sudoo/internationalization";
import React from "react";
import { TiThMenu } from "react-icons/ti";
import { NAVIGATION_PROFILE } from "../internationalization/profile";

export type NavigationDropdownTriggerProps = {

    readonly displayMode: HUMMINGBIRD_NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE;
    readonly navigationFormat: SudooFormat<NAVIGATION_PROFILE>;
};

export const createNavigationDropdownTrigger = (
    props: NavigationDropdownTriggerProps,
): React.ReactNode => {

    if (isFormatLoading(props.navigationFormat)) {
        return (<React.Fragment />);
    }

    if (props.displayMode === HUMMINGBIRD_NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE.TEXT) {

        return createUIButton({
            startContent: (<div>
                <TiThMenu
                    className="text-small"
                />
            </div>),
            children: props.navigationFormat.get(NAVIGATION_PROFILE.MORE),
        });
    }

    if (props.displayMode === HUMMINGBIRD_NAVIGATION_BOTTOM_BUTTONS_DISPLAY_MODE.ICON) {

        return createUIButton({
            children: (<TiThMenu
                className="text-xl"
            />),
        });
    }

    return (<React.Fragment />);
};
