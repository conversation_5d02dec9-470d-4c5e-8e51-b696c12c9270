/**
 * <AUTHOR>
 * @namespace Config
 * @description Resource Card
 */

import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { StyledCard } from "@imbricate-hummingbird/react-components";
import { <PERSON>ICard<PERSON>ody, UICardHeader, UIDivider, <PERSON><PERSON>ink, UISpacer } from "@imbricate-hummingbird/ui";
import { FaGithub, FaLink } from "react-icons/fa";
import { POLICY_HUMMINGBIRD_PRIVACY_POLICY, POLICY_HUMMINGBIRD_TERMS_OF_SERVICE } from "../../common/static/policy";
import { useConfigFormat } from "../internationalization/hook";
import { CONFIG_PROFILE } from "../internationalization/profile";

export const ResourceCard = () => {

    const configFormat = useConfigFormat();

    if (isFormatLoading(configFormat)) {
        return null;
    }

    return (<StyledCard>
        <UICardHeader>
            <FaLink />
            <UISpacer />
            {configFormat.get(CONFIG_PROFILE.RESOURCE)}
        </UICardHeader>
        <UIDivider />
        <UICardBody>
            <UILink
                href={POLICY_HUMMINGBIRD_TERMS_OF_SERVICE}
                isExternal
                showAnchorIcon
            >
                <span
                    className="font-mono"
                >IMBRICATE HUMMINGBIRD</span>&nbsp;Terms of Service
            </UILink>
            <UILink
                href={POLICY_HUMMINGBIRD_PRIVACY_POLICY}
                isExternal
                showAnchorIcon
            >
                <span
                    className="font-mono"
                >IMBRICATE HUMMINGBIRD</span>&nbsp;Privacy Policy
            </UILink>
            <UILink
                href="https://imbricate.io"
                isExternal
                showAnchorIcon
            >
                <span
                    className="font-mono"
                >IMBRICATE</span>&nbsp;Documentation
            </UILink>
            <UILink
                href="https://github.com/imbricate"
                isExternal
                showAnchorIcon
            >
                <FaGithub /><UISpacer />Github
            </UILink>
        </UICardBody>
    </StyledCard>);
};
