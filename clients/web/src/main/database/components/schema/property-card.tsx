/**
 * <AUTHOR>
 * @namespace Database_Components_Schema
 * @description Property Card
 */

import { getFormattedTextValue } from "@/internationalization/util/get-formatted-text-value";
import { StyledCard, getPropertyIcon } from "@imbricate-hummingbird/react-components";
import { UIButton, UIButtonGroup, UICardBody, UICardHeader, UIDivider, UIInput, UIPopover, UISingleSelect, createUIButton } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchema, ImbricateDatabaseSchemaProperty } from "@imbricate/core";
import { FC } from "react";
import { FaArrowCircleDown, FaArrowCircleUp, FaStar } from "react-icons/fa";
import { MdDelete, MdOutlineInfo } from "react-icons/md";
import { CommonCopyItem } from "../../../common/components/copy-item";
import { useDatabaseFormat } from "../../internationalization/hook";
import { DATABASE_PROFILE } from "../../internationalization/profile";
import { DatabaseSchemaPropertyCardOptions } from "./property-card-options";

export type DatabaseSchemaPropertyCardProps = {

    readonly property: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>;
    readonly schema: ImbricateDatabaseSchema;
    readonly setSchema: (schema: ImbricateDatabaseSchema) => void;
};

export const DatabaseSchemaPropertyCard: FC<DatabaseSchemaPropertyCardProps> = (
    props: DatabaseSchemaPropertyCardProps,
) => {

    const databaseFormat = useDatabaseFormat();

    const isPrimary: boolean = props.property.isPrimaryKey ?? false;
    const allowToSetPrimary: boolean = !isPrimary && props.property.propertyType === IMBRICATE_PROPERTY_TYPE.STRING;

    return (<StyledCard
        key={props.property.propertyIdentifier}
    >
        <UICardHeader
            className="flex justify-end"
        >
            <div
                className="text-large flex items-center gap-1 flex-1"
            >
                <UIPopover
                    placement="right"
                    trigger={createUIButton({
                        isIconOnly: true,
                        size: "sm",
                        variant: "flat",
                        color: isPrimary ? "primary" : "default",
                        children: (isPrimary ? <FaStar
                            className="text-medium"
                        /> : <MdOutlineInfo
                            className="text-medium"
                        />),
                    })}
                >
                    <CommonCopyItem
                        startContent={databaseFormat.get(DATABASE_PROFILE.PROPERTY_UNIQUE_IDENTIFIER)}
                        content={props.property.propertyIdentifier}
                    />
                </UIPopover>
                {isPrimary && databaseFormat.get(DATABASE_PROFILE.PRIMARY_KEY)}
            </div>
            <div
                className="text-default-400 text-small"
            >
                <UIButtonGroup>
                    <UIButton
                        isIconOnly
                        size="sm"
                        variant="flat"
                        onPress={() => {

                            const currentIndex: number = props.schema.properties.findIndex((each) => each.propertyIdentifier === props.property.propertyIdentifier);
                            if (currentIndex === 0) {
                                return;
                            }

                            const nextIndex: number = currentIndex - 1;
                            const nextProperties: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>[] = [...props.schema.properties];
                            const currentProperty: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE> = nextProperties[currentIndex];
                            nextProperties[currentIndex] = nextProperties[nextIndex];
                            nextProperties[nextIndex] = currentProperty;

                            props.setSchema({
                                properties: nextProperties,
                            });
                        }}
                    >
                        <FaArrowCircleUp
                            className="text-medium"
                        />
                    </UIButton>
                    <UIButton
                        isIconOnly
                        size="sm"
                        variant="flat"
                        onPress={() => {

                            const currentIndex: number = props.schema.properties.findIndex((each) => each.propertyIdentifier === props.property.propertyIdentifier);
                            if (currentIndex === props.schema.properties.length - 1) {
                                return;
                            }

                            const nextIndex: number = currentIndex + 1;
                            const nextProperties: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>[] = [...props.schema.properties];
                            const currentProperty: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE> = nextProperties[currentIndex];
                            nextProperties[currentIndex] = nextProperties[nextIndex];
                            nextProperties[nextIndex] = currentProperty;

                            props.setSchema({
                                properties: nextProperties,
                            });
                        }}
                    >
                        <FaArrowCircleDown
                            className="text-medium"
                        />
                    </UIButton>
                </UIButtonGroup>
            </div>
        </UICardHeader>
        <UIDivider />
        <UICardHeader
            className="flex flex-col gap-2 items-start"
        >
            <div
                className="flex w-full gap-1 items-center justify-center"
            >
                {allowToSetPrimary && <UIButton
                    className="h-14"
                    variant="flat"
                    color="primary"
                    isIconOnly
                    size="lg"
                    title="Set as Primary Key"
                    onPress={() => {

                        props.setSchema({
                            properties: props.schema.properties.map((each) => {
                                if (each.propertyIdentifier === props.property.propertyIdentifier) {
                                    return {
                                        ...each,
                                        propertyOptions: {
                                            ...each.propertyOptions,
                                        },
                                        isPrimaryKey: true,
                                    };
                                }
                                if (each.isPrimaryKey) {
                                    return {
                                        ...each,
                                        propertyOptions: {
                                            ...each.propertyOptions,
                                        },
                                        isPrimaryKey: false,
                                    };
                                }
                                return each;
                            }),
                        });
                    }}
                >
                    <FaStar
                        className="text-large"
                    />
                </UIButton>}
                <UIInput
                    label={databaseFormat.get(DATABASE_PROFILE.PROPERTY_NAME)}
                    color="primary"
                    value={props.property.propertyName}
                    className="flex-1"
                    onValueChange={(
                        newValue: string,
                    ) => {

                        props.setSchema({
                            properties: props.schema.properties.map((
                                each: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>,
                            ) => {

                                if (each.propertyIdentifier === props.property.propertyIdentifier) {
                                    return {
                                        ...each,
                                        propertyName: newValue,
                                    };
                                }
                                return each;
                            }),
                        });
                    }}
                />
                <UIButton
                    className="h-14"
                    variant="flat"
                    color="danger"
                    isIconOnly
                    size="lg"
                    onPress={() => {

                        props.setSchema({
                            properties: props.schema.properties.filter((each) => each.propertyIdentifier !== props.property.propertyIdentifier),
                        });
                    }}
                >
                    <MdDelete
                        className="text-large"
                    />
                </UIButton>
            </div>
        </UICardHeader>
        <UIDivider />
        <UICardBody>
            <UISingleSelect
                ariaLabel="Property Type"
                label={databaseFormat.get(DATABASE_PROFILE.PROPERTY_TYPE)}
                defaultSelectedKey={props.property.propertyType}
                startContent={getPropertyIcon(props.property.propertyType)}
                onSelectedKeyChange={(
                    newSelectedKey: string,
                ) => {

                    const newValue: IMBRICATE_PROPERTY_TYPE = newSelectedKey as IMBRICATE_PROPERTY_TYPE;

                    props.setSchema({
                        properties: props.schema.properties.map((each) => {
                            if (each.propertyIdentifier === props.property.propertyIdentifier) {
                                return {
                                    ...each,
                                    propertyType: newValue,
                                };
                            }
                            return each;
                        }),
                    });
                }}
                items={[
                    {
                        itemKey: IMBRICATE_PROPERTY_TYPE.BINARY,
                        textValue: getFormattedTextValue(
                            databaseFormat,
                            DATABASE_PROFILE.BINARY,
                            "Binary",
                        ),
                        startContent: getPropertyIcon(IMBRICATE_PROPERTY_TYPE.BINARY),
                        content: databaseFormat.get(DATABASE_PROFILE.BINARY),
                    },
                    {
                        itemKey: IMBRICATE_PROPERTY_TYPE.BOOLEAN,
                        textValue: getFormattedTextValue(
                            databaseFormat,
                            DATABASE_PROFILE.BOOLEAN,
                            "Boolean",
                        ),
                        startContent: getPropertyIcon(IMBRICATE_PROPERTY_TYPE.BOOLEAN),
                        content: databaseFormat.get(DATABASE_PROFILE.BOOLEAN),
                    },
                    {
                        itemKey: IMBRICATE_PROPERTY_TYPE.STRING,
                        textValue: getFormattedTextValue(
                            databaseFormat,
                            DATABASE_PROFILE.STRING,
                            "String",
                        ),
                        startContent: getPropertyIcon(IMBRICATE_PROPERTY_TYPE.STRING),
                        content: databaseFormat.get(DATABASE_PROFILE.STRING),
                    },
                    {
                        itemKey: IMBRICATE_PROPERTY_TYPE.NUMBER,
                        textValue: getFormattedTextValue(
                            databaseFormat,
                            DATABASE_PROFILE.NUMBER,
                            "Number",
                        ),
                        startContent: getPropertyIcon(IMBRICATE_PROPERTY_TYPE.NUMBER),
                        content: databaseFormat.get(DATABASE_PROFILE.NUMBER),
                    },
                    {
                        itemKey: IMBRICATE_PROPERTY_TYPE.DATE,
                        textValue: getFormattedTextValue(
                            databaseFormat,
                            DATABASE_PROFILE.DATE,
                            "Date",
                        ),
                        startContent: getPropertyIcon(IMBRICATE_PROPERTY_TYPE.DATE),
                        content: databaseFormat.get(DATABASE_PROFILE.DATE),
                    },
                    {
                        itemKey: IMBRICATE_PROPERTY_TYPE.MARKDOWN,
                        textValue: getFormattedTextValue(
                            databaseFormat,
                            DATABASE_PROFILE.MARKDOWN,
                            "Markdown",
                        ),
                        startContent: getPropertyIcon(IMBRICATE_PROPERTY_TYPE.MARKDOWN),
                        content: databaseFormat.get(DATABASE_PROFILE.MARKDOWN),
                    },
                    {
                        itemKey: IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT,
                        textValue: getFormattedTextValue(
                            databaseFormat,
                            DATABASE_PROFILE.IMBRISCRIPT,
                            "ImbriScript",
                        ),
                        startContent: getPropertyIcon(IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT),
                        content: databaseFormat.get(DATABASE_PROFILE.IMBRISCRIPT),
                    },
                    {
                        itemKey: IMBRICATE_PROPERTY_TYPE.JSON,
                        textValue: getFormattedTextValue(
                            databaseFormat,
                            DATABASE_PROFILE.JSON,
                            "JSON",
                        ),
                        startContent: getPropertyIcon(IMBRICATE_PROPERTY_TYPE.JSON),
                        content: databaseFormat.get(DATABASE_PROFILE.JSON),
                    },
                    {
                        itemKey: IMBRICATE_PROPERTY_TYPE.LABEL,
                        textValue: getFormattedTextValue(
                            databaseFormat,
                            DATABASE_PROFILE.LABEL,
                            "Label",
                        ),
                        startContent: getPropertyIcon(IMBRICATE_PROPERTY_TYPE.LABEL),
                        content: databaseFormat.get(DATABASE_PROFILE.LABEL),
                    },
                    {
                        itemKey: IMBRICATE_PROPERTY_TYPE.REFERENCE,
                        textValue: getFormattedTextValue(
                            databaseFormat,
                            DATABASE_PROFILE.REFERENCE,
                            "Reference",
                        ),
                        startContent: getPropertyIcon(IMBRICATE_PROPERTY_TYPE.REFERENCE),
                        content: databaseFormat.get(DATABASE_PROFILE.REFERENCE),
                    },
                ]}
            />
        </UICardBody>
        <DatabaseSchemaPropertyCardOptions
            property={props.property}
            schema={props.schema}
            setSchema={props.setSchema}
        />
    </StyledCard>);
};
