/**
 * <AUTHOR>
 * @namespace Common_Components_Unsupported
 * @description Origin Unsupported Warning
 */

import { UIColor } from "@imbricate-hummingbird/ui";
import { IImbricateOrigin } from "@imbricate/core";
import { IMBRICATE_ORIGIN_FEATURE } from "@imbricate/core/origin/feature";
import { FC } from "react";
import { UnsupportedWarning } from "./unsupported-warning";

export type OriginUnsupportedWarningProps = {

    readonly originName: string;
    readonly origin: IImbricateOrigin;

    readonly feature: IMBRICATE_ORIGIN_FEATURE;

    readonly variant?: "flat" | "solid" | "faded" | "bordered";
    readonly color?: UIColor;
};

export const OriginUnsupportedWarning: FC<OriginUnsupportedWarningProps> = (
    props: OriginUnsupportedWarningProps,
) => {

    switch (props.feature) {
        case IMBRICATE_ORIGIN_FEATURE.ORIGIN_DATABASE_MANAGER: {

            return (<UnsupportedWarning
                title="Database Manager"
                message={<div>
                    <span className="font-bold">{props.originName}</span> does not support Database Manager
                </div>}
                variant={props.variant}
                color={props.color}
            />);
        }
    }

    return null;
};
