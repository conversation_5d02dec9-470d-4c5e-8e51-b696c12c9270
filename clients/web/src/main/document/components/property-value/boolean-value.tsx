/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyValue
 * @description Boolean Value
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UICheckbox } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";

export type DocumentBooleanValueProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BOOLEAN>;
    readonly updateProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BOOLEAN>) => void;
};

export const DocumentBooleanValue: FC<DocumentBooleanValueProps> = (
    props: DocumentBooleanValueProps,
) => {

    return (<UICheckbox
        size="lg"
        isSelected={props.property.value}
        onValueChange={(newSelected: boolean) => {

            props.updateProperty({
                key: props.propertyKey,
                type: IMBRICATE_PROPERTY_TYPE.BOOLEAN,
                value: newSelected,
                variant: props.property.variant,
            });
        }}
    />);
};
