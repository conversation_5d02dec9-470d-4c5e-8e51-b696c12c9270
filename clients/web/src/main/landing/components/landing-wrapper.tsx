/**
 * <AUTHOR>
 * @namespace Common_Components_MainWrapper
 * @description Main Wrapper
 */

import "../../index.css";

import { DebugActionBarWrapper } from "@/main/debug/components/debug-action-bar-wrapper";
import { useControlledTheme } from "@imbricate-hummingbird/theme-core";
import clsx from "clsx";
import React, { FC } from "react";
import { ConfigBrandBanner } from "../../config/components/brand-banner";
import { DataRecoverWrapper } from "../../data/components/data-recover-wrapper";
import { useDataRecover } from "../../data/hooks/use-data-recover";

export type LandingMainWrapperProps = {

    readonly children: React.ReactNode;

    readonly className?: string;
};

export const LandingMainWrapper: FC<LandingMainWrapperProps> = (
    props: LandingMainWrapperProps,
) => {

    const theme = useControlledTheme();
    const dataRecover = useDataRecover();

    const fixedClassNames = clsx(
        theme.theme,
        "text-foreground bg-background h-dvh box-border max-h-screen overflow-hidden",
        props.className,
    );

    const showDataRecover = dataRecover.recoverableItems.length > 0;

    return (<main
        className={fixedClassNames}
    >
        <div
            className={clsx(
                showDataRecover ? "h-[24px]" : "h-0",
                "overflow-hidden",
                "transition-all duration-150 ease-in-out",
                "bg-danger",
            )}
        >
            <DataRecoverWrapper
                dataRecover={dataRecover}
            />
        </div>
        <div
            className={clsx(
                showDataRecover ? "h-[calc(100vh-24px)]" : "h-full",
                "transition-all duration-150 ease-in-out",
            )}
        >
            <div
                className="flex h-full min-h-0 min-w-0"
            >
                <ConfigBrandBanner />
                <div
                    className="flex-1 flex flex-col items-center justify-center h-full overflow-auto min-h-0 min-w-0"
                >
                    {props.children}
                </div>
            </div>
        </div>
        <DebugActionBarWrapper />
    </main>);
};
