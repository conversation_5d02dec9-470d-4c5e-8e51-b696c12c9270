/**
 * <AUTHOR>
 * @package UI
 * @namespace Input
 * @description Input
 */

import { Input } from "@heroui/input";
import type { ReactNode } from "react";
import { UIColor, UIRadius, UISize } from "../common/types";
import { UIInputLabelPlacement, UIInputType, UIInputVariant } from "./types";

export type UIInputProps = {

    readonly ref?: React.Ref<HTMLInputElement>;

    readonly className?: string;

    readonly color?: UIColor;
    readonly size?: UISize;
    readonly radius?: UIRadius;
    readonly variant?: UIInputVariant;

    readonly labelPlacement?: UIInputLabelPlacement;

    readonly isAutofocus?: boolean;
    readonly isDisabled?: boolean;
    readonly isReadOnly?: boolean;
    readonly isRequired?: boolean;
    readonly isInvalid?: boolean;
    readonly isClearable?: boolean;
    readonly isFullWidth?: boolean;

    readonly label?: ReactNode;
    readonly startContent?: ReactNode;
    readonly endContent?: ReactNode;

    readonly errorMessage?: string;
    readonly placeholder?: string;
    readonly defaultValue?: string;

    readonly type?: UIInputType;

    readonly value?: string;
    readonly onValueChange?: (newValue: string) => void;

    readonly onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
};

export const UIInput: React.FC<UIInputProps> = (
    props: UIInputProps,
) => {

    return (<Input
        ref={props.ref}

        className={props.className}

        color={props.color}
        size={props.size}
        radius={props.radius}
        variant={props.variant}

        labelPlacement={props.labelPlacement}

        autoFocus={props.isAutofocus}
        isDisabled={props.isDisabled}
        isReadOnly={props.isReadOnly}
        isRequired={props.isRequired}
        isInvalid={props.isInvalid}
        isClearable={props.isClearable}
        fullWidth={props.isFullWidth}

        label={props.label}
        startContent={props.startContent}
        endContent={props.endContent}

        errorMessage={props.errorMessage}
        placeholder={props.placeholder}
        defaultValue={props.defaultValue}

        type={props.type}

        value={props.value}
        onValueChange={props.onValueChange}

        onKeyDown={props.onKeyDown}
    />);
};
