/**
 * <AUTHOR>
 * @namespace Config_PanelsWrapper
 * @description Appearance Panel Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const ConfigAppearancePanel = LazyLoadComponent(
    () => import("../panels/appearance-panel"),
    "Config Appearance Panel",
);

export type ConfigAppearancePanelWrapperProps = {
};

export const ConfigAppearancePanelWrapper: FC<ConfigAppearancePanelWrapperProps> = (
    _props: ConfigAppearancePanelWrapperProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Config Appearance Panel"
            fullHeight
        />}
    >
        <ConfigAppearancePanel />
    </React.Suspense>);
};
