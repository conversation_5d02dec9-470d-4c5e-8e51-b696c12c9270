/**
 * <AUTHOR>
 * @namespace Common_Components_HeaderTitle_Types
 * @description Types
 */

import { IconType } from "react-icons";

export enum HEADER_TITLE_SECONDARY_ITEM_TYPE {

    LINK = "LINK",
}

export type HeaderTitleSecondaryItemLink = {

    readonly type: HEADER_TITLE_SECONDARY_ITEM_TYPE.LINK;

    readonly link: string;
    readonly title: string;
    readonly icon: IconType;
};

export type HeaderTitleSecondaryItem = HeaderTitleSecondaryItemLink;

export type HeaderTitleProps = {

    readonly title: string;

    readonly secondary?: HeaderTitleSecondaryItem;
};
