/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyCard
 * @description Property Display Content
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaProperty } from "@imbricate/core";
import React from "react";
import { DocumentBinaryDisplay } from "../property-display/binary-display";
import { DocumentBooleanDisplay } from "../property-display/boolean-display";
import { DocumentDateDisplay } from "../property-display/date-display";
import { DocumentImbriscriptDisplay } from "../property-display/imbriscript-display";
import { DocumentJsonDisplay } from "../property-display/json-display";
import { DocumentLabelDisplay } from "../property-display/label-display";
import { DocumentMarkdownDisplay } from "../property-display/markdown-display";
import { DocumentNumberDisplay } from "../property-display/number-display";
import { DocumentReferenceDisplay } from "../property-display/reference-display";
import { DocumentStringDisplay } from "../property-display/string-display";

export type DocumentPropertyDisplayContentProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;

    readonly schema: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>;
};

export const DocumentPropertyDisplayContent: React.FC<DocumentPropertyDisplayContentProps> = (
    props: DocumentPropertyDisplayContentProps,
) => {

    const propertyType: IMBRICATE_PROPERTY_TYPE = props.schema.propertyType;
    const propertyIdentifier: string = props.schema.propertyIdentifier;

    // IMBRICATE_PROPERTY_TYPE SWITCH
    switch (propertyType) {

        case IMBRICATE_PROPERTY_TYPE.BINARY: {

            return (<DocumentBinaryDisplay
                originUniqueIdentifier={props.originUniqueIdentifier}
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY>}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.BOOLEAN: {

            return (<DocumentBooleanDisplay
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BOOLEAN>}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.STRING: {

            return (<DocumentStringDisplay
                propertySchema={props.schema as ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE.STRING>}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.STRING>}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.NUMBER: {

            return (<DocumentNumberDisplay
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.NUMBER>}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.DATE: {

            return (<DocumentDateDisplay
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.DATE>}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.MARKDOWN: {

            return (<DocumentMarkdownDisplay
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.MARKDOWN>}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT: {

            return (<DocumentImbriscriptDisplay
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT>}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.JSON: {

            return (<DocumentJsonDisplay
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.JSON>}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.LABEL: {

            return (<DocumentLabelDisplay
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.LABEL>}
                options={props.schema.propertyOptions as any}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.REFERENCE: {

            return (<DocumentReferenceDisplay
                propertyKey={propertyIdentifier}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.REFERENCE>}
            />);
        }
        default: {

            return (<DocumentStringDisplay
                propertySchema={props.schema as ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE.STRING>}
                property={props.property as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.STRING>}
            />);
        }
    }
};
