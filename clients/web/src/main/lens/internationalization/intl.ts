/**
 * <AUTHOR>
 * @namespace Lens_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooLazyInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { LENS_PROFILE } from "./profile";

export const lensInternationalization: SudooLazyInternationalization<LENS_PROFILE> =
    SudooLazyInternationalization.create<LENS_PROFILE>(
        DEFAULT_LOCALE,
    );

lensInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/en-US").then(
        (module) => module.enUSLensProfile,
    ),
);

lensInternationalization.mergeLazyProfile(
    IETF_LOCALE.JAPANESE_JAPAN,
    import("./locale/ja-JP").then(
        (module) => module.jaJPLensProfile,
    ),
);

lensInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    import("./locale/zh-CN").then(
        (module) => module.zhCNLensProfile,
    ),
);
