/**
 * <AUTHOR>
 * @namespace Origin_Components_OriginNew
 * @description New Origin Select
 */

import { useDebugMode } from "@imbricate-hummingbird/debug";
import { ORIGIN_INSTANCE_TYPE } from "@imbricate-hummingbird/transfer-core";
import { UISelectItemData, UISingleSelect } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { FaBug } from "react-icons/fa";

export type NewOriginSelectProps = {

    readonly onSelect: (originType: ORIGIN_INSTANCE_TYPE) => void;
};

export const NewOriginSelect: FC<NewOriginSelectProps> = (
    props: NewOriginSelectProps,
) => {

    const debugMode = useDebugMode();

    const extraSelectItems: UISelectItemData[] = debugMode.isDebugMode
        ? [
            {
                itemKey: ORIGIN_INSTANCE_TYPE.MOCK,
                content: ORIGIN_INSTANCE_TYPE.MOCK,
                color: "warning",
                variant: "faded",
                startContent: <FaBug />,
            },
        ]
        : [];

    return (<div>
        <UISingleSelect
            ariaLabel="origin-type-selector"
            label="Origin Type"
            className="max-w-xs"
            onSelectedKeyChange={(
                newSelectedKey: string,
            ) => {
                props.onSelect(newSelectedKey as ORIGIN_INSTANCE_TYPE);
            }}
            items={[
                {
                    itemKey: ORIGIN_INSTANCE_TYPE.STACK_API,
                    content: ORIGIN_INSTANCE_TYPE.STACK_API,
                },
                ...extraSelectItems,
            ]}
        />
    </div>);
};
