/**
 * <AUTHOR>
 * @namespace Monaco_LogPreview_Internationalization
 * @description Hook
 */

import { useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { monacoLogPreviewInternationalization } from "./intl";
import { MONACO_LOG_PREVIEW_PROFILE } from "./profile";

export const useMonacoLogPreviewFormat = (): SudooFormat<MONACO_LOG_PREVIEW_PROFILE> => {

    const locale = useLocale();

    return monacoLogPreviewInternationalization.format(locale);
};
