/**
 * <AUTHOR>
 * @namespace Data_Components_DataRecoverDetails
 * @description Data Recover Details
 */

import { useDebugMode } from "@imbricate-hummingbird/debug";
import { ActionCentral, ActionItem } from "@imbricate-hummingbird/interceptor-core";
import { UIButton, UIChip } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { MdOutlineRestartAlt } from "react-icons/md";
import { formatTime } from "../../common/util/format-time";
import { UseDataRecoverResponse } from "../hooks/use-data-recover";
import { useDataFormat } from "../internationalization/hook";
import { DATA_PROFILE } from "../internationalization/profile";
import { ActionPayloadPopover } from "./action-payload-popover";
import { DataRecoverIcon } from "./data-recover-icon";

export type DataRecoverDetailsProps = {

    readonly dataRecover: UseDataRecoverResponse;
};

export const DataRecoverDetails: FC<DataRecoverDetailsProps> = (
    props: DataRecoverDetailsProps,
) => {

    const format = useDataFormat();

    const { isDebugMode } = useDebugMode();

    return (<div
        className="w-full"
    >
        {props.dataRecover.recoverableItems.map((
            recoverItem: ActionItem<any>,
        ) => {
            return (<div
                key={recoverItem.actionIdentifier}
                className="flex flex-row gap-4 items-center"
            >
                <DataRecoverIcon
                    item={recoverItem}
                />
                <div
                    className="flex-1 overflow-hidden min-h-0"
                >
                    <div
                        className="flex flex-row gap-1 items-center"
                    >
                        {recoverItem.retryCount > 0 ?
                            (<UIChip
                                className="font-mono"
                                color="primary"
                                size="sm"
                            >
                                {format.get(DATA_PROFILE.RETRY)} ({recoverItem.retryCount})
                            </UIChip>)
                            : null}
                        <span
                            className="font-bold"
                        >
                            {recoverItem.description.actionName}
                        </span>
                        {recoverItem.description.actionPayload && <ActionPayloadPopover
                            data={recoverItem.description.actionPayload}
                        />}
                    </div>
                    <div
                        className="text-sm text-gray-500 overflow-hidden text-ellipsis w-full"
                    >
                        {recoverItem.description.actionDescription}
                    </div>
                    <div
                        className="flex flex-row gap-1 items-center"
                    >
                        <span
                            className="font-bold"
                        >
                            {format.get(DATA_PROFILE.REASON)}
                        </span>
                        <span
                            className="font-bold font-mono"
                        >
                            {recoverItem.erroredReason}
                        </span>
                    </div>
                    {isDebugMode && <div
                        className="text-tiny text-gray-500 font-mono overflow-hidden text-ellipsis w-full"
                    >
                        {recoverItem.description.executerMetadata.executer}
                    </div>}
                    <div
                        className="text-tiny text-gray-500 font-mono"
                    >
                        {formatTime(recoverItem.startAt)}
                    </div>
                </div>
                {!recoverItem.retrying && <div>
                    <UIButton
                        startContent={<MdOutlineRestartAlt
                            className="text-xl"
                        />}
                        className="font-mono font-bold"
                        color="primary"
                        variant="flat"
                        onPress={() => {
                            ActionCentral.getInstance()
                                .retryAction(recoverItem.actionIdentifier);
                        }}
                    >
                        {recoverItem.retryCount > 0
                            ? format.get(DATA_PROFILE.RETRY_AGAIN)
                            : format.get(DATA_PROFILE.RETRY)}
                    </UIButton>
                </div>}
            </div>);
        })}
    </div>);
};
