/**
 * <AUTHOR>
 * @namespace Debug_Components_RecentActionTracker
 * @description Recent Action Item
 */

import { RECENT_ACTION_STATUS, RecentActionItem } from "@imbricate-hummingbird/interceptor-core";
import { WORKER_WEB_WORKER } from "@imbricate-hummingbird/web-worker-core";
import { FC } from "react";
import { formatTime } from "../../../common/util/format-time";
import { ActionPayloadPopover } from "../../../data/components/action-payload-popover";
import { DisplayRecentAction } from "../../types/display-recent-action";
import { RecentActionChips } from "./recent-action-chips";
import { RecentActionExtraInformation } from "./recent-action-extra-information";
import { RECENT_ACTION_STATUSIcon } from "./recent-action-status-icon";
import { RecentSubActionItem } from "./recent-sub-action-item";

const getActionDuration = (
    action: RecentActionItem<WORKER_WEB_WORKER>,
): string => {

    if (!action.endAt) {
        return "";
    }
    const duration: number = action.endAt - action.startAt;
    return `${duration / 1000} seconds`;
};

export type RecentActionItemCardProps = {

    readonly showExtraInformation: boolean;

    readonly displayRecentAction: DisplayRecentAction;
};

export const RecentActionItemCard: FC<RecentActionItemCardProps> = (
    props: RecentActionItemCardProps,
) => {

    const action: RecentActionItem<WORKER_WEB_WORKER> = props.displayRecentAction.recentAction;

    return (<div
        className="flex flex-row gap-4 items-center flex-1"
    >
        <div>
            <RECENT_ACTION_STATUSIcon
                status={action.status}
            />
        </div>
        <div
            className="w-full overflow-hidden min-h-0"
        >
            <div
                className="flex flex-row gap-1 items-center w-full"
            >
                <RecentActionChips
                    action={action}
                />
                <span
                    className="font-bold"
                >
                    {action.description.actionName}
                </span>
                {action.description.actionPayload && <ActionPayloadPopover
                    data={action.description.actionPayload}
                />}
            </div>
            <div
                className="text-sm text-gray-500 overflow-hidden text-ellipsis w-full"
            >
                {action.description.actionDescription}
            </div>
            <div
                className="text-tiny text-gray-500 font-mono overflow-hidden text-ellipsis w-full"
            >
                {action.description.executerMetadata.executer}
            </div>
            <div
                className="text-tiny text-gray-500 font-mono w-full"
            >
                {formatTime(action.startAt)}
                {action.endAt ? ` - ${formatTime(action.endAt)} (${getActionDuration(action)})` : ""}
            </div>
            <RecentActionExtraInformation
                showExtraInformation={props.showExtraInformation}
                action={action}
            />
            {props.displayRecentAction.subActions.length > 0 && <div
                className="flex flex-col w-full gap-2"
            >
                <div
                    className="text-tiny text-gray-500 font-mono w-full"
                >
                    Sub Actions
                </div>
                {props.displayRecentAction.subActions.map((
                    subAction: DisplayRecentAction,
                ) => {
                    return (<RecentSubActionItem
                        key={subAction.recentAction.recentActionIdentifier}
                        showExtraInformation={props.showExtraInformation}
                        displayRecentAction={subAction}
                        layer={1}
                    />);
                })}
            </div>}
        </div>
        {
            action.status === RECENT_ACTION_STATUS.FAILED && (
                <div
                    className="flex flex-col items-end text-end"
                >
                    <p
                        className="text-sm text-gray-500 font-bold"
                    >
                        Reason
                    </p>
                    <p
                        className="text-tiny text-gray-500 font-mono"
                    >
                        {action.result}
                    </p>
                </div>
            )
        }
    </div>);
};
