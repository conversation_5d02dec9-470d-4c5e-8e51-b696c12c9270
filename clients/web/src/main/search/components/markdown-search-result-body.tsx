/**
 * <AUTHOR>
 * @namespace Search_Component
 * @description Markdown Search Result Body
 */

import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { IMBRICATE_SEARCH_TARGET_TYPE, ImbricateSearchItem, ImbricateSearchTarget } from "@imbricate/core";
import React, { FC } from "react";

export type MarkdownSearchResultBodyProps = {

    readonly origin: ImbricateOriginObject;
    readonly searchItem: ImbricateSearchItem;
};

export const MarkdownSearchResultBody: FC<MarkdownSearchResultBodyProps> = (
    props: MarkdownSearchResultBodyProps,
) => {

    const target = props.searchItem.target as ImbricateSearchTarget<IMBRICATE_SEARCH_TARGET_TYPE.MARKDOWN>;

    const longestLineNumberDigits: number = Math.max(
        target.target.lineNumber - 1,
        target.target.lineNumber,
        target.target.lineNumber + 1,
    ).toString().length;

    return (<div
        className="flex flex-col gap-1"
    >
        {target.target.lineNumber > 1 && <div
            className="flex gap-2"
        >
            <div
                className="font-mono"
            >
                {Number(target.target.lineNumber - 1).toFixed().padStart(longestLineNumberDigits, "0")}
            </div>
            <div
                className="w-full overflow-hidden text-ellipsis text-nowrap"
            >
                {props.searchItem.secondaryPrevious}
            </div>
        </div>}
        <div
            className="flex gap-2"
        >
            <div
                className="font-mono font-bold"
            >
                {Number(target.target.lineNumber).toFixed().padStart(longestLineNumberDigits, "0")}
            </div>
            <div
                className="w-full overflow-hidden text-ellipsis text-nowrap"
            >
                {props.searchItem.secondary.trim()}
            </div>
        </div>
        <div
            className="flex gap-2"
        >
            <div
                className="font-mono"
            >
                {Number(target.target.lineNumber + 1).toFixed().padStart(longestLineNumberDigits, "0")}
            </div>
            <div
                className="w-full overflow-hidden text-ellipsis text-nowrap"
            >
                {props.searchItem.secondaryNext}
            </div>
        </div>
    </div>);
};
