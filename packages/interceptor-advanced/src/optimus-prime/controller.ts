/**
 * <AUTHOR>
 * @namespace OptimusPrime
 * @description Controller
 */

export type OptimusPrimeOverrideInstance = {

    readonly source: string;
    readonly value: string;

    active: boolean;
}

export class OptimusPrimeController {

    private static _instance: OptimusPrimeController;

    public static getInstance(): OptimusPrimeController {

        if (!OptimusPrimeController._instance) {
            OptimusPrimeController._instance = new OptimusPrimeController();
        }

        return OptimusPrimeController._instance;
    }

    public static createStandaloneInstance(): OptimusPrimeController {

        return new OptimusPrimeController();
    }

    private readonly _listeners: Set<() => void>;

    private _overrideInstances: OptimusPrimeOverrideInstance[];

    private _currentItem: number;
    private _currentPosition: number;

    private constructor() {

        this._listeners = new Set();

        this._overrideInstances = [];

        this._currentItem = 0;
        this._currentPosition = 0;
    }

    public addListener(listener: () => void): void {

        this._listeners.add(listener);
    }

    public removeListener(listener: () => void): void {

        this._listeners.delete(listener);
    }

    public getOverrides(): OptimusPrimeOverrideInstance[] {

        return this._overrideInstances;
    }

    public addOverride(source: string, value: string): void {

        const fixedValue = value.replace(/[^a-zA-Z0-9-]/gm, "");

        this._overrideInstances.push({
            source,
            value: fixedValue,
            active: true,
        });
        this._notifyListeners();
    }

    public removeOverride(source: string): void {

        this._overrideInstances = this._overrideInstances.map((instance) => {

            if (instance.source === source) {
                return {
                    ...instance,
                    active: false,
                };
            }
            return instance;
        });
        this._notifyListeners();
    }

    public getNextObject(
        length: number,
        existing: string = "",
    ): string | null {

        if (this._overrideInstances.length === 0) {
            return null;
        }

        if (existing.length >= length) {
            return existing;
        }

        const currentObject = this._overrideInstances[this._currentItem];

        if (!currentObject) {

            this._resetAll();
            return this.getNextObject(length, existing);
        }

        if (!currentObject.active) {

            this._nextItem();
            return this.getNextObject(length, existing);
        }

        const currentValue = currentObject.value.slice(this._currentPosition);

        if (currentValue.length === 0) {

            this._nextItem();
            return this.getNextObject(length, existing);
        }

        const remainingLength = length - existing.length;
        const nextValue = currentValue.slice(0, remainingLength);
        const totalValue = existing + nextValue;

        if (totalValue.length >= length) {

            this._currentPosition += remainingLength;
            return this.getNextObject(length, totalValue);
        }

        this._nextItem();
        return this.getNextObject(length, totalValue);
    }

    private _nextItem(): void {

        this._currentItem++;
        this._currentPosition = 0;
    }

    private _resetAll(): void {

        this._currentItem = 0;
        this._currentPosition = 0;

        this._overrideInstances = this._overrideInstances.filter((instance) => {
            return instance.active;
        });
    }

    private _notifyListeners(): void {

        this._listeners.forEach((listener) => listener());
    }
}
