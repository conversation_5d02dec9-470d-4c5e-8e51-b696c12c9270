/**
 * <AUTHOR>
 * @namespace Document_Components
 * @description Documents Table Cells
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { TableCell } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaProperty } from "@imbricate/core";
import { JSX } from "react";
import { DocumentEditingController } from "../controller/editing-controller";
import { ArrangeDocumentsResultItem } from "../util/arrange-documents";
import { DocumentsTableEditingExtraCell } from "./extra-cell/editing-extra";
import { getDocumentTableCell } from "./table-cell";

export type DocumentsTableCellsProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;

    readonly propertyIdentifiers: string[];
    readonly propertyTypesMap: Record<string, IMBRICATE_PROPERTY_TYPE>;
    readonly schemaMap: Record<string, ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>>;

    readonly document: ArrangeDocumentsResultItem;
    readonly editingController: DocumentEditingController;
};

export const createDocumentsTableCells = (
    props: DocumentsTableCellsProps,
): JSX.Element[] => {

    return [
        ...props.propertyIdentifiers.map((
            propertyIdentifier: string,
        ) => {

            const propertyDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> | undefined =
                props.document.propertyValueMap[propertyIdentifier];

            const schemaProperty: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE> =
                props.schemaMap[propertyIdentifier];

            return getDocumentTableCell({
                originUniqueIdentifier: props.originUniqueIdentifier,
                databaseUniqueIdentifier: props.databaseUniqueIdentifier,
                propertyIdentifier: propertyIdentifier,
                propertyDraft: propertyDraft,
                schemaProperty: schemaProperty,
                document: props.document,
                editingController: props.editingController,
            });
        }),
        <TableCell
            key="$extra"
        >
            <DocumentsTableEditingExtraCell
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                item={props.document}
                editingController={props.editingController}
            />
        </TableCell>,
    ];
};
