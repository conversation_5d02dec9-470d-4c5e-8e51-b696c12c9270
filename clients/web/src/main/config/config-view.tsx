/**
 * <AUTHOR>
 * @namespace Config
 * @description Config View
 */

import { useDebugMode } from "@imbricate-hummingbird/debug";
import { useTitleWithAsyncFormat } from "@imbricate-hummingbird/internationalization";
import { UINavbar, UIScrollShadow } from "@imbricate-hummingbird/ui";
import { FC, useMemo, useState } from "react";
import { FaCog } from "react-icons/fa";
import { Outlet } from "react-router-dom";
import { BrandFooter } from "../common/components/footer";
import { ConfigBrandBanner } from "./components/brand-banner";
import { useConfigFormat } from "./internationalization/hook";
import { CONFIG_PROFILE } from "./internationalization/profile";
import { ConfigNavigation } from "./navigation/config-navigation";

export type ConfigViewProps = {
};

// LAZY LOAD ONLY
const ConfigView: FC<ConfigViewProps> = (
    _props: ConfigViewProps,
) => {

    const configFormat = useConfigFormat();

    const { isDebugMode, isDevelopmentMode } = useDebugMode();
    const [easterEggTriggered, setEasterEggTriggered] = useState(isDebugMode);

    useTitleWithAsyncFormat(
        configFormat,
        () => [
            configFormat.get(CONFIG_PROFILE.CONFIG),
        ],
        [],
    );

    const shouldDisplayDebugCard = useMemo(() => {
        if (isDevelopmentMode || isDebugMode) {
            return true;
        }

        return easterEggTriggered;
    }, [isDevelopmentMode, isDebugMode, easterEggTriggered]);

    return (<div
        className="h-full min-h-full flex flex-col"
    >
        <UINavbar
            isFullWidth
            isBordered
            startContent={<FaCog
                className="text-2xl"
            />}
            coreContent={<p className="font-bold text-xl">
                {configFormat.get(CONFIG_PROFILE.CONFIG)}
            </p>}
        />
        <div
            className="flex-1 min-h-0 min-w-0 flex gap-2 h-full"
        >
            <div
                className="flex-1 h-full flex flex-col"
            >
                <div
                    className="flex-1 h-full overflow-auto flex flex-row gap-2"
                >
                    <ConfigNavigation
                        shouldDisplayDebugPanel={shouldDisplayDebugCard}
                    />
                    <UIScrollShadow
                        className="flex-1 h-full flex flex-col gap-2"
                        overflowXAuto
                        overflowYAuto
                    >
                        <Outlet />
                    </UIScrollShadow>
                </div>
                <BrandFooter
                    onBrandEasterEgg={() => {
                        setEasterEggTriggered(true);
                    }}
                />
            </div>
            <ConfigBrandBanner />
        </div>
    </div>);
};
export default ConfigView;
