/**
 * <AUTHOR>
 * @namespace Data_Components_DataRecover
 * @description Data Recover
 */

import { useDebugMode } from "@imbricate-hummingbird/debug";
import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { openWarningToaster } from "@imbricate-hummingbird/react-components";
import { UIButton, UIDivider, UIModal, UIModalBody, UIModalFooter, UIModalHeader, useUIDisclosure } from "@imbricate-hummingbird/ui";
import React, { FC, useState } from "react";
import { FaBug } from "react-icons/fa";
import { UseDataRecoverResponse } from "../hooks/use-data-recover";
import { useDataFormat } from "../internationalization/hook";
import { DATA_PROFILE } from "../internationalization/profile";
import { DataRecoverDetails } from "./data-recover-details";

export type DataRecoverProps = {

    readonly dataRecover: UseDataRecoverResponse;
};

// LAZY LOAD ONLY
const DataRecover: FC<DataRecoverProps> = (
    props: DataRecoverProps,
) => {

    const isDebug = useDebugMode();

    const format = useDataFormat();

    const { isOpen, onOpen, onOpenChange } = useUIDisclosure({
        initialOpen: true,
    });

    const [closeConfirmed, setCloseConfirmed] = useState(false);

    return (<div
        className="bg-danger text-danger-foreground"
    >
        <UIButton
            isFullWidth
            aria-label="Error occurred on linked origins"
            className="flex flex-col items-center font-mono font-bold h-[24px]"
            radius="none"
            color="danger"
            variant="solid"
            onPress={onOpen}
        >
            {format.get(DATA_PROFILE.ERROR_OCCURRED)}
        </UIButton>
        <UIModal
            isOpen={isOpen}
            onOpenChange={onOpenChange}
            size="3xl"
            isHideCloseButton
            isDismissable={false}
            isKeyboardDismissDisabled={true}
        >
            {(
                onClose: () => void,
            ) => {
                return (<React.Fragment>
                    <UIModalHeader
                        className="items-center gap-5"
                    >
                        <FaBug
                            className="text-2xl"
                        />
                        <div
                            className="flex-1"
                        >
                            <div
                                className="font-mono text-2xl"
                            >
                                {format.get(DATA_PROFILE.ERROR_OCCURRED_WHEN_PROCESSING_DATA)}
                            </div>
                        </div>
                        {isDebug.isDebugMode && <div>
                            <UIButton
                                startContent={<FaBug />}
                                color="warning"
                                variant="flat"
                                onPress={() => {
                                    onClose();
                                }}
                            >
                                Continue with requests hanging
                            </UIButton>
                        </div>}
                    </UIModalHeader>
                    <UIDivider />
                    <UIModalBody>
                        <DataRecoverDetails
                            dataRecover={props.dataRecover}
                        />
                    </UIModalBody>
                    <UIDivider />
                    <UIModalFooter
                        className="flex flex-col gap-1 items-start"
                    >
                        <div
                            className="flex flex-row gap-2 items-center"
                        >
                            <UIButton
                                className="font-mono font-bold"
                                color={closeConfirmed ? "danger" : "warning"}
                                variant={closeConfirmed ? "solid" : "flat"}
                                onPress={() => {
                                    if (closeConfirmed) {

                                        const proceedCount = ActionCentral.getInstance().proceedAllWithError();

                                        openWarningToaster(`${proceedCount} actions proceeded with error`);

                                        onClose();
                                        setCloseConfirmed(false);
                                    } else {
                                        setCloseConfirmed(true);
                                    }
                                }}
                            >
                                {closeConfirmed
                                    ? format.get(DATA_PROFILE.CONFIRM_PROCEED_WITH_ERROR)
                                    : format.get(DATA_PROFILE.PROCEED_WITH_ERROR)}
                            </UIButton>
                        </div>
                        <div
                            className="text-tiny text-gray-500"
                        >
                            {format.get(DATA_PROFILE.PROCEED_WITH_ERROR_DESCRIPTION)}
                        </div>
                    </UIModalFooter>
                </React.Fragment>);
            }}
        </UIModal>
    </div>);
};
export default DataRecover;
