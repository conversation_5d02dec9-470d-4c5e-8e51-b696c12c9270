/**
 * <AUTHOR>
 * @namespace Origin_Actions
 * @description Get Origin Origin Actions
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";

export const createGetOriginOriginActionsAction = (
    origin: ImbricateOriginObject,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Get Origin Origin Actions",
        actionDescription: `Get the origin origin actions for [${origin.origin.uniqueIdentifier}]`,
        executerMetadata: metadata,
        actionPayload: {
            originUniqueIdentifier: origin.origin.uniqueIdentifier,
            originSupportedFeatures: origin.origin.supportedFeatures,
        },
    };
};
