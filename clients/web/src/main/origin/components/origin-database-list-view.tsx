/**
 * <AUTHOR>
 * @namespace Origin_Components
 * @description Origin Database List View
 */

import { StyledCard } from "@imbricate-hummingbird/react-components";
import { UIButton, UICardBody, UICardHeader, UIDivider, UIInput } from "@imbricate-hummingbird/ui";
import { IImbricateOrigin } from "@imbricate/core";
import React, { FC } from "react";
import { useNavigate } from "react-router-dom";
import { clearCache } from "../../common/cache/cache";
import { DATABASE_CACHE_IDENTIFIER } from "../../common/cache/static";
import { ImbricateDatabasesObject, UseDatabasesResponse, UseDatabasesResponseSymbol, useDatabases } from "../../database/hooks/use-databases";

export type OriginDatabaseListViewProps = {

    readonly originName: string;
    readonly origin: IImbricateOrigin;
};

export const OriginDatabaseListView: FC<OriginDatabaseListViewProps> = (
    props: OriginDatabaseListViewProps,
) => {

    const databases: UseDatabasesResponse | UseDatabasesResponseSymbol =
        useDatabases(
            props.origin.uniqueIdentifier,
        );

    const navigate = useNavigate();

    const [newDatabaseName, setNewDatabaseName] = React.useState<string>("");
    const [creating, setCreating] = React.useState<boolean>(false);

    if (typeof databases === "symbol") {
        return null;
    }

    return (<div
        className="flex flex-col gap-2"
    >
        {databases.databases.map((database: ImbricateDatabasesObject) => {
            return (<StyledCard
                key={database.database.uniqueIdentifier}
            >
                <UICardHeader>
                    {database.database.databaseName}
                </UICardHeader>
            </StyledCard>);
        })}
        <StyledCard>
            <UICardHeader>
                <UIInput
                    label="Database Name"
                    value={newDatabaseName}
                    onValueChange={(
                        newValue: string,
                    ) => {
                        setNewDatabaseName(newValue);
                    }}
                />
            </UICardHeader>
            <UIDivider />
            <UICardBody>
                <UIButton
                    variant="flat"
                    isDisabled={newDatabaseName.length === 0 || creating}
                    isLoading={creating}
                    color={newDatabaseName.length === 0 ? "default" : "primary"}
                    onPress={async () => {

                        setCreating(true);

                        const newDatabase = await props.origin.getDatabaseManager()
                            .createDatabase(newDatabaseName, {
                                properties: [],
                            });

                        if (typeof newDatabase === "symbol") {
                            throw new Error("Failed to create database");
                        }

                        clearCache(DATABASE_CACHE_IDENTIFIER);
                        navigate(`/database/${newDatabase.database.uniqueIdentifier}/schema`);
                    }}
                >
                    Create Database
                </UIButton>
            </UICardBody>
        </StyledCard>
    </div>);
};
