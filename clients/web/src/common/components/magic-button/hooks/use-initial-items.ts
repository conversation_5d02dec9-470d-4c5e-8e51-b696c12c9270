/**
 * <AUTHOR>
 * @namespace Common_Components_MagicButton_Hooks
 * @description Use Initial Items
 */

import { useEffect, useRef } from "react";
import { MagicButtonItem } from "../types/button-item";
import { UseMagicButtonResult } from "./use-magic-button";

export const useMagicButtonInitialItems = (
    magicButtonResult: UseMagicButtonResult,
    condition: boolean,
    initialItems: MagicButtonItem[],
    dependencies: React.DependencyList = [],
) => {

    const refKeys = useRef<string[]>([]);

    useEffect(() => {

        if (condition) {

            refKeys.current = initialItems.map((item) => item.itemKey);
            initialItems.forEach((item) => {
                magicButtonResult.addItem(item);
            });
        }

        return () => {

            refKeys.current.forEach((key) => {
                magicButtonResult.removeItem(key);
            });
            refKeys.current = [];
        };
    }, [condition, ...dependencies]);

    return;
};
