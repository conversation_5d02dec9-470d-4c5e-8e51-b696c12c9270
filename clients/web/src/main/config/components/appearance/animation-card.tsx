/**
 * <AUTHOR>
 * @namespace Config_Components
 * @description Animation Card
 */

import { HUMMINGBIRD_ANIMATION_LEVEL, HUMMINGBIRD_PREFERENCE_ITEM, PreferenceController } from "@imbricate-hummingbird/configuration";
import { StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody, UICardHeader, UIDivider, UIRadioGroup, UISpacer } from "@imbricate-hummingbird/ui";
import { FC, useState } from "react";
import { FaPlay } from "react-icons/fa6";
import { useConfigFormat } from "../../internationalization/hook";
import { CONFIG_PROFILE } from "../../internationalization/profile";

export type ConfigAnimationCardProps = {
};

export const ConfigAnimationCard: FC<ConfigAnimationCardProps> = (
    _props: ConfigAnimationCardProps,
) => {

    const configFormat = useConfigFormat();

    const [animationLevel, setAnimationLevel] = useState(() => {

        return PreferenceController.getPreference(
            HUMMINGBIRD_PREFERENCE_ITEM.APPLICATION_APPEARANCE_ANIMATION_LEVEL,
        );
    });

    return (<StyledCard>
        <UICardHeader>
            <FaPlay />
            <UISpacer />
            {configFormat.get(CONFIG_PROFILE.APPLICATION_ANIMATION)}
        </UICardHeader>
        <UIDivider />
        <UICardBody>
            <UIRadioGroup
                label={configFormat.get(CONFIG_PROFILE.APPLICATION_ANIMATION_LEVEL)}
                selected={animationLevel}
                onSelectedChange={(newSelected: string) => {

                    setAnimationLevel(newSelected as HUMMINGBIRD_ANIMATION_LEVEL);

                    PreferenceController.setPreference(
                        HUMMINGBIRD_PREFERENCE_ITEM.APPLICATION_APPEARANCE_ANIMATION_LEVEL,
                        newSelected as HUMMINGBIRD_ANIMATION_LEVEL,
                    );
                }}
                items={[
                    {
                        itemKey: HUMMINGBIRD_ANIMATION_LEVEL.FULL,
                        itemContent: configFormat.get(CONFIG_PROFILE.FULL),
                    },
                    {
                        itemKey: HUMMINGBIRD_ANIMATION_LEVEL.REDUCED,
                        itemContent: configFormat.get(CONFIG_PROFILE.REDUCED),
                    },
                    {
                        itemKey: HUMMINGBIRD_ANIMATION_LEVEL.MINIMAL,
                        itemContent: configFormat.get(CONFIG_PROFILE.MINIMAL),
                    },
                ]}
            />
        </UICardBody>
    </StyledCard>);
};
