/**
 * <AUTHOR>
 * @namespace CrossSection_Components
 * @description Layout Panel
 */

import { GetLayoutedElementsDirection } from "@imbricate-hummingbird/flow-common";
import { UIButton, UIButtonGroup } from "@imbricate-hummingbird/ui";
import { Panel, PanelPosition } from "@xyflow/react";
import { FaGrip, FaGripVertical } from "react-icons/fa6";

export type LayoutPanelProps = {

    readonly onLayout: (
        direction: GetLayoutedElementsDirection,
    ) => void;

    readonly position: PanelPosition;
};

export const LayoutPanel = (
    props: LayoutPanelProps,
) => {

    return (<Panel
        position={props.position}
    >
        <UIButtonGroup
            variant="ghost"
        >
            <UIButton
                isIconOnly
                onPress={() => {
                    props.onLayout("LR");
                }}
            >
                <FaGripVertical />
            </UIButton>
            <UIButton
                isIconOnly
                onPress={() => {
                    props.onLayout("TB");
                }}
            >
                <FaGrip />
            </UIButton>
        </UIButtonGroup>
    </Panel>);
};
