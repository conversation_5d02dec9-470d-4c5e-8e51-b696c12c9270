/**
 * <AUTHOR>
 * @namespace Debug
 * @description Debug Setting Button
 */

import { DebugSetting, DebugSettingListener, DebugSettingsController } from "@imbricate-hummingbird/debug";
import { UIDropdown, createUIButton } from "@imbricate-hummingbird/ui";
import { useEffect, useState } from "react";
import { FaScrewdriverWrench } from "react-icons/fa6";

export const DebugSettingButton = () => {

    const [settings, setSettings] = useState<DebugSetting[]>([]);

    useEffect(() => {

        const settingListener: DebugSettingListener = (
            settings: DebugSetting[],
        ) => {

            setSettings(settings);
        };

        const controller = DebugSettingsController.getInstance();
        controller.listenDebugSettings(settingListener);

        return () => {
            controller.unlistenDebugSettings(settingListener);
        };
    }, []);

    return (<UIDropdown
        placement="top-end"
        trigger={createUIButton({
            isIconOnly: true,
            size: "lg",
            variant: "flat",
            color: "warning",
            children: (<FaScrewdriverWrench
                className="text-large"
            />),
        })}
        categories={[
            {
                categoryKey: "debug-settings",
                title: "Debug Settings",
                items: settings.map((
                    setting: DebugSetting,
                ) => {

                    return {
                        itemKey: `${setting.scope}-${setting.identifier}`,
                        description: setting.description,
                        content: setting.name,
                        onPress: () => {
                            setting.action();
                        },
                    };
                }),
            },
        ]}
    />);
};
