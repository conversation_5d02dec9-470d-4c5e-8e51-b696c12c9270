/**
 * <AUTHOR>
 * @namespace Config_Components
 * @description Reset Card
 */

import { StyledCard } from "@imbricate-hummingbird/react-components";
import { useNavigateConfigConfigurationExportView, useReloadApplication } from "@imbricate-hummingbird/react-navigation";
import { UIAlert, UIButton, UICardBody, UICardHeader, UIDivider, UIModal, UIModalBody, UIModalFooter, UIModalHeader, UISpacer, useUIDisclosure } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { FaFileExport } from "react-icons/fa";
import { LuTimerReset } from "react-icons/lu";
import { useConfigFormat } from "../internationalization/hook";
import { CONFIG_PROFILE } from "../internationalization/profile";
import { resetConfigurationStorage } from "../util/reset-configuration";

export type ConfigResetCardProps = {
};

export const ConfigResetCard: FC<ConfigResetCardProps> = (
    _props: ConfigResetCardProps,
) => {

    const configFormat = useConfigFormat();

    const navigateToExportConfiguration = useNavigateConfigConfigurationExportView();
    const reloadApplication = useReloadApplication();

    const { isOpen, onOpen, onOpenChange } = useUIDisclosure();

    return (<React.Fragment>
        <StyledCard>
            <UICardHeader>
                <LuTimerReset />
                <UISpacer />
                {configFormat.get(CONFIG_PROFILE.RESET_APPLICATION)}
            </UICardHeader>
            <UIDivider />
            <UICardBody>
                <p
                    className="mb-1"
                >
                    {configFormat.get(CONFIG_PROFILE.RESET_APPLICATION_MESSAGE)}
                </p>
                <UIButton
                    radius="sm"
                    variant="flat"
                    color="danger"
                    startContent={<LuTimerReset />}
                    onPress={onOpen}
                >
                    {configFormat.get(CONFIG_PROFILE.RESET_APPLICATION)}
                </UIButton>
            </UICardBody>
        </StyledCard>
        <UIModal
            isOpen={isOpen}
            onOpenChange={onOpenChange}
            size="2xl"
        >
            {(onClose: () => void) => {
                return (<React.Fragment>
                    <UIModalHeader
                        className="flex flex-col gap-1"
                    >
                        {configFormat.get(CONFIG_PROFILE.RESET_APPLICATION)}
                    </UIModalHeader>
                    <UIDivider />
                    <UIModalBody>
                        <UIAlert
                            color="danger"
                            title={configFormat.get(CONFIG_PROFILE.RESET_ALERT_TITLE)}
                            description={<div
                                className="flex flex-col gap-1 items-start"
                            >
                                {configFormat.get(CONFIG_PROFILE.RESET_ALERT_DESCRIPTION)}
                                <UIButton
                                    size="sm"
                                    variant="flat"
                                    startContent={<FaFileExport />}
                                    color="primary"
                                    onPress={() => {
                                        navigateToExportConfiguration({
                                            replace: true,
                                        });
                                    }}
                                >
                                    {configFormat.get(CONFIG_PROFILE.EXPORT_CONFIGURATION)}
                                </UIButton>
                            </div>}
                        />
                        <div className="mb-1">
                            {configFormat.get(CONFIG_PROFILE.RESET_APPLICATION_DESCRIPTION)}
                            <ul className="list-inside">
                                <li>{configFormat.get(CONFIG_PROFILE.RESET_ALERT_DESCRIPTION_POINT_1)}</li>
                                <li>{configFormat.get(CONFIG_PROFILE.RESET_ALERT_DESCRIPTION_POINT_2)}</li>
                                <li>{configFormat.get(CONFIG_PROFILE.RESET_ALERT_DESCRIPTION_POINT_3)}</li>
                            </ul>
                        </div>
                    </UIModalBody>
                    <UIDivider />
                    <UIModalFooter>
                        <UIButton
                            color="primary"
                            variant="solid"
                            onPress={onClose}
                        >
                            {configFormat.get(CONFIG_PROFILE.CANCEL)}
                        </UIButton>
                        <UIButton
                            color="danger"
                            variant="flat"
                            onPress={() => {
                                const result = confirm("Are you sure you want to reset the application?");

                                if (result) {

                                    resetConfigurationStorage();
                                    reloadApplication();
                                } else {

                                    onClose();
                                }
                            }}
                        >
                            {configFormat.get(CONFIG_PROFILE.RESET_APPLICATION_CONFIRM)}
                        </UIButton>
                    </UIModalFooter>
                </React.Fragment>);
            }}
        </UIModal>
    </React.Fragment>);
};
