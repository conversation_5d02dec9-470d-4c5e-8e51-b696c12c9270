/**
 * <AUTHOR>
 * @package UI
 * @namespace Drawer
 * @description Drawer
 */

import { Drawer, DrawerContent } from "@heroui/drawer";
import type { FC, ReactNode } from "react";
import { UIDrawerRadius, UIDrawerSize } from "./types";

export type UIDrawerProps = {

    readonly className?: string;

    readonly size?: UIDrawerSize;
    readonly radius?: UIDrawerRadius;

    readonly isShowCloseButton?: boolean;

    readonly isOpen: boolean;
    readonly onOpenChange: (isOpen: boolean) => void;

    readonly children?: (onClose: () => void) => ReactNode;
};

export const UIDrawer: FC<UIDrawerProps> = (
    props: UIDrawerProps,
) => {


    return (<Drawer
        className={props.className}

        size={props.size}
        radius={props.radius}

        hideCloseButton={!props.isShowCloseButton}

        isOpen={props.isOpen}
        onOpenChange={props.onOpenChange}
    >
        {typeof props.children === "function"
            ? (<DrawerContent>
                {(onClose: () => void) => {

                    const content: ReactNode = props.children
                        ? props.children(onClose)
                        : null;

                    return content;
                }}
            </DrawerContent>)
            : null}
    </Drawer>);
};
