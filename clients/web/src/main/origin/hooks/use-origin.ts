/**
 * <AUTHOR>
 * @namespace Origin
 * @description Use Origin
 */

import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { useEffect, useState } from "react";
import { OldDataCentral } from "../../data/old-data-central";
import { createFindOriginAction } from "../actions/find-origin";

export const useOldOrigin = (
    originUniqueIdentifier: string,
): ImbricateOriginObject | null => {

    const [origin, setOrigin] = useState<ImbricateOriginObject | null>(null);

    useEffect(() => {

        const execute = async () => {

            const origin = await ActionCentral.getInstance().executeAction(
                async (
                    actionIdentifier: string,
                    recordIdentifier: string,
                ) => {
                    return OldDataCentral.getInstance()
                        .getOrigin(
                            originUniqueIdentifier,
                            actionIdentifier,
                            recordIdentifier,
                        );
                },
                createFindOriginAction(
                    originUniqueIdentifier,
                    {
                        executer: import.meta.url,
                    },
                ),
            );

            if (typeof origin === "symbol") {
                return;
            }

            setOrigin(origin);
        };

        execute();
    }, [originUniqueIdentifier]);

    return origin;
};
