/**
 * <AUTHOR>
 * @namespace Origin_Hooks
 * @description Use Origin Origin Actions
 */

import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { IMBRICATE_ORIGIN_FEATURE, ImbricateOriginAction, checkImbricateOriginFeatureSupported } from "@imbricate/core";
import { useEffect, useState } from "react";
import { OldDataCentral } from "../../data/old-data-central";
import { createGetOriginOriginActionsAction } from "../actions/get-origin-origin-actions";

export const useOldOriginOriginActions = (
    origin: ImbricateOriginObject,
): ImbricateOriginAction[] => {

    const [actions, setActions] = useState<ImbricateOriginAction[]>([]);

    useEffect(() => {

        const supportedFeatures = origin.origin.supportedFeatures;

        const isSupported = checkImbricateOriginFeatureSupported(
            supportedFeatures,
            IMBRICATE_ORIGIN_FEATURE.ORIGIN_GET_ORIGIN_ACTIONS,
        );

        if (!isSupported) {
            return;
        }

        const execute = async () => {

            const actions = await ActionCentral.getInstance().executeAction(
                async (
                    actionIdentifier: string,
                    recordIdentifier: string,
                ) => {

                    return OldDataCentral.getInstance().queryOriginActions(
                        origin.origin.uniqueIdentifier,
                        actionIdentifier,
                        recordIdentifier,
                    );
                },
                createGetOriginOriginActionsAction(
                    origin,
                    {
                        executer: import.meta.url,
                    },
                ),
            );

            if (typeof actions === "symbol") {
                return;
            }

            setActions(actions.actions);
        };

        execute();
    }, [origin]);

    return actions;
};
