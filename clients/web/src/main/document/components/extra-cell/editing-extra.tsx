/**
 * <AUTHOR>
 * @namespace Document_Components_ExtraCell
 * @description Editing Extra
 */

import { DocumentGroupOriginData } from "@/common/components/header-group/origin-data/document-origin-data";
import { HEADER_GROUP_GROUP } from "@/common/components/header-group/types";
import { useCommonFormat } from "@/common/internationalization/hook";
import { useNavigateDocumentView } from "@imbricate-hummingbird/react-navigation";
import { DRAWER_TYPE, useDrawerAction } from "@imbricate-hummingbird/react-store";
import { UIButton, UIButtonGroup, UIPopover, createUIButton } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { GoTelescopeFill } from "react-icons/go";
import { IoCheckmarkSharp, IoClose } from "react-icons/io5";
import { MdEdit, MdOutlineInfo } from "react-icons/md";
import { TbLayoutSidebarRightExpandFilled, TbMoodPuzzled } from "react-icons/tb";
import { DocumentEditingController } from "../../controller/editing-controller";
import { ArrangeDocumentsResultItem } from "../../util/arrange-documents";

export type DocumentsTableEditingExtraCellProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly item: ArrangeDocumentsResultItem;
    readonly editingController: DocumentEditingController;
};

export const DocumentsTableEditingExtraCell: FC<DocumentsTableEditingExtraCellProps> = (
    props: DocumentsTableEditingExtraCellProps,
) => {

    const commonFormat = useCommonFormat();

    const items: React.ReactElement[] = [];

    const navigateToDocumentView = useNavigateDocumentView();

    const drawerActions = useDrawerAction();

    if (props.item.editing) {

        items.push(<div
            key="editing-status"
            className="flex gap-1"
        >
            <UIButton
                isIconOnly
                color="success"
                variant="solid"
                size="sm"
                onPress={() => {
                    props.editingController.saveEditingDocument(props.item.document);
                }}
            >
                <IoCheckmarkSharp />
            </UIButton>
            <UIButton
                isIconOnly
                color="danger"
                variant="solid"
                size="sm"
                onPress={() => {
                    props.editingController.cancelEditingDocument(props.item.document);
                }}
            >
                <IoClose />
            </UIButton>
        </div>);

    } else {

        items.push(<div
            key="editing-status"
            className="flex gap-1"
        >
            <UIButtonGroup>
                <UIButton
                    isIconOnly
                    color="secondary"
                    variant="flat"
                    size="sm"
                    onPress={() => {
                        props.editingController.startEditingDocument(props.item.document);
                    }}
                >
                    <MdEdit />
                </UIButton>
                <UIButton
                    isIconOnly
                    color="primary"
                    variant="solid"
                    size="sm"
                    onPress={() => {
                        navigateToDocumentView(
                            props.originUniqueIdentifier,
                            props.databaseUniqueIdentifier,
                            props.item.document.documentUniqueIdentifier,
                        );
                    }}
                >
                    <GoTelescopeFill />
                </UIButton>
                <UIButton
                    isIconOnly
                    color="primary"
                    variant="solid"
                    size="sm"
                    onPress={() => {

                        drawerActions.openDrawer({
                            type: DRAWER_TYPE.DOCUMENT_VIEW,
                            title: "Document View",

                            payload: {
                                databaseUniqueIdentifier: props.databaseUniqueIdentifier,
                                documentUniqueIdentifier: props.item.document.documentUniqueIdentifier,
                            },
                        });
                    }}
                >
                    <TbLayoutSidebarRightExpandFilled />
                </UIButton>
            </UIButtonGroup>
        </div>);
    }

    items.push(<div
        key="document-information"
    >
        <UIPopover
            placement="left"
            contentClassName="p-0"
            trigger={createUIButton({
                isIconOnly: true,
                color: "primary",
                variant: "light",
                size: "sm",
                children: (<MdOutlineInfo />),
            })}
        >
            <DocumentGroupOriginData
                commonFormat={commonFormat}
                group={HEADER_GROUP_GROUP.DOCUMENT}
                payload={{
                    originUniqueIdentifier: props.originUniqueIdentifier,
                    databaseUniqueIdentifier: props.databaseUniqueIdentifier,
                    documentUniqueIdentifier: props.item.document.documentUniqueIdentifier,
                }}
            />
        </UIPopover>
    </div>);

    if (props.item.floatingProperties.length > 0) {

        items.push(<div key="floating-properties">
            <UIPopover
                placement="left"
                trigger={createUIButton({
                    isIconOnly: true,
                    color: "warning",
                    variant: "light",
                    size: "sm",
                    children: (<TbMoodPuzzled />),
                })}
            >
                {props.item.floatingProperties.map((floatingProperty) => {
                    return (<div
                        key={floatingProperty.propertyIdentifier}
                        className="px-1 py-2"
                    >
                        <div className="text-small font-bold">
                            {floatingProperty.propertyIdentifier}
                        </div>
                        <div className="text-tiny">
                            {String(floatingProperty.propertyValue)}
                        </div>
                    </div>);
                })}
            </UIPopover>
        </div>);
    }

    return (<div
        className="flex gap-1"
    >
        {items}
    </div>);
};
