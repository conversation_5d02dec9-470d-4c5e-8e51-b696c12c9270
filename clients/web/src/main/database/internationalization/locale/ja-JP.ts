/**
 * <AUTHOR>
 * @namespace Database_Internationalization_Locale
 * @description Ja-JP
 */

import { DATABASE_PROFILE } from "../profile";

export const jaJPDatabaseProfile: Record<DATABASE_PROFILE, string> = {

    [DATABASE_PROFILE.ACTIONS]: "アクション",
    [DATABASE_PROFILE.ADD_PROPERTY]: "プロパティを追加",
    [DATABASE_PROFILE.ALL]: "すべて",
    [DATABASE_PROFILE.ALLOW_MULTIPLE]: "複数",
    [DATABASE_PROFILE.BINARY]: "バイナリ",
    [DATABASE_PROFILE.BOOLEAN]: "ブール",
    [DATABASE_PROFILE.CREATE_DOCUMENT]: "ドキュメント作成",
    [DATABASE_PROFILE.CREATE_DOCUMENT_DESCRIPTION]: "新しいドキュメントを作成します",
    [DATABASE_PROFILE.DATABASE]: "データベース",
    [DATABASE_PROFILE.DATABASE_SCHEMA]: "データベーススキーマ",
    [DATABASE_PROFILE.DATE]: "日付",
    [DATABASE_PROFILE.DISPLAY_ALL]: "すべて表示",
    [DATABASE_PROFILE.DISPLAY_ALL_DESCRIPTION]: "すべてのプロパティを表示します",
    [DATABASE_PROFILE.DOCUMENT]: "ドキュメント",
    [DATABASE_PROFILE.DOCUMENT_LIST]: "ドキュメントリスト",
    [DATABASE_PROFILE.EDIT_DATABASE_SCHEMA]: "データベーススキーマを編集",
    [DATABASE_PROFILE.EDIT_DATABASE_SCHEMA_DESCRIPTION]: "データベーススキーマエディタに移動します",
    [DATABASE_PROFILE.FLOATING_PROPERTIES]: "フローティングプロパティ",
    [DATABASE_PROFILE.FLOATING_PROPERTY_DESCRIPTION]: "データベーススキーマで定義されていないプロパティです",
    [DATABASE_PROFILE.JSON]: "JSON",
    [DATABASE_PROFILE.IMBRISCRIPT]: "ImbriScript",
    [DATABASE_PROFILE.ITEM_$1_PER_PAGE]: "{} ドキュメント / ページ",
    [DATABASE_PROFILE.ITEM_PER_PAGE_FOR_THIS_DATABASE]: "Items per page for this database",
    [DATABASE_PROFILE.LABEL]: "ラベル",
    [DATABASE_PROFILE.LABEL_COLOR]: "ラベルの色",
    [DATABASE_PROFILE.LABEL_NAME]: "ラベル名",
    [DATABASE_PROFILE.MARKDOWN]: "マークダウン",
    [DATABASE_PROFILE.NUMBER]: "数値",
    [DATABASE_PROFILE.REFRESH_DOCUMENT_LIST]: "ドキュメントリストを更新",
    [DATABASE_PROFILE.REFRESH_DOCUMENT_LIST_DESCRIPTION]: "キャッシュを無効化し、ドキュメントリストを更新します",
    [DATABASE_PROFILE.PRIMARY_KEY]: "主キー",
    [DATABASE_PROFILE.PROPERTIES_FOR_THIS_DATABASE]: "このデータベースのプロパティ",
    [DATABASE_PROFILE.PROPERTY_NAME]: "プロパティ名",
    [DATABASE_PROFILE.PROPERTY_TYPE]: "プロパティの型",
    [DATABASE_PROFILE.PROPERTY_UNIQUE_IDENTIFIER]: "プロパティの一意の識別子",
    [DATABASE_PROFILE.REFERENCE]: "参照",
    [DATABASE_PROFILE.SAVE]: "保存",
    [DATABASE_PROFILE.SELECT_DATABASE]: "データベースを選択",
    [DATABASE_PROFILE.STRING]: "文字列",
    [DATABASE_PROFILE.VIEW_DOCUMENTS]: "ドキュメントを表示",
    [DATABASE_PROFILE.VIEW_DOCUMENTS_DESCRIPTION]: "ドキュメントリストビューに移動します",
};
