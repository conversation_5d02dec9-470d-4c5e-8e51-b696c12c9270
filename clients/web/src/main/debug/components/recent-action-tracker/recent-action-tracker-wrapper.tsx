/**
 * <AUTHOR>
 * @namespace Debug_Components_RecentActionTracker
 * @description Recent Action Tracker Wrapper
 */

import { useDebugMode } from "@imbricate-hummingbird/debug";
import { LazyLoadComponent } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const RecentActionTracker = LazyLoadComponent(
    () => import("./recent-action-tracker"),
    "Recent Action Tracker",
);

export const RecentActionTrackerWrapper: FC = () => {

    const { isDebugMode } = useDebugMode();

    if (!isDebugMode) {
        return null;
    }

    return (<React.Suspense>
        <RecentActionTracker />
    </React.Suspense>);
};
