/**
 * <AUTHOR>
 * @package UI
 * @namespace Spinner
 * @description Spinner
 */

import { Spinner } from "@heroui/spinner";
import { UIColor, UISize } from "../common/types";
import { UISpinnerVariant } from "./types";

export type UISpinnerProps = {

    readonly className?: string;

    readonly color?: UIColor;
    readonly size?: UISize;
    readonly variant?: UISpinnerVariant;

    readonly label?: React.ReactNode;
};

export const UISpinner: React.FC<UISpinnerProps> = (
    props: UISpinnerProps,
) => {

    return (<Spinner
        className={props.className}

        color={props.color}
        size={props.size}
        variant={props.variant}

        label={props.label as any}
    />);
};
