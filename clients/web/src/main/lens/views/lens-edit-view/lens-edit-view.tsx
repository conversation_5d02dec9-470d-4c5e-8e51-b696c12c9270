/**
 * <AUTHOR>
 * @namespace Lens
 * @description Lens Edit View
 */

import { HeaderGroup } from "@/common/components/header-group/header-group";
import { HEADER_GROUP_GROUP } from "@/common/components/header-group/types";
import { MagicButton } from "@/common/components/magic-button/magic-button";
import { HUMMINGBIRD_MANAGED_CONFIG_ITEM, LENS_CONFIG_SOURCE, LensConfig, LensConfigItem, ManagedConfigController } from "@imbricate-hummingbird/configuration";
import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { getRouteLensView } from "@imbricate-hummingbird/navigation-core";
import { useAsyncTitle } from "@imbricate-hummingbird/react-common";
import { StyledCard } from "@imbricate-hummingbird/react-components";
import { useNavigateDocumentView, useNavigateLensView } from "@imbricate-hummingbird/react-navigation";
import { LensSlice } from "@imbricate-hummingbird/react-store";
import { UIButton, UICardBody, UICardFooter, UICardHeader, UIDivider, UIInput, UINavbar, UISpacer, UITextarea } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { RiCameraLensFill } from "react-icons/ri";
import { useDispatch } from "react-redux";
import { Link } from "react-router-dom";
import { useWideOrigin } from "../../../origin/hooks/use-wide-origin";
import { useLensEditViewMagicButton } from "../../hooks/lens-edit-view/use-lens-edit-view-magic-button";
import { useLensFormat } from "../../internationalization/hook";
import { LENS_PROFILE } from "../../internationalization/profile";

export type LensEditViewProps = {
    readonly targetLens: LensConfigItem<LENS_CONFIG_SOURCE>;
};

export const LensEditView: FC<LensEditViewProps> = (
    props: LensEditViewProps,
) => {

    const wideOrigin = useWideOrigin(props.targetLens.target.databaseUniqueIdentifier);

    const lensFormat = useLensFormat();

    const magicButtonResult = useLensEditViewMagicButton({
        targetLens: props.targetLens,
    });

    const [edit, setEdit] = React.useState<boolean>(false);
    const [lensName, setLensName] = React.useState<string>(
        props.targetLens.lensName,
    );

    const navigateToLensView = useNavigateLensView();
    const navigateToDocumentView = useNavigateDocumentView();

    const dispatch = useDispatch();

    useAsyncTitle(
        () => Boolean(props.targetLens),
        () => {
            return [
                props.targetLens.lensName,
                "Edit",
                lensFormat.get(LENS_PROFILE.LENS),
            ];
        },
        [props.targetLens.lensIdentifier, isFormatLoading(lensFormat)],
    );

    return (<div
        className="flex flex-col gap-2 pb-4 overflow-auto h-full min-h-full"
    >
        <UINavbar
            isFullWidth
            isBordered
            startContent={<HeaderGroup
                group={HEADER_GROUP_GROUP.LENS}
                payload={{
                    lensIdentifier: props.targetLens.lensIdentifier,
                }}
            />}
            coreClassName="flex flex-col gap-0"
            coreContent={<React.Fragment>
                <p className="font-bold text-xl">
                    <span
                        className="font-mono font-light"
                    >
                        {lensFormat.get(LENS_PROFILE.EDITING_LENS)}
                    </span> {props.targetLens.lensName}
                </p>
                <Link
                    className="h-2 text-tiny flex gap-1 items-center"
                    to={getRouteLensView(props.targetLens.lensIdentifier)}
                    replace
                >
                    <RiCameraLensFill />
                    <div>
                        {lensFormat.get(LENS_PROFILE.VIEW_LENS)}
                    </div>
                </Link>
            </React.Fragment>}
            endContent={<MagicButton
                magicButtonResult={magicButtonResult}
            />}
        />
        <div
            className="pr-2 h-full"
        >
            <StyledCard>
                <UICardHeader>
                    {lensFormat.get(LENS_PROFILE.EDIT_LENS)}
                </UICardHeader>
                <UIDivider />
                <UICardBody
                    className="flex flex-col gap-2"
                >
                    <UIInput
                        label={lensFormat.get(LENS_PROFILE.LENS_IDENTIFIER)}
                        placeholder={lensFormat.get(LENS_PROFILE.LENS_IDENTIFIER)}
                        value={props.targetLens.lensIdentifier}
                        isDisabled
                    />
                    <UIInput
                        label={lensFormat.get(LENS_PROFILE.LENS_SOURCE)}
                        placeholder={lensFormat.get(LENS_PROFILE.LENS_SOURCE)}
                        value={props.targetLens.source}
                        isDisabled
                    />
                    <UIInput
                        label={lensFormat.get(LENS_PROFILE.LENS_NAME)}
                        placeholder={lensFormat.get(LENS_PROFILE.LENS_NAME)}
                        value={lensName}
                        onValueChange={(
                            newValue: string,
                        ) => {
                            setLensName(newValue);
                            setEdit(true);
                        }}
                    />
                    <UITextarea
                        label={lensFormat.get(LENS_PROFILE.LENS_TARGET)}
                        placeholder={lensFormat.get(LENS_PROFILE.LENS_TARGET)}
                        value={JSON.stringify(props.targetLens.target, null, 2)}
                        isDisabled
                    />
                </UICardBody>
                <UIDivider />
                <UICardFooter
                    className="flex flex-row gap-2"
                >
                    <UIButton
                        variant="flat"
                        color="primary"
                        isDisabled={lensName.length === 0 || !edit}
                        onPress={() => {

                            const current: LensConfig = ManagedConfigController.getInstance()
                                .getManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.LENS_CONFIG);

                            const newItem: LensConfigItem<LENS_CONFIG_SOURCE.IMBRISCRIPT> = {

                                lensIdentifier: props.targetLens.lensIdentifier,

                                lensName,
                                source: props.targetLens.source,
                                target: props.targetLens.target,
                            };

                            const updatedItem = current.items.map((item: LensConfigItem<LENS_CONFIG_SOURCE>) => {
                                if (item.lensIdentifier === props.targetLens.lensIdentifier) {
                                    return newItem;
                                }
                                return item;
                            });

                            const updated: LensConfig = {
                                ...current,
                                items: updatedItem,
                            };

                            ManagedConfigController.getInstance()
                                .setManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.LENS_CONFIG, updated);

                            dispatch(
                                LensSlice.actions.setLensConfig(updated),
                            );

                            navigateToLensView(newItem.lensIdentifier);
                        }}
                    >
                        {lensFormat.get(LENS_PROFILE.UPDATE_LENS)}
                    </UIButton>
                    <UISpacer
                        isFlexFill
                    />
                    <UIButton
                        variant="solid"
                        color="primary"
                        isDisabled={!wideOrigin}
                        onPress={() => {
                            navigateToDocumentView(
                                wideOrigin!.origin.uniqueIdentifier,
                                props.targetLens.target.databaseUniqueIdentifier,
                                props.targetLens.target.documentUniqueIdentifier,
                            );
                        }}
                    >
                        {lensFormat.get(LENS_PROFILE.NAVIGATE_TO_LENS_SOURCE)}
                    </UIButton>
                </UICardFooter>
            </StyledCard>
        </div>
    </div>);
};
