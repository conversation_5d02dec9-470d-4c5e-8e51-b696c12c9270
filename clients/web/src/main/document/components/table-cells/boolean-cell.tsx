/**
 * <AUTHOR>
 * @namespace Document_Components_TableCells
 * @description Boolean Cell
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UICheckbox } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { DocumentTableCellContent } from "./cell-content";

export type DocumentTableBooleanCellProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BOOLEAN>;
    readonly getEditingProperty: () => TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BOOLEAN> | undefined;
    readonly updateEditingProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BOOLEAN>) => void;
    readonly editing: boolean;
};

export const DocumentTableBooleanCell: FC<DocumentTableBooleanCellProps> = (
    props: DocumentTableBooleanCellProps,
) => {

    if (props.editing) {

        const updatedProperty = props.getEditingProperty();

        if (typeof updatedProperty === "undefined") {
            throw new Error("[Imbricate] Updated property value not found");
        }

        return (<UICheckbox
            size="lg"
            isSelected={updatedProperty.value}
            onValueChange={(newValue: boolean) => {

                props.updateEditingProperty({
                    ...updatedProperty,
                    value: newValue,
                });
            }}
        />);
    }

    return (<DocumentTableCellContent
        schemaType={IMBRICATE_PROPERTY_TYPE.BOOLEAN}
        property={props.property}
        render={(
            propertyDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> | null,
        ) => {

            const propertyValue: boolean | undefined = propertyDraft === null
                ? undefined
                : propertyDraft.value as boolean;

            return (<UICheckbox
                size="lg"
                isSelected={propertyValue}
                isDisabled
            />);
        }}
    />);
};
