/**
 * <AUTHOR>
 * @namespace Landing_Mobile
 * @description Mobile Landing
 */

import "../../index.css";

import { useTitle } from "@imbricate-hummingbird/react-common";
import { useControlledTheme } from "@imbricate-hummingbird/theme-core";
import { UIButton, UIDivider } from "@imbricate-hummingbird/ui";
import { setSkippedMobileWarning } from "../../common/hooks/use-is-mobile";
import { ConfigBrandBanner } from "../../config/components/brand-banner";

// LAZY LOAD ONLY
const MobileLanding = () => {

    useTitle([
        "Mobile",
    ], []);

    const theme = useControlledTheme();

    return (<main
        className={`${theme.theme} text-foreground bg-background h-dvh box-border max-h-screen overflow-hidden`}
    >
        <div
            className="flex h-full min-h-0 min-w-0"
        >
            <ConfigBrandBanner />
            <div
                className="flex-1 flex flex-col items-center justify-center h-full overflow-auto min-h-0 min-w-0"
            >
                <div
                    className="px-3"
                >
                    <h2 className="text-2xl font-mono font-bold">
                        IMBRICATE<br />HUMMINGBIRD
                    </h2>
                    <UIDivider
                        className="my-2"
                    />
                    <p className="text-medium">
                        This application is not designed to provide support for mobile devices. Please use a desktop device to access the full features.
                    </p>
                    <UIDivider
                        className="my-2"
                    />
                    <UIButton
                        color="primary"
                        variant="flat"
                        radius="sm"
                        isFullWidth
                        onPress={() => {
                            setSkippedMobileWarning();
                        }}
                    >
                        Continue anyway
                    </UIButton>
                </div>
            </div>
        </div>
    </main>);
};

export default MobileLanding;
