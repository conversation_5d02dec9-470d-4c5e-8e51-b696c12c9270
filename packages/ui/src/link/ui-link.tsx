/**
 * <AUTHOR>
 * @package UI
 * @namespace Link
 * @description Link
 */

import { Link } from "@heroui/link";
import React from "react";

export type UILinkProps = {

    readonly className?: string;

    readonly href?: string;
    readonly hrefInNewWindow?: boolean;

    readonly isExternal?: boolean;

    readonly showAnchorIcon?: boolean;

    readonly children?: React.ReactNode;
};

export const UILink: React.FC<UILinkProps> = (
    props: UILinkProps,
) => {

    return (<Link
        className={props.className}

        href={props.href}
        target={props.hrefInNewWindow ? "_blank" : undefined}

        isExternal={props.isExternal}

        showAnchorIcon={props.showAnchorIcon}
    >
        {props.children}
    </Link>);
};
