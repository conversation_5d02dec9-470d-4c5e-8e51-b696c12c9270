/**
 * <AUTHOR>
 * @namespace Debug_Components_DebugActionBarTab
 * @description Debug Action Bar Tab
 */

import { HUMMINGBIRD_MANAGED_CONFIG_ITEM, ManagedConfigController } from "@imbricate-hummingbird/configuration";
import { UITabs } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { FaCode } from "react-icons/fa";
import { FaDatabase, FaIcons, FaPalette, FaTags } from "react-icons/fa6";
import { HiVariable } from "react-icons/hi";
import { IoMdAnalytics } from "react-icons/io";
import { MdOutlineRunningWithErrors } from "react-icons/md";
import { RiCheckboxMultipleBlankFill } from "react-icons/ri";
import { TiInfo } from "react-icons/ti";
import { AnalyticsQuickAccess } from "./analytics/analytics-quick-access";
import { CachedEntriesQuickAccess } from "./cached-entries/cached-entries-quick-access";
import { EnvironmentVariablesQuickAccess } from "./environment-variables/environment-variables-quick-access";
import { OptimusPrimeQuickAccess } from "./optimus-prime/optimus-prime-quick-access";
import { ResultManagerQuickAccess } from "./result-manager/result-manager-quick-access";
import { RunningRecentItemQuickAccess } from "./running-actions/running-recent-item-quick-access";
import { TagsManagerQuickAccess } from "./tags-manager/tags-manager-quick-access";
import { ThemeQuickAccess } from "./theme/theme-quick-access";
import { ApplicationVersionQuickAccess } from "./version/application-version-quick-access";
import { WorkerStatusQuickAccess } from "./worker-status/worker-status-quick-access";

export const DebugActionBarTab: FC = () => {

    const [openedQuickAccess, setOpenedQuickAccess] = React.useState<string>(() => {

        const lastOpenedQuickAccess = ManagedConfigController.getManagedConfig(
            HUMMINGBIRD_MANAGED_CONFIG_ITEM.LAST_OPENED_QUICK_ACCESS,
        );

        if (lastOpenedQuickAccess) {
            return lastOpenedQuickAccess;
        }
        return "version";
    });

    return (<UITabs
        tabListClassName="self-end"
        placement="end"
        variant="solid"
        isDestroyInactiveTabPanel={true}
        defaultSelectedKey={openedQuickAccess}
        onSelectionChange={(key) => {

            setOpenedQuickAccess(key as string);
            ManagedConfigController.setManagedConfig(
                HUMMINGBIRD_MANAGED_CONFIG_ITEM.LAST_OPENED_QUICK_ACCESS,
                key as string,
            );
        }}
        tabs={[
            {
                tabKey: "version",
                tabTitle: (<TiInfo />),
                panel: (<div
                    className="h-full flex items-end"
                >
                    <ApplicationVersionQuickAccess />
                </div>),
            },
            {
                tabKey: "cached-entries",
                tabTitle: (<FaDatabase />),
                panel: (<div
                    className="h-full flex items-end"
                >
                    <CachedEntriesQuickAccess />
                </div>),
            },
            {
                tabKey: "optimus-prime",
                tabTitle: (<FaIcons />),
                panel: (<div
                    className="h-full flex items-end"
                >
                    <OptimusPrimeQuickAccess />
                </div>),
            },
            {
                tabKey: "theme",
                tabTitle: (<FaPalette />),
                panel: (<div
                    className="h-full flex items-end"
                >
                    <ThemeQuickAccess />
                </div>),
            },
            {
                tabKey: "environment-variables",
                tabTitle: (<HiVariable />),
                panel: (<div
                    className="h-full flex items-end"
                >
                    <EnvironmentVariablesQuickAccess />
                </div>),
            },
            {
                tabKey: "result-manager",
                tabTitle: (<FaCode />),
                panel: (<div
                    className="h-full flex items-end"
                >
                    <ResultManagerQuickAccess />
                </div>),
            },
            {
                tabKey: "tags-manager",
                tabTitle: (<FaTags />),
                panel: (<div
                    className="h-full flex items-end"
                >
                    <TagsManagerQuickAccess />
                </div>),
            },
            {
                tabKey: "worker",
                tabTitle: (<RiCheckboxMultipleBlankFill />),
                panel: (<div
                    className="h-full flex items-end"
                >
                    <WorkerStatusQuickAccess />
                </div>),
            },
            {
                tabKey: "running-recent-item",
                tabTitle: (<MdOutlineRunningWithErrors />),
                panel: (<div
                    className="h-full flex items-end"
                >
                    <RunningRecentItemQuickAccess />
                </div>),
            },
            {
                tabKey: "analytics",
                tabTitle: (<IoMdAnalytics />),
                panel: (<div
                    className="h-full flex items-end"
                >
                    <AnalyticsQuickAccess />
                </div>),
            },
        ]}
    />);
};
