/**
 * <AUTHOR>
 * @namespace Main_Edit_Hooks_UseScrollBarMagicButtonItem
 * @description Use Scroll Bar Magic Button Item
 */

import { UseMagicButtonResult } from "@/common/components/magic-button/hooks/use-magic-button";
import { HUMMINGBIRD_PREFERENCE_ITEM, PreferenceController } from "@imbricate-hummingbird/configuration";
import { isDefaultEmptyFormat } from "@imbricate-hummingbird/internationalization";
import { MONACO_REGULAR_SCROLL_BAR_CONFIG, createMonacoMinimapConfig } from "@imbricate-hummingbird/monaco-core";
import { SudooFormat } from "@sudoo/internationalization";
import * as monaco from "monaco-editor";
import { RefObject, useEffect, useState } from "react";
import { FaMap, FaScroll } from "react-icons/fa6";
import { EDIT_PROFILE } from "../internationalization/profile";

export type UseScrollBarMagicButtonItemProps = {

    readonly editorRef: RefObject<monaco.editor.IStandaloneCodeEditor | null>;

    readonly editorFormat: SudooFormat<EDIT_PROFILE>;
    readonly magicButtonResult: UseMagicButtonResult;
};

export const useScrollBarMagicButtonItem = (
    props: UseScrollBarMagicButtonItemProps,
) => {

    const [showingMinimap, setShowingMinimap] = useState(() => {

        const showMinimap = PreferenceController.getPreference(
            HUMMINGBIRD_PREFERENCE_ITEM.EDITOR_MONACO_SCROLLBAR_MINIMAP_ENABLEMENT,
        );

        return showMinimap;
    });

    useEffect(() => {

        if (isDefaultEmptyFormat(props.editorFormat)) {
            return;
        }

        if (showingMinimap) {

            props.magicButtonResult.addItem({
                order: 2,
                categoryKey: "editor",
                itemKey: "use-regular-scroll-bar",
                title: props.editorFormat.get(EDIT_PROFILE.USE_REGULAR_SCROLL_BAR),
                description: props.editorFormat.get(EDIT_PROFILE.USE_REGULAR_SCROLL_BAR_DESCRIPTION),
                icon: FaScroll,
                onPress: () => {

                    const editor = props.editorRef.current;

                    if (!editor) {
                        return;
                    }

                    editor.updateOptions(MONACO_REGULAR_SCROLL_BAR_CONFIG);
                    setShowingMinimap(false);

                    PreferenceController.setPreference(
                        HUMMINGBIRD_PREFERENCE_ITEM.EDITOR_MONACO_SCROLLBAR_MINIMAP_ENABLEMENT,
                        false,
                    );
                },
            });
        }

        if (!showingMinimap) {

            props.magicButtonResult.addItem({
                order: 2,
                categoryKey: "editor",
                itemKey: "use-minimap-scroll-bar",
                title: props.editorFormat.get(EDIT_PROFILE.USE_MINIMAP_SCROLL_BAR),
                description: props.editorFormat.get(EDIT_PROFILE.USE_MINIMAP_SCROLL_BAR_DESCRIPTION),
                icon: FaMap,
                onPress: () => {

                    const editor = props.editorRef.current;

                    if (!editor) {
                        return;
                    }

                    editor.updateOptions(createMonacoMinimapConfig({
                        size: PreferenceController.getPreference(
                            HUMMINGBIRD_PREFERENCE_ITEM.EDITOR_MONACO_SCROLLBAR_MINIMAP_SIZE,
                        ),
                    }));
                    setShowingMinimap(true);

                    PreferenceController.setPreference(
                        HUMMINGBIRD_PREFERENCE_ITEM.EDITOR_MONACO_SCROLLBAR_MINIMAP_ENABLEMENT,
                        true,
                    );
                },
            });
        }

        return () => {
            props.magicButtonResult.removeItem("use-regular-scroll-bar");
            props.magicButtonResult.removeItem("use-minimap-scroll-bar");
        };
    }, [
        typeof props.editorRef.current,
        props.editorRef.current === null,
        isDefaultEmptyFormat(props.editorFormat),
    ]);
};
