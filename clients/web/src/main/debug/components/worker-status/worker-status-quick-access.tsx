/**
 * <AUTHOR>
 * @namespace Debug_Components_WorkerStatus
 * @description Worker Status Quick Access
 */

import { WorkerTextWorkerManager } from "@imbricate-hummingbird/editor-text-web-worker";
import { WorkerOriginWorkerManager } from "@imbricate-hummingbird/origin-central-web-worker";
import { StyledCard } from "@imbricate-hummingbird/react-components";
import { WorkerScriptWorkerManager } from "@imbricate-hummingbird/script-central-web-worker";
import { UIButton, UICardBody, UIChip, UIDivider } from "@imbricate-hummingbird/ui";
import { WORKER_WEB_WORKER } from "@imbricate-hummingbird/web-worker-core";
import React, { useCallback, useEffect, useState } from "react";
import { FaSync } from "react-icons/fa";
import { FaPersonRunning } from "react-icons/fa6";

export const WorkerStatusQuickAccess: React.FC = () => {

    const [isOriginWorkerInitialized, setIsOriginWorkerInitialized] = useState<boolean>(false);
    const [isScriptWorkerInitialized, setIsScriptWorkerInitialized] = useState<boolean>(false);
    const [isTextWorkerInitialized, setIsTextWorkerInitialized] = useState<boolean>(false);

    const refresh = useCallback(() => {

        const isOriginWorkerInitialized = WorkerOriginWorkerManager.isInitialized();
        setIsOriginWorkerInitialized(isOriginWorkerInitialized);

        const isScriptWorkerInitialized = WorkerScriptWorkerManager.isInitialized();
        setIsScriptWorkerInitialized(isScriptWorkerInitialized);

        const isTextWorkerInitialized = WorkerTextWorkerManager.isInitialized();
        setIsTextWorkerInitialized(isTextWorkerInitialized);
    }, []);

    useEffect(() => {
        refresh();
    }, []);

    return (<StyledCard>
        <UICardBody
            className="flex flex-col gap-1 p-1"
        >
            <UIButton
                size="sm"
                radius="sm"
                color="secondary"
                variant="flat"
                startContent={<FaSync />}
                onPress={refresh}
            >
                Refresh
            </UIButton>
        </UICardBody>
        <UIDivider />
        <UICardBody
            className="p-2 px-4 flex flex-col gap-2 font-mono"
        >
            <div>
                <div
                    className="flex flex-row gap-2"
                >
                    <UIChip
                        className="font-mono pl-2"
                        color={isOriginWorkerInitialized ? "success" : "warning"}
                        size="sm"
                        startContent={<FaPersonRunning />}
                    >
                        {WORKER_WEB_WORKER.ORIGIN_WORKER}
                    </UIChip>
                    {isOriginWorkerInitialized ? "Initialized" : "Not Initialized"}
                </div>
                {isOriginWorkerInitialized && <div
                    className="text-tiny text-default-500"
                >
                    {WorkerOriginWorkerManager.getUsingInstances().join(", ")}
                </div>}
            </div>
            <div>
                <div
                    className="flex flex-row gap-2"
                >
                    <UIChip
                        className="font-mono pl-2"
                        color={isScriptWorkerInitialized ? "success" : "warning"}
                        size="sm"
                        startContent={<FaPersonRunning />}
                    >
                        {WORKER_WEB_WORKER.SCRIPT_WORKER}
                    </UIChip>
                    {isScriptWorkerInitialized ? "Initialized" : "Not Initialized"}
                </div>
                {isScriptWorkerInitialized && <div
                    className="text-tiny text-default-500"
                >
                    {WorkerScriptWorkerManager.getUsingInstances().join(", ")}
                </div>}
            </div>
            <div>
                <div
                    className="flex flex-row gap-2"
                >
                    <UIChip
                        className="font-mono pl-2"
                        color={isTextWorkerInitialized ? "success" : "warning"}
                        size="sm"
                        startContent={<FaPersonRunning />}
                    >
                        {WORKER_WEB_WORKER.TEXT_WORKER}
                    </UIChip>
                    {isTextWorkerInitialized ? "Initialized" : "Not Initialized"}
                </div>
                {isTextWorkerInitialized && <div
                    className="text-tiny text-default-500"
                >
                    {WorkerTextWorkerManager.getUsingInstances().join(", ")}
                </div>}
            </div>
        </UICardBody>
    </StyledCard>);
};
