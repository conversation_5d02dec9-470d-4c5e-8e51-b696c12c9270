/**
 * <AUTHOR>
 * @package UI
 * @namespace Tabs
 * @description Tabs
 */

import { Tab, Tabs } from "@heroui/tabs";
import React, { Key } from "react";
import { UITabsItem, UITabsPlacement, UITabsVariant } from "./types";
import { UIColor } from "../common/types";

export type UITabsProps = {

    readonly ariaLabel?: string;

    readonly className?: string;
    readonly tabListClassName?: string;
    readonly tabContentClassName?: string;

    readonly color?: UIColor;
    readonly placement?: UITabsPlacement;
    readonly variant?: UITabsVariant;

    readonly isFullWidth?: boolean;
    readonly isDestroyInactiveTabPanel?: boolean;

    readonly defaultSelectedKey?: string;
    readonly selectedKey?: string;
    readonly onSelectionChange?: (newSelectedKey: string) => void;

    readonly tabs: UITabsItem[];
};

export const UITabs: React.FC<UITabsProps> = (
    props: UITabsProps,
) => {

    return (<Tabs

        className={props.className}

        color={props.color}
        placement={props.placement}
        variant={props.variant}

        fullWidth={props.isFullWidth}
        destroyInactiveTabPanel={props.isDestroyInactiveTabPanel}

        defaultSelectedKey={props.defaultSelectedKey}
        selectedKey={props.selectedKey}
        onSelectionChange={typeof props.onSelectionChange === "function"
            ? (newKey: Key) => {
                props.onSelectionChange!(String(newKey));
            } : undefined}
    >
        {props.tabs.map((
            tab: UITabsItem,
        ) => {

            return (<Tab
                key={tab.tabKey}
                title={tab.tabTitle}
            >
                {tab.panel}
            </Tab>);
        })}
    </Tabs>);
};
