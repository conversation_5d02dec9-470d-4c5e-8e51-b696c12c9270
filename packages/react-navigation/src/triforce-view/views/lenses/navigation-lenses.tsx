/**
 * <AUTHOR>
 * @namespace Navigation
 * @description Navigation Lenses
 */

import { LENS_CONFIG_SOURCE, LensConfigItem, getUserFriendlyLensSourceName } from "@imbricate-hummingbird/configuration";
import { getRouteLensNewView, getRouteLensView } from "@imbricate-hummingbird/navigation-core";
import { DRAWER_TYPE, useDrawerAction, useLensConfig } from "@imbricate-hummingbird/react-store";
import { FC } from "react";
import { FaPlus } from "react-icons/fa";
import { TbLayoutSidebarRightExpandFilled } from "react-icons/tb";
import { NavigationActionButton } from "../../../components/action-button";
import { useNavigationFormat } from "../../../internationalization/hook";
import { NAVIGATION_PROFILE } from "../../../internationalization/profile";

export type NavigationLensesProps = {
};

export const NavigationLenses: FC<NavigationLensesProps> = (
    _props: NavigationLensesProps,
) => {

    const navigationFormat = useNavigationFormat();

    const lensConfig = useLensConfig();

    const drawerActions = useDrawerAction();

    return (<div
        className="flex flex-col gap-1"
    >
        <NavigationActionButton
            Icon={FaPlus}
            title={navigationFormat.get(NAVIGATION_PROFILE.ADD_LENS)}
            description={navigationFormat.get(NAVIGATION_PROFILE.ADD_LENS_DESCRIPTION)}
            href={getRouteLensNewView()}
            color="primary"
        />
        {lensConfig.items.map((
            lens: LensConfigItem<LENS_CONFIG_SOURCE>,
        ) => {

            return (<NavigationActionButton
                key={lens.lensIdentifier}
                title={lens.lensName}
                description={getUserFriendlyLensSourceName(lens.source)}
                href={getRouteLensView(lens.lensIdentifier)}
                endingButtonIcon={TbLayoutSidebarRightExpandFilled}
                onEndingButtonPress={() => {

                    drawerActions.openDrawer({
                        type: DRAWER_TYPE.LENS_VIEW,
                        title: navigationFormat.get(NAVIGATION_PROFILE.LENS_VIEW),

                        payload: {
                            lensIdentifier: lens.lensIdentifier,
                        },
                    });
                }}
            />);
        })}
    </div>);
};
