/**
 * <AUTHOR>
 * @namespace Main_Recent_Actions
 * @description Get Document
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createGetDocumentForRecentAction = (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {

        actionName: "Get Document for Recent",
        actionDescription: `Get the document from origin [${originUniqueIdentifier}] with database [${databaseUniqueIdentifier}] and document [${documentUniqueIdentifier}]`,
        executerMetadata: metadata,
        actionPayload: {
            originUniqueIdentifier,
            databaseUniqueIdentifier,
            documentUniqueIdentifier,
        },
    };
};
