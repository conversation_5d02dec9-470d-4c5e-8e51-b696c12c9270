/**
 * <AUTHOR>
 * @namespace Drawer_Components_Lens_View
 * @description Lens View Fetcher
 */

import { LENS_CONFIG_SOURCE, LensConfig, LensConfigItem } from "@imbricate-hummingbird/configuration";
import { useLensConfig } from "@imbricate-hummingbird/react-store";
import { FC } from "react";
import { ErrorScreen } from "../../../common/components/error-screen";
import { DrawerLensView } from "./lens-view";

export type DrawerLensViewFetcherProps = {

    readonly lensIdentifier: string;
};

// LAZY LOAD ONLY
const DrawerLensViewFetcher: FC<DrawerLensViewFetcherProps> = (
    props: DrawerLensViewFetcherProps,
) => {

    const lensConfig: LensConfig = useLensConfig();

    const targetLens: LensConfigItem<LENS_CONFIG_SOURCE> | undefined =
        lensConfig.items.find((lens: LensConfigItem<LENS_CONFIG_SOURCE>) => {
            return lens.lensIdentifier === props.lensIdentifier;
        });

    if (!targetLens) {

        return (<ErrorScreen
            color="warning"
            title="Unable to load lens"
            message={<div>
                <div>Lens: {props.lensIdentifier}</div>
            </div>}
        />);
    }

    return (<DrawerLensView
        targetLens={targetLens}
    />);
};
export default DrawerLensViewFetcher;
