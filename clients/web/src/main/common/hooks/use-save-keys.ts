/**
 * <AUTHOR>
 * @namespace Common_Hooks
 * @description Use Save Keys
 */

import { useEffect } from "react";
import { rootLogger } from "../../log/logger";

// Common/Hooks/UseSaveKeys
const logger = rootLogger.fork({
    scopes: [
        "Common",
        "Hooks",
        "UseSaveKeys",
    ],
});

export const useSaveKeys = (onSave: () => void) => {

    useEffect(() => {
        logger.debug("Save Keys Hook Initialized");

        const handleKeyDown = (event: KeyboardEvent) => {

            if ((event.ctrlKey || event.metaKey) && event.key === "s") {
                event.preventDefault();
                onSave();
            }
        };

        window.addEventListener("keydown", handleKeyDown);

        return () => {
            logger.debug("Save Keys Hook Terminated");
            window.removeEventListener("keydown", handleKeyDown);
        };
    }, [onSave]);
};
