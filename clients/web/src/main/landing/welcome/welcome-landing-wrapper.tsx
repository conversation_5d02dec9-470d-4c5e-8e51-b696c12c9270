/**
 * <AUTHOR>
 * @namespace Landing_Welcome
 * @description Welcome Landing Wrapper
 */

import { getRouteRootView } from "@imbricate-hummingbird/navigation-core";
import { FullLoadingWrapper, LazyLoadComponent } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import { Navigate } from "react-router-dom";
import { useOldOrigins } from "../../origin/hooks/use-origins";

const WelcomeLanding = LazyLoadComponent(
    () => import("./welcome-landing"),
    "Welcome Landing",
);

export const WelcomeLandingWrapper: FC = () => {

    const origins = useOldOrigins();

    if (origins.length > 0) {

        return (<Navigate
            to={getRouteRootView()}
            replace={true}
        />);
    }

    return (<React.Suspense
        fallback={<FullLoadingWrapper
            debugDescription="Welcome Landing"
        />}
    >
        <WelcomeLanding />
    </React.Suspense>);
};
