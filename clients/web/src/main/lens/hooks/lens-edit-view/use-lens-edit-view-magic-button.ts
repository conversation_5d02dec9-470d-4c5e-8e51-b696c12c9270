/**
 * <AUTHOR>
 * @namespace Main_Lens_Hooks_LensEditView_UseLensEditViewMagicButton
 * @description Use Lens Edit View Magic Button
 */

import { useMagicButtonInitialItems } from "@/common/components/magic-button/hooks/use-initial-items";
import { UseMagicButtonResult, useMagicButton } from "@/common/components/magic-button/hooks/use-magic-button";
import { LENS_CONFIG_SOURCE, LensConfigItem } from "@imbricate-hummingbird/configuration";
import { isDefaultEmptyFormat } from "@imbricate-hummingbird/internationalization";
import { useNavigateLensView, useNavigateRoot } from "@imbricate-hummingbird/react-navigation";
import { LensSlice } from "@imbricate-hummingbird/react-store";
import { RiCameraLensFill, RiDeleteBinFill } from "react-icons/ri";
import { useDispatch } from "react-redux";
import { useLensFormat } from "../../internationalization/hook";
import { LENS_PROFILE } from "../../internationalization/profile";
import { DeleteLensResponse, deleteLens } from "../../operations/delete-lens";

export type UseLensEditViewMagicButtonProps = {

    readonly targetLens: LensConfigItem<LENS_CONFIG_SOURCE>;
};

export const useLensEditViewMagicButton = (
    props: UseLensEditViewMagicButtonProps,
): UseMagicButtonResult => {

    const lensFormat = useLensFormat();

    const navigateToLens = useNavigateLensView();
    const navigateToRoot = useNavigateRoot();

    const dispatch = useDispatch();

    const magicButtonResult = useMagicButton([
        {
            categoryKey: "lens",
            categoryTitle: lensFormat.get(LENS_PROFILE.LENS),
            categoryOrder: 1,
        },
        {
            categoryKey: "danger-zone",
            categoryTitle: lensFormat.get(LENS_PROFILE.DANGER_ZONE),
            categoryOrder: 2,
        },
    ]);

    useMagicButtonInitialItems(
        magicButtonResult,
        !isDefaultEmptyFormat(lensFormat),
        [
            {
                categoryKey: "lens",
                itemKey: "render-lens",
                icon: RiCameraLensFill,
                title: lensFormat.get(LENS_PROFILE.RENDER_LENS),
                description: lensFormat.get(LENS_PROFILE.RENDER_LENS_DESCRIPTION),
                onPress: () => {
                    navigateToLens(
                        props.targetLens.lensIdentifier,
                        {
                            replace: true,
                        },
                    );
                },
            },
            {
                categoryKey: "danger-zone",
                itemKey: "delete-lens",
                icon: RiDeleteBinFill,
                title: lensFormat.get(LENS_PROFILE.DELETE_LENS),
                description: lensFormat.get(LENS_PROFILE.DELETE_LENS_DESCRIPTION),
                color: "danger",
                onPress: () => {

                    const deletedLens: DeleteLensResponse = deleteLens(
                        props.targetLens.lensIdentifier,
                    );

                    dispatch(
                        LensSlice.actions.setLensConfig(
                            deletedLens.updated,
                        ),
                    );

                    navigateToRoot(
                        {
                            replace: true,
                        },
                    );
                },
            },
        ],
    );

    return magicButtonResult;
};
