/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Hooks
 * @description Use Doc Cast Modal
 */

import { useDocCastModalContext } from "../contexts/doc-cast-context";
import { DOC_CAST_SOURCE_TYPE, DocCastSource } from "../types/source";

export type UseDocCastModalResponse = {

    readonly addSources: (
        sources: DocCastSource<DOC_CAST_SOURCE_TYPE>[],
    ) => void;
};

export const useDocCastModal = (): UseDocCastModalResponse => {

    const context = useDocCastModalContext();

    return {
        addSources: context.addSources,
    };
};
