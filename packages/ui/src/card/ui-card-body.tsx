/**
 * <AUTHOR>
 * @package UI
 * @namespace Card
 * @description Card Body
 */

import { CardBody } from "@heroui/card";
import type { FC, ReactNode } from "react";

export type UICardBodyProps = {

    readonly className?: string;

    readonly children?: ReactNode;
};

export const UICardBody: FC<UICardBodyProps> = (
    props: UICardBodyProps,
) => {

    return (<CardBody
        className={props.className}
    >
        {props.children}
    </CardBody>);
};
