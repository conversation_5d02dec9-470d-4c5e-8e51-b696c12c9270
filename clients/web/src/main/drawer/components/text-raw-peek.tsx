/**
 * <AUTHOR>
 * @namespace Drawer_Components
 * @description Text Raw Peek
 */

import { LoadingWrapper } from "@imbricate-hummingbird/react-components";
import { UIDrawerBody, UITabs } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { MarkdownRender } from "../../common/components/markdown-render";
import { S_RawTextLoading, UseRawTextResponse, useRawText } from "../../text/hooks/use-raw-text";

export type DrawerTextRawPeekProps = {

    readonly originUniqueIdentifier: string;
    readonly textUniqueIdentifier: string;
};

// LAZY LOAD ONLY
const DrawerTextRawPeek: FC<DrawerTextRawPeekProps> = (
    props: DrawerTextRawPeekProps,
) => {

    const rawText: UseRawTextResponse = useRawText(
        props.originUniqueIdentifier,
        props.textUniqueIdentifier,
        [],
    );

    if (rawText === S_RawTextLoading) {
        return (<LoadingWrapper
            debugDescription="Text Raw Peek"
        />);
    }

    if (typeof rawText === "symbol") {
        return "Error";
    }

    return (<UIDrawerBody
        className="relative"
    >
        <UITabs
            ariaLabel="raw-text-tabs"
            className="sticky top-0 z-10"
            isFullWidth
            color="primary"
            defaultSelectedKey="rendered"
            tabs={[
                {
                    tabKey: "raw-text",
                    tabTitle: "Code",
                    panel: (<p
                        className="whitespace-pre-wrap break-all font-mono text-small"
                    >
                        {rawText.textContent}
                    </p>),
                },
                {
                    tabKey: "rendered",
                    tabTitle: "Rendered",
                    panel: (<MarkdownRender
                        rawText={rawText.textContent}
                    />),
                },
            ]}
        />
    </UIDrawerBody>);
};
export default DrawerTextRawPeek;
