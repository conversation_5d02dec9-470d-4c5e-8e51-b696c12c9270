/**
 * <AUTHOR>
 * @namespace GLSL
 * @description Canvas
 */

import React from "react";
import { useEffect, useRef } from "react";

export const Canvas = () => {

    const canvasRef = useRef<HTMLCanvasElement>(null);

    useEffect(() => {

        if (!canvasRef.current) {
            return;
        }

        const canvas = canvasRef.current;
        const gl = canvas.getContext("webgl");

        if (!gl) {
            return;
        }

        gl.clearColor(0, 0, 0, 1);
        gl.clear(gl.COLOR_BUFFER_BIT);
    }, []);

    return (<canvas
        ref={canvasRef}
        width={100}
        height={100}
    />);
};
