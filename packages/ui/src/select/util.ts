/**
 * <AUTHOR>
 * @package UI
 * @namespace Select
 * @description Util
 */

import { Select, SelectItemProps, SelectedItems } from "@heroui/select";
import { UISelectSelectedItem } from "./types";

export const parseUISelectSelectedItems = (
    rawSelectedItems: SelectedItems,
    renderValue: (
        selectedItems: UISelectSelectedItem[],
    ) => React.ReactNode,
) => {

    const selectedItems: UISelectSelectedItem[] = rawSelectedItems.map((
        each: SelectItemProps<typeof Select>,
    ) => {

        return {
            itemKey: String(each.key),
            itemTextValue: each.textValue,
        };
    });

    return renderValue(selectedItems);
};
