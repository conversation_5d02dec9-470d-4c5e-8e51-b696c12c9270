/**
 * <AUTHOR>
 * @namespace Flow_Hooks
 * @description Use Nodes Change
 */

import { Node, NodeChange } from "@xyflow/react";
import { useCallback } from "react";
import { applyDocCastFlowNodeChanges } from "../util/apply-node-changes";

export const useDocCastFlowNodesChange = (
    setParsedNodes: React.Dispatch<React.SetStateAction<Node[]>>,
): (changes: NodeChange[]) => void => {

    const onNodesChange = useCallback((
        changes: NodeChange[],
    ) => {

        setParsedNodes((
            previousNodes: Node[],
        ) => {

            return applyDocCastFlowNodeChanges(
                previousNodes,
                changes,
            );
        });
    }, []);

    return onNodesChange;
};
