/**
 * <AUTHOR>
 * @package UI
 * @namespace User
 * @description Imbricate Author User
 */

import { User } from "@heroui/user";
import type { ImbricateAuthor } from "@imbricate/core";
import { UISize } from "../common/types";

export type UIImbricateAuthorUserProps = {

    readonly author: ImbricateAuthor;

    readonly size?: UISize;
};

export const UIImbricateAuthorUser: React.FC<UIImbricateAuthorUserProps> = (
    props: UIImbricateAuthorUserProps,
) => {

    return (<User
        avatarProps={{
            size: props.size,
            name: props.author.identifier,
            isBordered: props.author.isAutomation,
            color: props.author.isAutomation ? "primary" : undefined,
        }}
        name={props.author.identifier}
        description={props.author.category}
    />);
};
