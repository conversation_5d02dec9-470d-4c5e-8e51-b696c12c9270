/**
 * <AUTHOR>
 * @namespace Config_Configuration
 * @description Export View Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const ConfigConfigurationExportViewApplication = LazyLoadComponent(
    () => import("./export-view"),
    "Config Configuration Export View Application",
);

export const ConfigConfigurationExportViewWrapper: FC = () => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Config Configuration Export View Application"
        />}
    >
        <ConfigConfigurationExportViewApplication />
    </React.Suspense>);
};
