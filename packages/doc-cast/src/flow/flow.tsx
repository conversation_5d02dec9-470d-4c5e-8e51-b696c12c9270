/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Flow
 * @description Flow
 */

import { useControlledTheme } from "@imbricate-hummingbird/theme-core";
import { Background, Controls, ReactFlow, ReactFlowInstance, useReactFlow } from "@xyflow/react";
import React, { useEffect, useRef } from "react";
import { DOC_CAST_SOURCE_TYPE, DocCastSource } from "../types/source";
import { useDocCastFlowReactFlow } from "./hooks/use-react-flow";
import { DOC_CAST_NODE_TYPES } from "./node/node-types";
import clsx from "clsx";

export type DocCastFlowProps = {

    readonly source: DocCastSource<DOC_CAST_SOURCE_TYPE>;
};

export const DocCastFlow: React.FC<DocCastFlowProps> = (
    props: DocCastFlowProps,
) => {

    const theme = useControlledTheme();

    const reactFlow: ReactFlowInstance = useReactFlow();
    const docCastFlow = useDocCastFlowReactFlow(
        reactFlow,
        props.source,
    );

    const flowRef = useRef<HTMLDivElement>(null);

    useEffect(() => {

        if (!flowRef.current) {
            return;
        }

        flowRef.current.focus();
    }, []);

    return (<div
        className={clsx(
            theme.theme,
            "h-100",
        )}
        ref={flowRef}
    >
        <ReactFlow
            fitView
            nodes={docCastFlow.nodes}
            onNodesChange={docCastFlow.onNodesChange}
            edges={docCastFlow.edges}
            onEdgesChange={docCastFlow.onEdgesChange}
            onConnect={docCastFlow.onConnect}
            onReconnectStart={docCastFlow.onReconnectStart}
            onReconnectEnd={docCastFlow.onReconnectEnd}
            onReconnect={docCastFlow.onReconnect}
            nodeTypes={DOC_CAST_NODE_TYPES}
        >
            <Controls />
            <Background />
        </ReactFlow>
    </div>);
};
