/**
 * <AUTHOR>
 * @package UI
 * @namespace Drawer
 * @description Drawer Header
 */

import { DrawerHeader } from "@heroui/drawer";
import type { FC, ReactNode } from "react";

export type UIDrawerHeaderProps = {

    readonly className?: string;

    readonly children?: ReactNode;
};

export const UIDrawerHeader: FC<UIDrawerHeaderProps> = (
    props: UIDrawerHeaderProps,
) => {

    return (<DrawerHeader
        className={props.className}
    >
        {props.children}
    </DrawerHeader>);
};
