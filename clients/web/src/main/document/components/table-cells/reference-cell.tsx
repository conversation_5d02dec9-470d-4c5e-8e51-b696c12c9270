/**
 * <AUTHOR>
 * @namespace Document_Components_TableCells
 * @description Reference Cell
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIButton, UIPopover, createUIButton } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaPropertyOptionsReference, ImbricatePropertyValueObjectReference } from "@imbricate/core";
import { FC } from "react";
import { rootLogger } from "../../../log/logger";
import { useDocumentFormat } from "../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../internationalization/profile";
import { DocumentReferenceValueReferencePopover } from "../property-value/reference/reference-popover";
import { DocumentReferenceValueSelectedReference } from "../property-value/reference/selected-reference";
import { DocumentTableCellContent } from "./cell-content";

const logger = rootLogger.fork({
    scopes: [
        "Document",
        "Components",
        "TableCells",
        "ReferenceCell",
    ],
});

export type DocumentTableReferenceCellProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.REFERENCE>;
    readonly getEditingProperty: () => TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.REFERENCE> | undefined;
    readonly updateEditingProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.REFERENCE>) => void;
    readonly editing: boolean;

    readonly options: ImbricateDatabaseSchemaPropertyOptionsReference;
};

export const DocumentTableReferenceCell: FC<DocumentTableReferenceCellProps> = (
    props: DocumentTableReferenceCellProps,
) => {

    const documentFormat = useDocumentFormat();

    const renderChip = (
        propertyDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.REFERENCE> | undefined,
    ) => {

        if (typeof propertyDraft === "undefined" || propertyDraft === null) {
            return null;
        }

        const value: ImbricatePropertyValueObjectReference[] =
            propertyDraft.value as ImbricatePropertyValueObjectReference[];

        if (!Array.isArray(value)) {
            return null;
        }

        const fixedValue: ImbricatePropertyValueObjectReference[] =
            Array.isArray(value)
                ? value
                : [];

        if (fixedValue.length > 0) {

            return (<UIPopover
                trigger={createUIButton({
                    color: "primary",
                    size: "sm",
                    variant: "flat",
                    children: (fixedValue.length > 1
                        ? documentFormat.get(
                            DOCUMENT_PROFILE.$1_REFERENCES,
                            fixedValue.length,
                        )
                        : documentFormat.get(
                            DOCUMENT_PROFILE.ONE_REFERENCE,
                        )),
                })}
                contentClassName="p-3 flex flex-col gap-1"
            >
                {fixedValue.map((item) => {

                    const fixedKey: string = [
                        item.originUniqueIdentifier,
                        item.databaseUniqueIdentifier,
                        item.documentUniqueIdentifier,
                    ].join("/");

                    return (<DocumentReferenceValueSelectedReference
                        key={fixedKey}
                        reference={item}
                        onDelete={props.editing ? () => {

                            logger.debug("Delete Reference", item);

                            const newValue: ImbricatePropertyValueObjectReference[] = fixedValue.filter((value) => {
                                return value.originUniqueIdentifier !== item.originUniqueIdentifier
                                    || value.databaseUniqueIdentifier !== item.databaseUniqueIdentifier
                                    || value.documentUniqueIdentifier !== item.documentUniqueIdentifier;
                            });

                            props.updateEditingProperty({
                                ...propertyDraft,
                                value: newValue,
                            });
                        } : undefined}
                    />);
                })}
            </UIPopover>);
        }

        return (<UIButton
            color="warning"
            size="sm"
            variant="flat"
            isDisabled
        >
            {documentFormat.get(
                DOCUMENT_PROFILE.NO_REFERENCE,
            )}
        </UIButton>);
    };

    if (props.editing) {

        const updatedProperty = props.getEditingProperty();

        if (typeof updatedProperty === "undefined") {
            throw new Error("[Imbricate] Updated property value not found");
        }

        return (<div
            className="flex gap-1 items-center"
        >
            {renderChip(updatedProperty)}
            <DocumentReferenceValueReferencePopover
                size="sm"
                radius="full"
                iconOnly
                propertyKey={props.propertyKey}
                currentProperty={{
                    key: props.propertyKey,
                    type: props.property
                        ? props.property.type
                        : IMBRICATE_PROPERTY_TYPE.REFERENCE,
                    value: updatedProperty.value,
                    variant: updatedProperty.variant,
                }}
                updateProperty={(newProperty) => {
                    props.updateEditingProperty({
                        ...updatedProperty,
                        value: newProperty.value,
                    });
                }}
                options={props.options}
            />
        </div>);
    }

    return (<DocumentTableCellContent
        schemaType={IMBRICATE_PROPERTY_TYPE.REFERENCE}
        property={props.property}
        render={(
            propertyDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> | null,
        ) => {

            return renderChip(propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.REFERENCE>);
        }}
    />);
};
