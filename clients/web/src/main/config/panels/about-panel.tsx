/**
 * <AUTHOR>
 * @namespace Config_Panels
 * @description About Panel
 */

import React, { FC } from "react";
import { ConfigResetCard } from "../components/reset-card";
import { ResourceCard } from "../components/resource-card";

export type ConfigAboutPanelProps = {
};

export const ConfigAboutPanel: FC<ConfigAboutPanelProps> = (
    _props: ConfigAboutPanelProps,
) => {

    return (<div
        className="flex flex-col gap-2 pr-2 py-2 flex-1"
    >
        <ResourceCard />
        <ConfigResetCard />
    </div>);
};
