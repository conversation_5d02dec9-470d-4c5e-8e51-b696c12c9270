/**
 * <AUTHOR>
 * @namespace WorkerOrigin_Worker_Commands
 * @description Upload Static In Base64
 */
// ONLY RUN IN WORKER

import { $ORIGIN_NOT_FOUND } from "@imbricate-hummingbird/global-symbol";
import { CentralUploadStaticInBase64Response, ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { IMBRICATE_STATIC_MIME_TYPE, ImbricateStaticManagerCreateStaticOutcome } from "@imbricate/core";
import { workerInternalGetNativeOriginObject } from "../internal/origin";
import { workerInternalExecuteUploadStaticInBase64 } from "../internal/static/upload-in-base64";

export const workerOriginUploadStaticInBase64Command = async (
    origins: ImbricateOriginObject[],
    originUniqueIdentifier: string,
    base64String: string,
    mimeType: IMBRICATE_STATIC_MIME_TYPE,
    actionIdentifier: string,
    recordIdentifier: string,
): Promise<CentralUploadStaticInBase64Response> => {

    const origin: ImbricateOriginObject | null = workerInternalGetNativeOriginObject(
        origins,
        originUniqueIdentifier,
    );

    if (!origin) {
        return $ORIGIN_NOT_FOUND;
    }

    const result: ImbricateStaticManagerCreateStaticOutcome =
        await workerInternalExecuteUploadStaticInBase64(
            origin,
            base64String,
            mimeType,
            actionIdentifier,
            recordIdentifier,
            false,
        );

    if (typeof result === "symbol") {
        return $ORIGIN_NOT_FOUND;
    }

    return {

        originUniqueIdentifier,
        static: {
            uniqueIdentifier: result.static.uniqueIdentifier,
        },
    };
};
