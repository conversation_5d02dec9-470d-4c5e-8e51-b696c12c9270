/**
 * <AUTHOR>
 * @namespace Common_Components_MagicButton_Hooks
 * @description Use Adjust Items
 */

import { ReactDependency } from "@imbricate-hummingbird/react-common";
import { useEffect } from "react";
import { UseMagicButtonResult } from "./use-magic-button";

export const useMagicButtonAdjustItems = (
    magicButtonResult: UseMagicButtonResult,
    condition: boolean,
    adjustItems: (result: UseMagicButtonResult) => () => void,
    dependencies: ReactDependency[],
) => {

    useEffect(() => {

        let callback: (() => void) | null = null;
        if (condition) {
            callback = adjustItems(magicButtonResult);
        }

        return () => {
            if (callback) {
                callback();
            }
        };
    }, [condition, ...dependencies]);

    return;
};
