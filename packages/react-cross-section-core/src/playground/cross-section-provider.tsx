/**
 * <AUTHOR>
 * @namespace CrossSection_Playground
 * @description Cross Section Provider
 */

import { ReactFlowProvider } from "@xyflow/react";
import { CrossSection } from "../types/cross-section";
import { CrossSectionPlayground } from "./cross-section-playground";

export type CrossSectionProviderProps = {

    readonly crossSection: CrossSection;
};

export const CrossSectionProvider = (
    props: CrossSectionProviderProps,
) => {

    return (<ReactFlowProvider>
        <CrossSectionPlayground crossSection={props.crossSection} />
    </ReactFlowProvider>);
};
