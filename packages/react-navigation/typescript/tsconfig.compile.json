{"compilerOptions": {"target": "ES6", "lib": ["WebWorker", "esnext", "DOM"], "noEmit": true, "downlevelIteration": true, "declaration": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "module": "ESNext", "jsx": "react-jsx", "moduleResolution": "node", "isolatedModules": true, "types": ["node", "jest"]}, "include": ["../src/**/*.ts", "../src/**/*.tsx"], "exclude": ["node_modules"]}