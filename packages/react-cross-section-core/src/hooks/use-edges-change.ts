/**
 * <AUTHOR>
 * @namespace CrossSection_Hooks
 * @description Use Edges Change
 */

import { Edge, EdgeChange } from "@xyflow/react";
import { useCallback } from "react";
import { applyFlowEdgeChanges } from "../../../doc-cast/src/flow/util/apply-edge-changes";

export const useCrossSectionEdgesChange = (
    setParsedEdges: React.Dispatch<React.SetStateAction<Edge[]>>,
): (changes: EdgeChange[]) => void => {

    const onEdgesChange = useCallback((
        changes: EdgeChange[],
    ) => {

        setParsedEdges((
            previousEdges: Edge[],
        ) => {
            return applyFlowEdgeChanges(
                previousEdges,
                changes,
            );
        });
    }, []);

    return onEdgesChange;
};
