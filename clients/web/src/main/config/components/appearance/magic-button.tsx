/**
 * <AUTHOR>
 * @namespace Config_Components_Appearance
 * @description Magic Button
 */

import { HUMMINGBIRD_MAGIC_BUTTON_DEFAULT_ACTION_MODE, HUMMINGBIRD_PREFERENCE_ITEM, PreferenceController } from "@imbricate-hummingbird/configuration";
import { FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody, UICardHeader, UIDivider, UIRadioGroup, UISpacer } from "@imbricate-hummingbird/ui";
import { FC, useState } from "react";
import { GiMagicLamp } from "react-icons/gi";
import { useConfigFormat } from "../../internationalization/hook";
import { CONFIG_PROFILE } from "../../internationalization/profile";

export type ConfigMagicButtonProps = {
};

export const ConfigMagicButton: FC<ConfigMagicButtonProps> = (
    _props: ConfigMagicButtonProps,
) => {

    const configFormat = useConfigFormat();

    const [magicButtonMode, setMagicButtonMode] = useState(() => {

        return PreferenceController.getPreference(
            HUMMINGBIRD_PREFERENCE_ITEM.HUMMINGBIRD_MAGIC_BUTTON_DEFAULT_ACTION_MODE,
        );
    });

    return (<StyledCard>
        <UICardHeader>
            <GiMagicLamp />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.MAGIC_BUTTON)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody>
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                >
                    {configFormat.get(CONFIG_PROFILE.MAGIC_BUTTON_DEFAULT_ACTION_MODE_DESCRIPTION)}
                </FormattedText>
            </div>
            <UIRadioGroup
                label={configFormat.get(CONFIG_PROFILE.MAGIC_BUTTON_DEFAULT_ACTION_MODE)}
                selected={magicButtonMode}
                onSelectedChange={(newSelected: string) => {

                    setMagicButtonMode(newSelected as HUMMINGBIRD_MAGIC_BUTTON_DEFAULT_ACTION_MODE);

                    PreferenceController.setPreference(
                        HUMMINGBIRD_PREFERENCE_ITEM.HUMMINGBIRD_MAGIC_BUTTON_DEFAULT_ACTION_MODE,
                        newSelected as HUMMINGBIRD_MAGIC_BUTTON_DEFAULT_ACTION_MODE,
                    );
                }}
                items={[
                    {
                        itemKey: HUMMINGBIRD_MAGIC_BUTTON_DEFAULT_ACTION_MODE.DISABLED,
                        itemContent: configFormat.get(CONFIG_PROFILE.DISABLED),
                    },
                    {
                        itemKey: HUMMINGBIRD_MAGIC_BUTTON_DEFAULT_ACTION_MODE.LAST_USED_ACTION,
                        itemContent: configFormat.get(CONFIG_PROFILE.LAST_USED_ACTION),
                    },
                    {
                        itemKey: HUMMINGBIRD_MAGIC_BUTTON_DEFAULT_ACTION_MODE.LAST_USED_OR_DEFAULT_ACTION,
                        itemContent: configFormat.get(CONFIG_PROFILE.LAST_USED_OR_DEFAULT_ACTION),
                    },
                ]}
            />
        </UICardBody>
    </StyledCard>);
};
