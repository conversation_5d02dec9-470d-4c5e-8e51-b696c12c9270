/**
 * <AUTHOR>
 * @namespace Database_Hooks
 * @description Use Database
 */

import { IImbricateDatabase } from "@imbricate/core";
import { S_UseDatabasesErrored, S_UseDatabasesLoading, S_UseDatabasesNotFound, UseDatabasesResponse, UseDatabasesResponseSymbol, useDatabases } from "./use-databases";
import { useMemo } from "react";

export const S_UseDatabaseLoading: unique symbol = Symbol("UseDatabaseLoading");
export const S_UseDatabaseNotFound: unique symbol = Symbol("UseDatabaseNotFound");
export const S_UseDatabaseErrored: unique symbol = Symbol("UseDatabaseErrored");

export type UseDatabaseResponseSymbol =
    | typeof S_UseDatabaseLoading
    | typeof S_UseDatabaseNotFound
    | typeof S_UseDatabaseErrored;

export const useDatabase = (
    databaseUniqueIdentifier: string,
): IImbricateDatabase | UseDatabaseResponseSymbol => {

    const databases: UseDatabasesResponse | UseDatabasesResponseSymbol =
        useDatabases();

    const response = useMemo(() => {

        if (databases === S_UseDatabasesLoading) {
            return S_UseDatabaseLoading;
        }

        if (databases === S_UseDatabasesNotFound) {
            return S_UseDatabaseNotFound;
        }

        if (databases === S_UseDatabasesErrored) {
            return S_UseDatabaseErrored;
        }

        const targetDatabase = databases.databases.find((database) => {
            return database.database.uniqueIdentifier === databaseUniqueIdentifier;
        });

        return targetDatabase
            ? targetDatabase.database
            : S_UseDatabaseNotFound;
    }, [databaseUniqueIdentifier, typeof databases]);

    return response;
};
