/**
 * <AUTHOR>
 * @namespace Debug_Components_WorkerActivities
 * @description Worker Activities Wrapper
 */

import { useDebugMode } from "@imbricate-hummingbird/debug";
import { LazyLoadComponent } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const WorkerActivities = LazyLoadComponent(
    () => import("./worker-activities"),
    "Worker Activities",
);

export const WorkerActivitiesWrapper: FC = () => {

    const { isDebugMode } = useDebugMode();

    if (!isDebugMode) {
        return null;
    }

    return (<React.Suspense>
        <WorkerActivities />
    </React.Suspense>);
};
