/**
 * <AUTHOR>
 * @package UI
 * @namespace Tabs
 * @description Types
 */

import type { ReactNode } from "react";

export type UITabsPlacement =
    | "top"
    | "bottom"
    | "start"
    | "end";

export type UITabsVariant =
    | "solid"
    | "light"
    | "underlined"
    | "bordered";

export type UITabsItem = {

    readonly tabKey: string;
    readonly tabTitle: ReactNode;

    readonly panel: ReactNode;
};
