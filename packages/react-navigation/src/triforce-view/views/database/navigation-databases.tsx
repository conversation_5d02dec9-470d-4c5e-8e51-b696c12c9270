/**
 * <AUTHOR>
 * @namespace Navigation
 * @description Navigation Databases
 */

import { getRouteDatabaseDocumentsView, getRouteOriginsView } from "@imbricate-hummingbird/navigation-core";
import { OriginWorkerDataCentral } from "@imbricate-hummingbird/origin-central-web-worker";
import { useDatabasesRecursive } from "@imbricate-hummingbird/react-origin-central";
import { TransferDatabase } from "@imbricate-hummingbird/transfer-core";
import { FC } from "react";
import { FaPlus } from "react-icons/fa";
import { TbWorld } from "react-icons/tb";
import { NavigationActionButton } from "../../../components/action-button";
import { useNavigationFormat } from "../../../internationalization/hook";
import { NAVIGATION_PROFILE } from "../../../internationalization/profile";

export const NavigationDatabases: FC = () => {

    const navigationFormat = useNavigationFormat();

    const databases = useDatabasesRecursive(
        OriginWorkerDataCentral.getInstance(),
        {
            actionName: "Get Databases",
            actionDescription: "Get all databases",
            executerMetadata: {
                executer: import.meta.url,
            },
        },
        [],
    );

    if (typeof databases === "symbol") {
        return null;
    }

    return (<div
        className="flex flex-col gap-1"
    >
        <NavigationActionButton
            Icon={FaPlus}
            title={navigationFormat.get(NAVIGATION_PROFILE.ADD_DATABASE)}
            description={navigationFormat.get(NAVIGATION_PROFILE.ADD_DATABASE_DESCRIPTION)}
            href={getRouteOriginsView()}
            color="primary"
        />
        {databases.map((
            database: TransferDatabase,
        ) => {

            return (<NavigationActionButton
                key={database.databaseUniqueIdentifier}
                title={database.databaseName}
                description={<div className="flex items-center gap-1">
                    <TbWorld />{database.originName}
                </div>}
                href={getRouteDatabaseDocumentsView(
                    database.databaseUniqueIdentifier,
                )}
            />);
        })}
    </div>);
};
