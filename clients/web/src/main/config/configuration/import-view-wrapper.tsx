/**
 * <AUTHOR>
 * @namespace Config_Configuration
 * @description Import View Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const ConfigConfigurationImportViewApplication = LazyLoadComponent(
    () => import("./import-view"),
    "Config Configuration Import View Application",
);

export const ConfigConfigurationImportViewWrapper: FC = () => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Config Configuration Import View Application"
        />}
    >
        <ConfigConfigurationImportViewApplication />
    </React.Suspense>);
};
