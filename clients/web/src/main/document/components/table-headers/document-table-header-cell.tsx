/**
 * <AUTHOR>
 * @namespace Document_Components_TableHeaders
 * @description Document Table Header Cell
 */

import { getPropertyIcon } from "@imbricate-hummingbird/react-components";
import { UITooltip } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { FaStar } from "react-icons/fa";
import { CommonCopyItem } from "../../../common/components/copy-item";
import { useDocumentFormat } from "../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../internationalization/profile";
import { ArrangeDocumentsResult } from "../../util/arrange-documents";

export type DocumentsTableHeaderCellProps = {

    readonly propertyIdentifier: string;
    readonly arrangedDocuments: ArrangeDocumentsResult;
};

export const DocumentsTableHeaderCell: FC<DocumentsTableHeaderCellProps> = (
    props: DocumentsTableHeaderCellProps,
) => {

    const documentFormat = useDocumentFormat();

    const propertyType: IMBRICATE_PROPERTY_TYPE = props.arrangedDocuments.propertyTypesMap[props.propertyIdentifier];
    const isPrimary: boolean = props.arrangedDocuments.primaryPropertyIdentifier === props.propertyIdentifier;

    return (<UITooltip
        content={<CommonCopyItem
            startContent={isPrimary
                ? documentFormat.get(DOCUMENT_PROFILE.PRIMARY_PROPERTY_IDENTIFIER)
                : documentFormat.get(DOCUMENT_PROFILE.PROPERTY_IDENTIFIER)}
            content={props.propertyIdentifier}
        />}
        delay={1000}
        placement="bottom"
    >
        <div className="flex gap-1 items-center">
            {isPrimary
                && <div>
                    <FaStar />
                </div>}
            <div>
                {getPropertyIcon(propertyType)}
            </div>
            <div>
                {props.arrangedDocuments.propertyNameMap[props.propertyIdentifier]}
            </div>
        </div>
    </UITooltip>);
};
