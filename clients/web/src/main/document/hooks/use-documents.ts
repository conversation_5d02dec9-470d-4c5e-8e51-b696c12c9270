/**
 * <AUTHOR>
 * @namespace Document_Hooks
 * @description Use Documents
 */

import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { IImbricateDocument, ImbricateDatabaseQueryDocumentsOutcome } from "@imbricate/core";
import { useEffect, useRef, useState } from "react";
import { OldDataCentral } from "../../data/old-data-central";
import { createQueryDocumentsAction } from "../actions/query-documents";

export const useOldDocuments = (
    databaseUniqueIdentifier: string,
    version: number,
): IImbricateDocument[] => {

    const [documents, setDocuments] = useState<IImbricateDocument[]>([]);

    const documentsRef = useRef<string>(databaseUniqueIdentifier);

    useEffect(() => {

        const execute = async () => {

            const documentsResult: ImbricateDatabaseQueryDocumentsOutcome =
                await ActionCentral.getInstance().executeAction(
                    async (
                        actionIdentifier: string,
                        recordIdentifier: string,
                    ) => {
                        return await OldDataCentral.getInstance()
                            .wideOriginQueryDocuments(
                                databaseUniqueIdentifier,
                                {},
                                actionIdentifier,
                                recordIdentifier,
                            );
                    },
                    createQueryDocumentsAction(
                        databaseUniqueIdentifier,
                        {
                            executer: import.meta.url,
                        },
                    ),
                );

            if (typeof documentsResult === "symbol") {
                return;
            }

            documentsRef.current = databaseUniqueIdentifier;
            setDocuments(documentsResult.documents);
        };

        execute();
    }, [databaseUniqueIdentifier, version]);

    if (documentsRef.current !== databaseUniqueIdentifier) {
        return [];
    }

    return documents;
};
