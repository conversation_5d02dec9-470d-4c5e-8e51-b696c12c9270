/**
 * <AUTHOR>
 * @namespace Debug_Components_RecentActionTracker
 * @description Recent Sub Action Item
 */

import { ActionPayloadPopover } from "@/main/data/components/action-payload-popover";
import { RecentActionItem } from "@imbricate-hummingbird/interceptor-core";
import { WORKER_WEB_WORKER } from "@imbricate-hummingbird/web-worker-core";
import { FC } from "react";
import { DisplayRecentAction } from "../../types/display-recent-action";
import { RecentActionChips } from "./recent-action-chips";
import { RecentActionExtraInformation } from "./recent-action-extra-information";
import { RECENT_ACTION_STATUSIcon } from "./recent-action-status-icon";

export type RecentSubActionItemProps = {

    readonly showExtraInformation: boolean;

    readonly displayRecentAction: DisplayRecentAction;
    readonly layer: number;
};

export const RecentSubActionItem: FC<RecentSubActionItemProps> = (
    props: RecentSubActionItemProps,
) => {

    const action: RecentActionItem<WORKER_WEB_WORKER> = props.displayRecentAction.recentAction;

    return (<div
        className="flex flex-row gap-2 items-center w-full"
    >
        <div>
            <RECENT_ACTION_STATUSIcon
                status={action.status}
            />
        </div>
        <div
            className="flex-1"
        >
            <div
                className="flex flex-row gap-1 items-center w-full"
            >
                <RecentActionChips
                    action={action}
                />
                <span
                    className="text-tiny"
                >
                    {action.description.actionName}
                </span>
                {action.description.actionPayload && <ActionPayloadPopover
                    data={action.description.actionPayload}
                />}
            </div>
            <div
                className="text-tiny text-gray-500 font-mono overflow-hidden text-ellipsis w-full"
            >
                {action.description.actionDescription}
            </div>
            <RecentActionExtraInformation
                showExtraInformation={props.showExtraInformation}
                action={action}
            />
            <div
                className="flex flex-col w-full gap-2"
            >
                {props.displayRecentAction.subActions.map((
                    subAction: DisplayRecentAction,
                ) => {
                    return (<RecentSubActionItem
                        key={subAction.recentAction.recentActionIdentifier}
                        showExtraInformation={props.showExtraInformation}
                        displayRecentAction={subAction}
                        layer={props.layer + 1}
                    />);
                })}
            </div>
        </div>
    </div>);
};
