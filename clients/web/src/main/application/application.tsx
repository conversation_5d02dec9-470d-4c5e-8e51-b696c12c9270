/**
 * <AUTHOR>
 * @namespace Application
 * @description Application
 */

import { getRoutePlatformMobileView, getRouteWelcomeView } from "@imbricate-hummingbird/navigation-core";
import { OriginWorkerDataCentral, useWorkerOriginWorker } from "@imbricate-hummingbird/origin-central-web-worker";
import { StyledErrorBoundary } from "@imbricate-hummingbird/react-components";
import { NavigationSwitch } from "@imbricate-hummingbird/react-navigation";
import { useWorkerScriptWorker } from "@imbricate-hummingbird/script-central-web-worker";
import { Navigate, Outlet } from "react-router-dom";
import { MainWrapper } from "../common/components/main-wrapper";
import { useIsNotSkippedMobileWarning } from "../common/hooks/use-is-mobile";
import { useLensInitialization } from "../lens/hooks/use-initialization";
import { rootLogger } from "../log/logger";
import { useOldOrigins } from "../origin/hooks/use-origins";

// Route/Application
const logger = rootLogger.fork({
    scopes: [
        "Route",
        "Application",
    ],
});

// LAZY LOAD ONLY
const Application = () => {

    const origins = useOldOrigins();
    useLensInitialization();

    useWorkerOriginWorker("main-application");
    useWorkerScriptWorker("main-application", () => {
        return OriginWorkerDataCentral.getInstance();
    });

    const isMobileWarningNotSkipped = useIsNotSkippedMobileWarning();

    if (isMobileWarningNotSkipped) {

        logger.debug("Redirecting to mobile landing");

        return (<Navigate
            to={getRoutePlatformMobileView()}
            replace={true}
        />);
    }

    if (origins.length === 0) {

        logger.debug("No origins found, redirecting to welcome");

        return (<Navigate
            to={getRouteWelcomeView()}
            replace={true}
        />);
    }

    return (<MainWrapper>
        <div className="flex gap-2 h-full">
            <NavigationSwitch />
            <div className="flex-1 overflow-hidden h-full">
                <StyledErrorBoundary>
                    <Outlet />
                </StyledErrorBoundary>
            </div>
        </div>
    </MainWrapper>);
};

export default Application;
