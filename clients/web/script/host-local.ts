/**
 * <AUTHOR>
 * @namespace Script
 * @description Host Local
 */

import { createServer } from "node:http";
import * as Path from "node:path";
import * as Fs from "node:fs";
import { stdout } from "node:process";

const assetsFolder: string = Path.join(__dirname, "..", "app");

const server = createServer((req, res) => {

    let url: string | undefined = req.url;

    if (!url) {
        res.end("Not Found");
        return;
    }

    if (url === "/") {
        url = "index.html";
    }

    stdout.write(url);
    stdout.write("\n");

    let filePath: string = Path.join(assetsFolder, url);

    const extension: string = Path.extname(url);

    let mimeType: string = "text/plain";
    switch (extension) {
        case ".html":
            mimeType = "text/html";
            break;
        case ".js":
            mimeType = "application/javascript";
            break;
        case ".css":
            mimeType = "text/css";
            break;
        case ".png":
            mimeType = "image/png";
            break;
        case ".json":
            mimeType = "application/json";
            break;
    }

    if (mimeType === "text/plain") {

        url = "/index.html";
        filePath = Path.join(assetsFolder, url);
        mimeType = "text/html";
    }

    if (!Fs.existsSync(filePath)) {
        res.statusCode = 404;
        res.end("Not Found");
        return;
    }

    Fs.readFile(filePath, (err, data) => {

        if (err) {
            res.statusCode = 500;
            res.end("Internal Server Error");
            return;
        }

        res.writeHead(200, {
            "Content-Type": mimeType,
        });
        res.end(data);
    });
});

server.listen(6711, "0.0.0.0");

stdout.write("Server is running on http://localhost:6711");
stdout.write("\n");
