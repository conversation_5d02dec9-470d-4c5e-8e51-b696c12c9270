/**
 * <AUTHOR>
 * @namespace Drawer_Internationalization
 * @description Hook
 */

import { defaultEmptyFormat, useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { useEffect, useState } from "react";
import { drawerInternationalization } from "./intl";
import { DRAWER_PROFILE } from "./profile";

export const useDrawerFormat = (): SudooFormat<DRAWER_PROFILE> => {

    const locale = useLocale();

    const [format, setFormat] = useState<SudooFormat<DRAWER_PROFILE>>(defaultEmptyFormat);

    useEffect(() => {

        const execute = async () => {

            const format = await drawerInternationalization.format(locale);
            setFormat(format);
        };

        execute();
    }, [locale]);

    return format;
};
