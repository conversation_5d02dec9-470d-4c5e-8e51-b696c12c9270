/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Components
 * @description Doc Cast Source
 */

import React from "react";
import { DOC_CAST_SOURCE_TYPE, DocCastSource } from "../types/source";

export type DocCastSourceItemProps = {

    readonly source: DocCastSource<DOC_CAST_SOURCE_TYPE>;
};

export const DocCastSourceItem: React.FC<DocCastSourceItemProps> = (
    props: DocCastSourceItemProps,
) => {

    return (<div>
        {props.source.type}
    </div>);
};
