/**
 * <AUTHOR>
 * @namespace Recent_Components_RecentCard
 * @description Database Card
 */

import { RECENT_TYPE, RecentItem } from "@imbricate-hummingbird/configuration";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { U<PERSON><PERSON>Header } from "@imbricate-hummingbird/ui";
import { IImbricateDatabase } from "@imbricate/core";
import React, { FC } from "react";
import { FaDatabase } from "react-icons/fa";
import { TbWorld } from "react-icons/tb";
import { S_UseDatabaseLoading, UseDatabaseResponseSymbol, useDatabase } from "../../../database/hooks/use-database";
import { useOldOrigin } from "../../../origin/hooks/use-origin";
import { RecentCardLoading } from "./loading-card";
import { RecentCardUnknown } from "./unknown-card";

export type RecentCardDatabaseProps = {

    readonly item: RecentItem<RECENT_TYPE.DATABASE>;
};

export const RecentCardDatabase: FC<RecentCardDatabaseProps> = (
    props: RecentCardDatabaseProps,
) => {

    const origin: ImbricateOriginObject | null = useOldOrigin(
        props.item.payload.originUniqueIdentifier,
    );

    const database: IImbricateDatabase | UseDatabaseResponseSymbol =
        useDatabase(
            props.item.payload.databaseUniqueIdentifier,
        );

    if (database === S_UseDatabaseLoading
        || !origin
    ) {

        return (<RecentCardLoading />);
    }


    if (typeof database === "symbol") {

        return (<RecentCardUnknown
            item={props.item}
        />);
    }

    return (<React.Fragment>
        <UICardHeader
            className="flex flex-row items-center gap-3"
        >
            <FaDatabase
                className="text-2xl"
            />
            <div
                className="flex-1 flex flex-col items-start text-left"
            >
                <div>
                    {database.databaseName}
                </div>
                <div
                    className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-1"
                >
                    <TbWorld /> {origin.originName}
                </div>
            </div>
            <div
                className="self-end text-xs text-gray-600 dark:text-gray-400"
            >
                {new Date(props.item.accessedAt).toLocaleString()}
            </div>
        </UICardHeader>
    </React.Fragment>);
};
