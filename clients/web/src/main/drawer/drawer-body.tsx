/**
 * <AUTHOR>
 * @namespace Drawer
 * @description Drawer Body
 */

import { DRAWER_TYPE, DrawerStatus } from "@imbricate-hummingbird/react-store";
import { FC } from "react";
import { DrawerDocumentViewWrapper } from "./component-wrappers/document-view-wrapper";
import { DrawerImbriScriptExecuteWrapper } from "./component-wrappers/imbriscript-execute-wrapper";
import { DrawerImbriscriptLensTextPeekWrapper } from "./component-wrappers/imbriscript-lens-text-peek-wrapper";
import { DrawerLensViewWrapper } from "./component-wrappers/lens-view-wrapper";
import { DrawerMarkdownTextPeekWrapper } from "./component-wrappers/markdown-text-peek-wrapper";
import { DrawerTextRawPeekWrapper } from "./component-wrappers/text-raw-peek-wrapper";

export type DrawerTextRawPeekProps = {

    readonly drawerStatus: DrawerStatus<DRAWER_TYPE>;
};

export const DrawerProviderBody: FC<DrawerTextRawPeekProps> = (
    props: DrawerTextRawPeekProps,
) => {

    // DRAWER_TYPE SWITCH
    switch (props.drawerStatus.type) {

        case DRAWER_TYPE.DOCUMENT_VIEW: {

            const status = props.drawerStatus as DrawerStatus<DRAWER_TYPE.DOCUMENT_VIEW>;

            return (<DrawerDocumentViewWrapper
                databaseUniqueIdentifier={status.payload.databaseUniqueIdentifier}
                documentUniqueIdentifier={status.payload.documentUniqueIdentifier}
            />);
        }
        case DRAWER_TYPE.IMBRISCRIPT_EXECUTE: {

            const status = props.drawerStatus as DrawerStatus<DRAWER_TYPE.IMBRISCRIPT_EXECUTE>;

            return (<DrawerImbriScriptExecuteWrapper
                imbriscriptText={status.payload.imbriscriptText}
                context={status.payload.context}
            />);
        }
        case DRAWER_TYPE.IMBRISCRIPT_LENS_TEXT_PEEK: {

            const status = props.drawerStatus as DrawerStatus<DRAWER_TYPE.IMBRISCRIPT_LENS_TEXT_PEEK>;

            return (<DrawerImbriscriptLensTextPeekWrapper
                imbriscriptText={status.payload.imbriscriptText}
                context={status.payload.context}
            />);
        }
        case DRAWER_TYPE.LENS_VIEW: {

            const status = props.drawerStatus as DrawerStatus<DRAWER_TYPE.LENS_VIEW>;

            return (<DrawerLensViewWrapper
                lensIdentifier={status.payload.lensIdentifier}
            />);
        }
        case DRAWER_TYPE.MARKDOWN_TEXT_PEEK: {

            const status = props.drawerStatus as DrawerStatus<DRAWER_TYPE.MARKDOWN_TEXT_PEEK>;

            return (<DrawerMarkdownTextPeekWrapper
                markdownText={status.payload.markdownText}
            />);
        }
        case DRAWER_TYPE.TEXT_RAW_PEEK: {

            const status = props.drawerStatus as DrawerStatus<DRAWER_TYPE.TEXT_RAW_PEEK>;

            return (<DrawerTextRawPeekWrapper
                originUniqueIdentifier={status.payload.originUniqueIdentifier}
                textUniqueIdentifier={status.payload.textUniqueIdentifier}
            />);
        }
    }
};
