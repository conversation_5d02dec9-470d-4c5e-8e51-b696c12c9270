/**
 * <AUTHOR>
 * @namespace Common_Shortcut
 * @description Get Meta Key Event
 */
// ONLY RUN IN RENDERER

import { isMac } from "../util/platform";

export const getMetaKeyEvent = (
    event: KeyboardEvent,
): boolean => {

    if (typeof window === "undefined") {
        return false;
    }

    if (isMac() && event.metaKey) {
        return true;
    }

    if (!isMac() && event.ctrlKey) {
        return true;
    }

    return false;
};
