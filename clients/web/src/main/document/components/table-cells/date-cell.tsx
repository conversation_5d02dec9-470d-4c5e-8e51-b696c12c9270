/**
 * <AUTHOR>
 * @namespace Document_Components_TableCells
 * @description Date Cell
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIDatePicker } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { DocumentTableCellContent } from "./cell-content";

export type DocumentTableDateCellProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.DATE>;
    readonly getEditingProperty: () => TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.DATE> | undefined;
    readonly updateEditingProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.DATE>) => void;
    readonly editing: boolean;
};

export const DocumentTableDateCell: FC<DocumentTableDateCellProps> = (
    props: DocumentTableDateCellProps,
) => {

    if (props.editing) {

        const updatedProperty = props.getEditingProperty();

        if (typeof updatedProperty === "undefined") {
            throw new Error("[Imbricate] Updated property value not found");
        }

        const parsedDate = typeof updatedProperty.value === "string"
            ? new Date(updatedProperty.value)
            : null;

        return (<UIDatePicker
            ariaLabel="Date picker"
            value={parsedDate}
            onChange={(newDate: Date | null) => {

                if (!newDate) {
                    return;
                }

                props.updateEditingProperty({
                    ...updatedProperty,
                    value: newDate.toISOString(),
                });
            }}
        />);
    }

    return (<DocumentTableCellContent
        schemaType={IMBRICATE_PROPERTY_TYPE.DATE}
        property={props.property}
        render={(
            propertyDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> | null,
        ) => {

            if (typeof propertyDraft === "undefined" || propertyDraft === null) {
                return null;
            }

            const propertyValue: string = propertyDraft.value as string;

            if (!propertyValue) {
                return null;
            }

            const parsedDate = typeof propertyValue === "string"
                ? new Date(propertyValue)
                : null;

            return (<UIDatePicker
                ariaLabel="Date picker"
                value={parsedDate}
                isReadOnly
            />);
        }}
    />);
};
