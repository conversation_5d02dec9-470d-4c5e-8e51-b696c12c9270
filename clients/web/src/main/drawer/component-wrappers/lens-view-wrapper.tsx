/**
 * <AUTHOR>
 * @namespace Drawer_Component_Wrappers
 * @description Lens View Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import { DrawerLensViewFetcherProps } from "../components/lens-view/lens-view-fetcher";

const DrawerLensView = LazyLoadComponent(
    () => import("../components/lens-view/lens-view-fetcher"),
    "Drawer Lens View",
);

export const DrawerLensViewWrapper: FC<DrawerLensViewFetcherProps> = (
    props: DrawerLensViewFetcherProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Drawer Lens View"
            label={["lens"]}
            fullHeight
        />}
    >
        <DrawerLensView
            {...props}
        />
    </React.Suspense>);
};
