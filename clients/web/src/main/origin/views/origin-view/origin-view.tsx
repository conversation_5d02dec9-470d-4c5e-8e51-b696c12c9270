/**
 * <AUTHOR>
 * @namespace Origin_Views_OriginView
 * @description Origin View
 */

import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { useAsyncTitle } from "@imbricate-hummingbird/react-common";
import { UINavbar, UISpacer } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { TbWorld } from "react-icons/tb";
import { useParams } from "react-router-dom";
import { OriginDatabaseListViewWrapper } from "../../components/origin-database-list-view-wrapper";
import { OriginInformationView } from "../../components/origin-information-view";
import { useOldOrigins } from "../../hooks/use-origins";

export type OriginViewProps = {
};

// LAZY LOAD ONLY
const OriginView: FC<OriginViewProps> = (
    _props: OriginViewProps,
) => {

    const params = useParams();
    const originUniqueIdentifier: string =
        params["origin-unique-identifier"] as string;

    const origins: ImbricateOriginObject[] = useOldOrigins();

    const targetOrigin: ImbricateOriginObject | undefined =
        origins.find((origin: ImbricateOriginObject) => {
            return origin.origin.uniqueIdentifier === originUniqueIdentifier;
        });

    useAsyncTitle(
        () => Boolean(targetOrigin),
        () => {
            return [
                targetOrigin!.originName,
                "Origin",
            ];
        },
        [targetOrigin?.origin.uniqueIdentifier],
    );

    if (!targetOrigin) {
        return null;
    }

    return (<div
        className="flex flex-col gap-2 overflow-auto h-full"
    >
        <UINavbar
            isFullWidth
            isBordered
            startContent={<React.Fragment>
                <TbWorld
                    className="text-2xl"
                />
                <UISpacer />
                <p
                    className="font-mono"
                >
                    Origin
                </p>
            </React.Fragment>}
            coreContent={<p className="font-bold text-xl">
                {targetOrigin.originName}
            </p>}
        />
        <div
            className="pr-2 flex flex-col gap-2 pb-4"
        >
            <OriginInformationView
                originUniqueIdentifier={targetOrigin.origin.uniqueIdentifier}
                originStorageInstance={targetOrigin.originInstance}
            />
            <OriginDatabaseListViewWrapper
                originName={targetOrigin.originName}
                origin={targetOrigin.origin}
            />
        </div>
    </div>);
};
export default OriginView;
