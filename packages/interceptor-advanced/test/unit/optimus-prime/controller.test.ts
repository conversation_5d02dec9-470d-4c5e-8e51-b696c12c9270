/**
 * <AUTHOR>
 * @namespace OptimusPrime
 * @description Controller
 * @override Unit Test
 */

import { OptimusPrimeController } from "../../../src/optimus-prime/controller";

describe("[Unit] test [Main -> Logo -> Controller -> Optimus Prime] controller class", () => {

    it("should be able to get next object", () => {

        const controller = OptimusPrimeController.createStandaloneInstance();

        controller.addOverride("1", "123");
        controller.addOverride("2", "456");

        expect(controller.getNextObject(3)).toBe("123");
    });

    it("should be able to get duplicated object", () => {

        const controller = OptimusPrimeController.createStandaloneInstance();

        controller.addOverride("1", "123");
        controller.addOverride("2", "456");

        expect(controller.getNextObject(9)).toBe("123456123");
    });

    it("should be able to remove override", () => {

        const controller = OptimusPrimeController.createStandaloneInstance();

        controller.addOverride("1", "123");
        controller.addOverride("2", "456");

        controller.removeOverride("1");

        expect(controller.getNextObject(6)).toBe("456456");
    });

    it("should be able to get empty object", () => {

        const controller = OptimusPrimeController.createStandaloneInstance();

        controller.addOverride("1", "123");
        controller.addOverride("2", "456");

        controller.removeOverride("1");
        controller.removeOverride("2");

        expect(controller.getNextObject(6)).toBe(null);
    });
});
