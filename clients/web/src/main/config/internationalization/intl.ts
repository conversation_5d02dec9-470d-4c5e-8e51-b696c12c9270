/**
 * <AUTHOR>
 * @namespace Config_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooLazyInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { CONFIG_PROFILE } from "./profile";

export const configInternationalization: SudooLazyInternationalization<CONFIG_PROFILE> =
    SudooLazyInternationalization.create<CONFIG_PROFILE>(
        DEFAULT_LOCALE,
    );

configInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/en-US").then(
        (module) => module.enUSConfigProfile,
    ),
);

configInternationalization.mergeLazyProfile(
    IETF_LOCALE.JAPANESE_JAPAN,
    import("./locale/ja-JP").then(
        (module) => module.jaJPConfigProfile,
    ),
);

configInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    import("./locale/zh-CN").then(
        (module) => module.zhCNConfigProfile,
    ),
);
