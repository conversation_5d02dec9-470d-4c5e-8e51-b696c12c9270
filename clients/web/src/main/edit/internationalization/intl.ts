/**
 * <AUTHOR>
 * @namespace Edit_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooLazyInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { EDIT_PROFILE } from "./profile";

export const editInternationalization: SudooLazyInternationalization<EDIT_PROFILE> =
    SudooLazyInternationalization.create<EDIT_PROFILE>(
        DEFAULT_LOCALE,
    );

editInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/en-US").then(
        (module) => module.enUSEditProfile,
    ),
);

editInternationalization.mergeLazyProfile(
    IETF_LOCALE.JAPANESE_JAPAN,
    import("./locale/ja-JP").then(
        (module) => module.jaJPEditProfile,
    ),
);

editInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    import("./locale/zh-CN").then(
        (module) => module.zhCNEEditProfile,
    ),
);
