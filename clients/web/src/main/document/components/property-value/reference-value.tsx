/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyValue
 * @description Reference Value
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaPropertyOptionsReference, ImbricatePropertyValueObjectReference } from "@imbricate/core";
import { FC } from "react";
import { DocumentReferenceValueReferencePopover } from "./reference/reference-popover";
import { DocumentReferenceValueSelectedReference } from "./reference/selected-reference";

export type DocumentReferenceValueProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.REFERENCE>;
    readonly updateProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.REFERENCE>) => void;

    readonly options: ImbricateDatabaseSchemaPropertyOptionsReference;
};

export const DocumentReferenceValue: FC<DocumentReferenceValueProps> = (
    props: DocumentReferenceValueProps,
) => {

    const fixedValue: ImbricatePropertyValueObjectReference[] =
        Array.isArray(props.property.value)
            ? props.property.value
            : [];

    return (<div
        className="flex w-full gap-2"
    >
        <div
            className="flex-1 flex flex-col gap-1"
        >
            {fixedValue.map((item) => {

                const fixedKey: string = [
                    item.originUniqueIdentifier,
                    item.databaseUniqueIdentifier,
                    item.documentUniqueIdentifier,
                ].join("/");

                return (<DocumentReferenceValueSelectedReference
                    key={fixedKey}
                    reference={item}
                    onDelete={() => {

                        const newValue: ImbricatePropertyValueObjectReference[] = fixedValue.filter((value) => {
                            return value.originUniqueIdentifier !== item.originUniqueIdentifier
                                || value.databaseUniqueIdentifier !== item.databaseUniqueIdentifier
                                || value.documentUniqueIdentifier !== item.documentUniqueIdentifier;
                        });

                        props.updateProperty({
                            key: props.propertyKey,
                            type: IMBRICATE_PROPERTY_TYPE.REFERENCE,
                            value: newValue,
                            variant: props.property.variant,
                        });
                    }}
                />);
            })}
        </div>
        <div>
            <DocumentReferenceValueReferencePopover
                radius="sm"
                propertyKey={props.propertyKey}
                currentProperty={{
                    key: props.propertyKey,
                    type: props.property.type,
                    value: fixedValue,
                    variant: props.property.variant,
                }}
                updateProperty={props.updateProperty}
                options={props.options}
            />
        </div>
    </div>);
};
