/**
 * <AUTHOR>
 * @namespace Navigation_View
 * @description Navigation Frame
 */

import { HUMMINGBIRD_ANIMATION_LEVEL, HUMMINGBIRD_PREFERENCE_ITEM, usePreferenceItem } from "@imbricate-hummingbird/configuration";
import { getRouteSearchView } from "@imbricate-hummingbird/navigation-core";
import { OptimusPrime, StyledErrorBoundary } from "@imbricate-hummingbird/react-components";
import { UIButtonLink } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { FaSearch } from "react-icons/fa";
import { Link } from "react-router-dom";
import { NavigationBottomButton } from "../components/bottom-button";
import { NavigationErrorHeader } from "../components/error-header";
import { useNavigationFormat } from "../internationalization/hook";
import { NAVIGATION_PROFILE } from "../internationalization/profile";

export type NavigationFrameProps = {

    readonly children: React.ReactNode;
};

export const NavigationFrame: FC<NavigationFrameProps> = (
    props: NavigationFrameProps,
) => {

    const navigationFormat = useNavigationFormat();

    const animationLevel = usePreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.APPLICATION_APPEARANCE_ANIMATION_LEVEL,
    );

    const enableAnimation = animationLevel.preference === HUMMINGBIRD_ANIMATION_LEVEL.FULL;

    return (<div className="w-full max-w-[270px] p-1 overflow-auto relative flex flex-col">
        <StyledErrorBoundary>
            <div className="w-full justify-center items-center flex mt-2 mb-1">
                <Link
                    to="/"
                    color="foreground"
                >
                    <OptimusPrime
                        enableAnimation={enableAnimation}
                        size="small"
                        sideRows={0}
                        sideColumns={3}
                        transitionBaseline={100}
                        useColorSteps
                    />
                </Link>
            </div>
            <div className="w-full mb-2 sticky top-0 z-20 flex flex-col gap-2">
                <NavigationErrorHeader />
                <UIButtonLink
                    href={getRouteSearchView()}
                    startContent={<FaSearch />}
                    color="secondary"
                    variant="solid"
                    isFullWidth
                >
                    {navigationFormat.get(NAVIGATION_PROFILE.SEARCH)}
                </UIButtonLink>
            </div>
            <div
                className="flex-1 flex flex-col"
            >
                <div
                    className="flex-1"
                >
                    {props.children}
                </div>
                <NavigationBottomButton />
            </div>
        </StyledErrorBoundary>
    </div>);
};
