/**
 * <AUTHOR>
 * @namespace Data_Components
 * @description Action Payload Popover
 */

import { UIPopover, UITextarea, createUIButton } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { GiExtraTime } from "react-icons/gi";

export type ActionPayloadPopoverProps = {

    readonly data: any;
};

export const ActionPayloadPopover: FC<ActionPayloadPopoverProps> = (
    props: ActionPayloadPopoverProps,
) => {

    return (<UIPopover
        trigger={createUIButton({
            size: "sm",
            variant: "flat",
            color: "primary",
            isIconOnly: true,
            children: (<GiExtraTime
                className="text-lg"
            />),
        })}
    >
        <UITextarea
            className="w-full font-mono"
            columns={64}
            label="Payload"
            labelPlacement="outside"
            value={JSON.stringify(props.data, null, 2)}
            isReadOnly
        />
    </UIPopover>);
};
