/**
 * <AUTHOR>
 * @namespace Monaco_LogPreview_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { enUSMonacoLogPreviewProfile } from "./locale/en-US";
import { jaJPMonacoLogPreviewProfile } from "./locale/ja-JP";
import { zhCNMonacoLogPreviewProfile } from "./locale/zh-CN";
import { MONACO_LOG_PREVIEW_PROFILE } from "./profile";

export const monacoLogPreviewInternationalization: SudooInternationalization<MONACO_LOG_PREVIEW_PROFILE> =
    SudooInternationalization.create<MONACO_LOG_PREVIEW_PROFILE>(
        DEFAULT_LOCALE,
    );

monacoLogPreviewInternationalization.merge(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    enUSMonacoLogPreviewProfile,
);

monacoLogPreviewInternationalization.merge(
    IETF_LOCALE.JAPANESE_JAPAN,
    jaJPMonacoLogPreviewProfile,
);

monacoLogPreviewInternationalization.merge(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    zhCNMonacoLogPreviewProfile,
);
