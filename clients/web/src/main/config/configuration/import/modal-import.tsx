/**
 * <AUTHOR>
 * @package Web
 * @namespace Config_Configuration_Import
 * @description Modal Import
 */

import { UIButton, UIModal, UIModalBody, UIModalFooter, UIModalHeader, UITextarea, useUIDisclosure } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { FaFileImport } from "react-icons/fa";
import { rootLogger } from "../../../log/logger";
import { ExportableConfiguration } from "../../types/configuration";
import { validateExportableConfiguration } from "../../util/validate-configuration";

const logger = rootLogger.fork({
    scopes: [
        "Config",
        "Configuration",
        "Import",
        "ModalImport",
    ],
});

export type ConfigConfigurationModalImportProps = {

    readonly onImportConfiguration: (configuration: ExportableConfiguration) => void;
};

export const ConfigConfigurationModalImport: FC<ConfigConfigurationModalImportProps> = (
    props: ConfigConfigurationModalImportProps,
) => {

    const { isOpen, onOpen, onOpenChange } = useUIDisclosure();
    const [error, setError] = React.useState<boolean>(false);
    const [importConfig, setImportConfig] = React.useState<string>("");

    return (<React.Fragment>
        <UIButton
            startContent={<FaFileImport />}
            variant="flat"
            color="primary"
            onPress={async () => {
                onOpen();
            }}
        >
            Import from JSON String
        </UIButton>
        <UIModal
            isOpen={isOpen}
            onOpenChange={onOpenChange}
            size="2xl"
        >
            {(onClose: () => void) => {
                return (<React.Fragment>
                    <UIModalHeader className="flex flex-col gap-1">
                        Import from JSON String
                    </UIModalHeader>
                    <UIModalBody>
                        <UITextarea
                            isFullWidth
                            isClearable
                            isRequired
                            isInvalid={error}
                            errorMessage="Invalid Imbricate Configuration object"
                            label="JSON String"
                            labelPlacement="outside"
                            placeholder="Enter configuration JSON string here"
                            value={importConfig}
                            onValueChange={(configString: string) => {
                                setError(false);
                                setImportConfig(configString);
                            }}
                        />
                    </UIModalBody>
                    <UIModalFooter>
                        <UIButton
                            color="danger"
                            variant="light"
                            onPress={onClose}
                        >
                            Close
                        </UIButton>
                        <UIButton
                            isDisabled={error || importConfig.length === 0}
                            color="primary"
                            onPress={() => {

                                try {
                                    const parsedConfiguration: ExportableConfiguration = JSON.parse(importConfig);

                                    const validationResult: boolean = validateExportableConfiguration(parsedConfiguration);

                                    if (!validationResult) {
                                        setError(true);
                                        return;
                                    }

                                    props.onImportConfiguration(parsedConfiguration);
                                    onClose();
                                } catch (error) {

                                    logger.error(error);
                                    setError(true);
                                }
                            }}
                        >
                            Import
                        </UIButton>
                    </UIModalFooter>
                </React.Fragment>);
            }}
        </UIModal>
    </React.Fragment>);
};
