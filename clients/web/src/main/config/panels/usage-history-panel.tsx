/**
 * <AUTHOR>
 * @namespace Config_Panels
 * @description Usage History Panel
 */

import React, { FC } from "react";
import { ConfigCleanRecentUsageCard } from "../components/clean-recent-card";
import { ConfigRecentItemsCard } from "../components/recent-items";

export type ConfigUsageHistoryPanelProps = {
};

export const ConfigUsageHistoryPanel: FC<ConfigUsageHistoryPanelProps> = (
    _props: ConfigUsageHistoryPanelProps,
) => {

    return (<div
        className="flex flex-col gap-2 pr-2 py-2 flex-1"
    >
        <ConfigRecentItemsCard />
        <ConfigCleanRecentUsageCard />
    </div>);
};
