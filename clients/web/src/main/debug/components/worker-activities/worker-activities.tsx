/**
 * <AUTHOR>
 * @namespace Debug_Components
 * @description Worker Activities
 */

import { WORKER_ACTIVITY_INITIATOR, WorkerActivitiesController, WorkerActivityItem } from "@imbricate-hummingbird/bridge-core";
import { DebugSettingsController } from "@imbricate-hummingbird/debug";
import { UIBadge, UIButton, UICheckbox, UIChip, UIDivider, UIModal, UIModalBody, UIModalFooter, UIModalHeader, useUIDisclosure } from "@imbricate-hummingbird/ui";
import { WORKER_WEB_WORKER } from "@imbricate-hummingbird/web-worker-core";
import React, { FC, useEffect, useState } from "react";
import { RiCheckboxMultipleBlankFill } from "react-icons/ri";
import { WorkerActivitiesActivity } from "./activity/activity";
import { buildActivitySetKey } from "./util/activity-set-key";
import { concludeWorkerActivities } from "./util/conclude-activities";

// LAZY LOAD ONLY
const WorkerActivities: FC = () => {

    const [activitySets, setActivitySets] =
        useState<[WorkerActivityItem | null, WorkerActivityItem | null][]>([]);
    const [hangingActivities, setHangingActivities] =
        useState<WorkerActivityItem[]>([]);

    const [displayInitiatorResponseGroup, setDisplayInitiatorResponseGroup] =
        useState<boolean>(true);
    const [displayNotification, setDisplayNotification] =
        useState<boolean>(true);

    const [displayOriginWorker, setDisplayOriginWorker] =
        useState<boolean>(true);
    const [displayScriptWorker, setDisplayScriptWorker] =
        useState<boolean>(true);
    const [displayTextWorker, setDisplayTextWorker] =
        useState<boolean>(true);

    const { isOpen, onOpen, onOpenChange } = useUIDisclosure();

    const updateActivitySets = () => {

        const instance = WorkerActivitiesController.getInstance();
        const activities = instance.getActivities();

        const filteredActivities = activities.filter((activity) => {

            if (!displayOriginWorker
                && activity.worker === WORKER_WEB_WORKER.ORIGIN_WORKER) {
                return false;
            }

            if (!displayScriptWorker
                && activity.worker === WORKER_WEB_WORKER.SCRIPT_WORKER) {
                return false;
            }

            if (!displayTextWorker
                && activity.worker === WORKER_WEB_WORKER.TEXT_WORKER) {
                return false;
            }

            if (displayInitiatorResponseGroup
                && (activity.initiator === WORKER_ACTIVITY_INITIATOR.INITIATOR
                    || activity.initiator === WORKER_ACTIVITY_INITIATOR.RESPONSE)) {
                return true;
            }

            if (displayNotification
                && activity.initiator === WORKER_ACTIVITY_INITIATOR.NOTIFICATION) {
                return true;
            }

            return false;
        });

        const {
            result,
            hangingActivities,
        } = concludeWorkerActivities(filteredActivities);

        setActivitySets(result);
        setHangingActivities(hangingActivities);
    };

    useEffect(() => {
        updateActivitySets();
    }, [displayInitiatorResponseGroup, displayNotification, displayOriginWorker, displayScriptWorker, displayTextWorker]);

    useEffect(() => {

        const instance = WorkerActivitiesController.getInstance();

        const listener = () => {
            updateActivitySets();
        };
        instance.addListener(listener);

        return () => {
            instance.removeListener(listener);
        };
    }, []);

    useEffect(() => {

        const controller = DebugSettingsController.getInstance();

        controller.putDebugSetting({
            scope: "debug/worker-activities",
            identifier: "toggle-worker-activities-limitations",
            name: "Toggle Worker Activities Limitations",
            description: "Toggle the worker activities limitations",
            checked: true,
            action: () => {
            },
        });
    }, []);

    return (<React.Fragment>
        <UIBadge
            placement="top-left"
            shape="rectangle"
            color={hangingActivities.length > 0 ? "danger" : "success"}
            content={activitySets.length > 0 ? activitySets.length : ""}
            size="lg"
        >
            <UIButton
                size="lg"
                isIconOnly
                variant="flat"
                color="warning"
                onPress={onOpen}
            >
                <RiCheckboxMultipleBlankFill
                    className="text-xl"
                />
            </UIButton>
        </UIBadge>
        <UIModal
            isOpen={isOpen}
            onOpenChange={onOpenChange}
            size="3xl"
            isHideCloseButton
            isDismissable={false}
            isKeyboardDismissDisabled={true}
        >
            {(onClose: () => void) => {
                return (<React.Fragment>
                    <UIModalHeader
                        className="items-center gap-5"
                    >
                        <RiCheckboxMultipleBlankFill
                            className="text-2xl"
                        />
                        <div
                            className="w-full flex flex-col gap-1"
                        >
                            <div
                                className="font-mono text-2xl"
                            >
                                Worker Activities
                            </div>
                            <div
                                className="font-normal flex flex-row gap-3"
                            >
                                <UIChip
                                    color="primary"
                                    variant="flat"
                                    className="font-mono"
                                >
                                    Message Type
                                </UIChip>
                                <UICheckbox
                                    isSelected={displayInitiatorResponseGroup}
                                    onValueChange={setDisplayInitiatorResponseGroup}
                                >
                                    Initiator Response Group
                                </UICheckbox>
                                <UICheckbox
                                    isSelected={displayNotification}
                                    onValueChange={setDisplayNotification}
                                >
                                    Notification
                                </UICheckbox>
                            </div>
                            <div
                                className="font-normal flex flex-row gap-3"
                            >
                                <UIChip
                                    color="primary"
                                    variant="flat"
                                    className="font-mono"
                                >
                                    Worker
                                </UIChip>
                                <UICheckbox
                                    isSelected={displayOriginWorker}
                                    onValueChange={setDisplayOriginWorker}
                                >
                                    Origin Worker
                                </UICheckbox>
                                <UICheckbox
                                    isSelected={displayScriptWorker}
                                    onValueChange={setDisplayScriptWorker}
                                >
                                    Script Worker
                                </UICheckbox>
                                <UICheckbox
                                    isSelected={displayTextWorker}
                                    onValueChange={setDisplayTextWorker}
                                >
                                    Text Worker
                                </UICheckbox>
                            </div>
                        </div>
                    </UIModalHeader>
                    <UIDivider />
                    <UIModalBody>
                        <div
                            className="grid grid-cols-[1fr_auto_1fr] gap-y-4 gap-x-1"
                        >
                            {activitySets.map((activitySet) => {

                                const key = buildActivitySetKey(activitySet);

                                return (<WorkerActivitiesActivity
                                    key={key}
                                    activitySet={activitySet}
                                />);
                            })}
                            {activitySets.length === 0 && "No activities"}
                        </div>
                    </UIModalBody>
                    <UIDivider />
                    <UIModalFooter>
                        <UIButton
                            color="danger"
                            variant="light"
                            onPress={onClose}
                        >
                            Close
                        </UIButton>
                    </UIModalFooter>
                </React.Fragment>);
            }}
        </UIModal>
    </React.Fragment>);
};

export default WorkerActivities;
