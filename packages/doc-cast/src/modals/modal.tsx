/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Modals
 * @description Modal
 */

import { UIButton, UIDivider, UIModal, UIModalBody, UIModalFooter, UIModalHeader } from "@imbricate-hummingbird/ui";
import React from "react";
import { DocCastList } from "../components/doc-cast-list";
import { DocCastModalContextProps } from "../contexts/doc-cast-context";
import { DocCastFlow } from "../flow/flow";
import { useArrangeFormat } from "../internationalization/hook";
import { ARRANGE_PROFILE } from "../internationalization/profile";

export type DocCastModalProps = {

    readonly context: DocCastModalContextProps;
};

export const DocCastModal: React.FC<DocCastModalProps> = (
    props: DocCastModalProps,
) => {

    const arrangeFormat = useArrangeFormat();

    const modalContext = props.context;

    return (<UIModal
        isOpen={modalContext.open}
        onOpenChange={modalContext.onOpenChange}
        size="5xl"
        isHideCloseButton
        isDismissable={false}
        isKeyboardDismissDisabled={true}
    >
        {(
            onClose: () => void,
        ) => {

            return (<React.Fragment>
                <UIModalHeader
                    className="flex flex-col gap-1"
                >
                    {arrangeFormat.get(ARRANGE_PROFILE.ARRANGE_DOCUMENT)}
                </UIModalHeader>
                <UIDivider />
                <UIModalBody
                    className="h-full p-0"
                >
                    <div
                        className="flex"
                    >
                        <div
                        >
                            <DocCastList
                                sources={modalContext.sources}
                            />
                        </div>
                        <UIDivider
                            className="ml-2"
                            orientation="vertical"
                        />
                        <div
                            className="flex-1"
                        >
                            <DocCastFlow
                                source={modalContext.sources[0]}
                            />
                        </div>
                    </div>
                </UIModalBody>
                <UIDivider />
                <UIModalFooter
                    className="p-3 flex justify-start"
                >
                    <UIButton
                        color="danger"
                        variant="light"
                        onPress={() => {

                            props.context.clearSources();
                            onClose();
                        }}
                    >
                        {arrangeFormat.get(ARRANGE_PROFILE.CLOSE_AND_CANCEL_ALL_PENDING_DOCUMENTS)}
                    </UIButton>
                </UIModalFooter>
            </React.Fragment>);
        }}
    </UIModal>);
};
