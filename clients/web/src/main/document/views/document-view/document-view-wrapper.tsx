/**
 * <AUTHOR>
 * @namespace Document_Views_DocumentView
 * @description Document View Wrapper
 */

import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import { useParams } from "react-router-dom";
import { useWideOrigin } from "../../../origin/hooks/use-wide-origin";

const DocumentViewApplication = LazyLoadComponent(
    () => import("./document-view"),
    "Document View Application",
);

export const DocumentViewWrapper: FC = () => {

    const params = useParams();

    const databaseUniqueIdentifier: string =
        params["database-unique-identifier"] as string;

    const origin: ImbricateOriginObject | null =
        useWideOrigin(databaseUniqueIdentifier);

    if (typeof origin === "symbol") {
        return null;
    }

    if (origin === null) {
        return null;
    }

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Document View Application"
            fullHeight
        />}
    >
        <DocumentViewApplication
            origin={origin}
        />
    </React.Suspense>);
};
