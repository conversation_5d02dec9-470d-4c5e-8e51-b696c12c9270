/**
 * <AUTHOR>
 * @namespace Monaco_LogPreview
 * @description Log Preview Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import { ImbriScriptContainerLog } from "@imbricate-hummingbird/script-core";
import React, { FC } from "react";

const LogPreview = LazyLoadComponent(
    () => import("./log-preview"),
    "Log Preview",
);

export type MonacoLogPreviewWrapperProps = {

    readonly logs: ImbriScriptContainerLog[];

    readonly progressing: boolean;
};

export const MonacoLogPreviewWrapper: FC<MonacoLogPreviewWrapperProps> = (
    props: MonacoLogPreviewWrapperProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Log Preview"
            fullHeight
        />}
    >
        <LogPreview
            logs={props.logs}
            progressing={props.progressing}
        />
    </React.Suspense>);
};
