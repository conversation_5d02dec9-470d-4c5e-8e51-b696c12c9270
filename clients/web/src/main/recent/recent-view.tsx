/**
 * <AUTHOR>
 * @namespace Recent
 * @description Recent View
 */

import { useTitle } from "@imbricate-hummingbird/react-common";
import { UIScrollShadow } from "@imbricate-hummingbird/ui";
import { ConfigBrandBanner } from "../config/components/brand-banner";
import { RecentCardRenderer } from "./recent-card-renderer";

// LAZY LOAD ONLY
const RecentView = () => {

    useTitle([], []);

    return (<div
        className="h-full min-h-full flex flex-col"
    >
        <div
            className="flex-1 flex h-full"
        >
            <ConfigBrandBanner />
            <UIScrollShadow
                className="flex-1 h-full flex flex-col gap-2"
                overflowXAuto
                overflowYAuto
            >
                <div
                    className="flex flex-col gap-2 px-2 py-2 flex-1 justify-center"
                >
                    <RecentCardRenderer />
                </div>
            </UIScrollShadow>
        </div>
    </div>);
};
export default RecentView;
