/**
 * <AUTHOR>
 * @namespace Debug_Components_OptimusPrime
 * @description Optimus Prime Quick Access
 */

import { OptimusPrimeController, OptimusPrimeOverrideInstance } from "@imbricate-hummingbird/interceptor-advanced";
import { StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody } from "@imbricate-hummingbird/ui";
import React from "react";

export const OptimusPrimeQuickAccess: React.FC = () => {

    const [overrides, setOverrides] = React.useState<OptimusPrimeOverrideInstance[]>([]);

    React.useEffect(() => {

        const controller = OptimusPrimeController.getInstance();

        const updateValue = () => {

            const overrides = controller.getOverrides();
            setOverrides(overrides);
        };

        const listener = () => {
            updateValue();
        };

        controller.addListener(listener);
        return () => {

            controller.removeListener(listener);
        };
    }, []);

    return (<StyledCard>
        <UICardBody>
            <div
                className="flex gap-2 font-mono"
            >
                <div
                    className="text-tiny text-foreground-500"
                >
                    Optimus Prime Overrides:
                </div>
                <div
                    className="text-tiny"
                >
                    {overrides.length > 0 ? overrides.map((override) => {
                        return (<div
                            key={override.source}
                        >
                            {override.source}
                        </div>);
                    }) : "No overrides"}
                </div>
            </div>
        </UICardBody>
    </StyledCard>);
};
