/**
 * <AUTHOR>
 * @namespace Common_PWA
 * @description Refresh
 */

import { rootLogger } from "../../log/logger";

const logger = rootLogger.fork({
    scopes: [
        "Common",
        "PWA",
        "Refresh",
    ],
});

export const updatePWA = async (): Promise<void> => {

    const navigatorClone = navigator as Navigator | null;

    if (!navigatorClone) {
        return;
    }

    const registrations = await navigatorClone.serviceWorker.getRegistrations();

    for (const registration of registrations) {
        await registration.update();
    }

    logger.info("PWA updated");
};

export const refreshPWA = async (): Promise<void> => {

    const navigatorClone = navigator as Navigator | null;

    if (!navigatorClone) {
        return;
    }

    const registrations = await navigatorClone.serviceWorker.getRegistrations();

    for (const registration of registrations) {
        await registration.unregister();
    }

    window.location.reload();

    logger.info("PWA refreshed");
};
