/**
 * <AUTHOR>
 * @namespace Config_Navigation
 * @description Config Navigation Button
 */

import { UIButtonLink } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { IconType } from "react-icons";
import { IoCaretBackCircle } from "react-icons/io5";
import { useLocation } from "react-router-dom";

export type ConfigNavigationButtonProps = {

    readonly label: string;
    readonly icon: IconType;

    readonly targetUrl: string;
};

export const ConfigNavigationButton: FC<ConfigNavigationButtonProps> = (
    props: ConfigNavigationButtonProps,
) => {

    const location = useLocation();
    const isActive = location.pathname === props.targetUrl;

    const isSemiActive = !isActive
        && location.pathname.startsWith(props.targetUrl);

    return (<UIButtonLink
        href={props.targetUrl}
        startContent={<props.icon
            className="text-large font-bold"
        />}
        endContent={isSemiActive && <IoCaretBackCircle
            className="text-large font-bold"
        />}
        variant={isActive
            ? "solid"
            : isSemiActive
                ? "flat"
                : "light"}
        className="w-full min-h-10"
        color={isActive
            ? "primary"
            : isSemiActive
                ? "primary"
                : "default"}
        radius="sm"
    >
        <div
            className="w-full text-left"
        >
            {props.label}
        </div>
    </UIButtonLink>);
};
