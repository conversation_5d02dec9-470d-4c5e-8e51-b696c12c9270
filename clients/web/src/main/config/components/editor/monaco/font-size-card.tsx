/**
 * <AUTHOR>
 * @namespace Main_Config_Components_Editor_Monaco_MinimapCard
 * @description Font Size Card
 */

import { useConfigFormat } from "@/main/config/internationalization/hook";
import { CONFIG_PROFILE } from "@/main/config/internationalization/profile";
import { HUMMINGBIRD_PREFERENCE_ITEM, PreferenceController } from "@imbricate-hummingbird/configuration";
import { FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody, UICardHeader, UIDivider, UIDropdown, UISpacer, createUIButton } from "@imbricate-hummingbird/ui";
import { FC, useState } from "react";
import { FaChevronDown, FaFont } from "react-icons/fa6";

export type ConfigEditorMonacoFontSizeCardProps = {
};

export const ConfigEditorMonacoFontSizeCard: FC<ConfigEditorMonacoFontSizeCardProps> = (
    _props: ConfigEditorMonacoFontSizeCardProps,
) => {

    const [markdownFontSize, setMarkdownFontSize] = useState(() => {

        return PreferenceController.getPreference(
            HUMMINGBIRD_PREFERENCE_ITEM.EDITOR_MONACO_FONT_SIZE_MARKDOWN,
        );
    });

    const configFormat = useConfigFormat();

    return (<StyledCard>
        <UICardHeader>
            <FaFont />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.FONT_SIZE)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody>
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                >
                    {configFormat.get(CONFIG_PROFILE.EDITOR_FONT_SIZE_DESCRIPTION)}
                </FormattedText>
            </div>
            <UIDropdown
                trigger={createUIButton({
                    endContent: (<FaChevronDown />),
                    children: (<FormattedText
                        usedFormats={[
                            configFormat,
                        ]}
                    >
                        {configFormat.get(
                            CONFIG_PROFILE.FONT_SIZE_MARKDOWN_$1,
                            `${markdownFontSize}px`,
                        )}
                    </FormattedText>),
                })}
                categories={[
                    {
                        categoryKey: "font-size",
                        items: [
                            8, 10, 12, 14, 16, 18, 20,
                            22, 24, 26, 28, 30, 32,
                        ].map((item) => {
                            return {
                                itemKey: String(item),
                                textValue: String(item),
                                isSelected: item === markdownFontSize,
                                content: `${item}px`,
                                onPress: () => {
                                    setMarkdownFontSize(item);

                                    PreferenceController.setPreference(
                                        HUMMINGBIRD_PREFERENCE_ITEM.EDITOR_MONACO_FONT_SIZE_MARKDOWN,
                                        item,
                                    );
                                },
                            };
                        }),
                    },
                ]}
            />
        </UICardBody>
    </StyledCard>);
};
