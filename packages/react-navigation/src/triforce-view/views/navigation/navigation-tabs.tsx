/**
 * <AUTHOR>
 * @namespace Main_Navigation_Views_Navigation_NavigationTabs
 * @description Navigation Tabs
 */

import { HUMMINGBIRD_NAVIGATION_TABS_DISPLAY_MODE, HUMMINGBIRD_PREFERENCE_ITEM, useDynamicPreferenceItem } from "@imbricate-hummingbird/configuration";
import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { UITabs } from "@imbricate-hummingbird/ui";
import { FC, useMemo } from "react";
import { FaDatabase } from "react-icons/fa";
import { RiCameraLensFill } from "react-icons/ri";
import { TbWorld } from "react-icons/tb";
import { useNavigationFormat } from "../../../internationalization/hook";
import { NAVIGATION_PROFILE } from "../../../internationalization/profile";
import { getNavigationDefaultTab, putNavigationLastUsedTab } from "../../../util/default-tab";
import { NavigationDatabases } from "../database/navigation-databases";
import { NavigationLenses } from "../lenses/navigation-lenses";
import { NavigationOrigins } from "../origins/navigation-origins";

export type NavigationTabsProps = {
};

export const NavigationTabs: FC<NavigationTabsProps> = (
    _props: NavigationTabsProps,
) => {

    const navigationFormat = useNavigationFormat();

    const navigationTabsDisplayMode = useDynamicPreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.NAVIGATION_TABS_DISPLAY_MODE,
    );

    const originsTitle = useMemo(() => {

        if (navigationTabsDisplayMode.preference === HUMMINGBIRD_NAVIGATION_TABS_DISPLAY_MODE.TEXT) {

            if (isFormatLoading(navigationFormat)) {
                return null;
            }

            return navigationFormat.get(NAVIGATION_PROFILE.ORIGINS);
        }

        return (<TbWorld
            className="text-xl"
        />);
    }, [navigationTabsDisplayMode.preference, isFormatLoading(navigationFormat)]);

    const lensesTitle = useMemo(() => {

        if (navigationTabsDisplayMode.preference === HUMMINGBIRD_NAVIGATION_TABS_DISPLAY_MODE.TEXT) {

            if (isFormatLoading(navigationFormat)) {
                return null;
            }

            return navigationFormat.get(NAVIGATION_PROFILE.LENSES);
        }

        return (<RiCameraLensFill
            className="text-xl"
        />);
    }, [navigationTabsDisplayMode.preference, isFormatLoading(navigationFormat)]);

    const databasesTitle = useMemo(() => {

        if (navigationTabsDisplayMode.preference === HUMMINGBIRD_NAVIGATION_TABS_DISPLAY_MODE.TEXT) {

            if (isFormatLoading(navigationFormat)) {
                return null;
            }

            return navigationFormat.get(NAVIGATION_PROFILE.DATABASES);
        }

        return (<FaDatabase
            className="text-large"
        />);
    }, [navigationTabsDisplayMode.preference, isFormatLoading(navigationFormat)]);

    const defaultSelectedKey = useMemo(() => {

        return getNavigationDefaultTab();
    }, []);

    return (<UITabs
        isFullWidth
        variant="solid"
        defaultSelectedKey={defaultSelectedKey}
        onSelectionChange={(newTab) => {

            putNavigationLastUsedTab(newTab as string);
        }}
        tabs={[
            {
                tabKey: "origins",
                tabTitle: originsTitle,
                panel: (<NavigationOrigins />),
            },
            {
                tabKey: "lenses",
                tabTitle: lensesTitle,
                panel: (<NavigationLenses />),
            },
            {
                tabKey: "databases",
                tabTitle: databasesTitle,
                panel: (<NavigationDatabases />),
            },
        ]}
    />);
};
