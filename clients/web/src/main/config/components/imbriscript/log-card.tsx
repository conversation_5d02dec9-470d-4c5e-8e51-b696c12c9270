/**
 * <AUTHOR>
 * @namespace Main_Config_Components_ImbriScript
 * @description Log Card
 */

import { HUMMINGBIRD_IMBRISCRIPT_LOG_PREVIEW_EDITOR_OPTION, HUMMINGBIRD_PREFERENCE_ITEM, usePreferenceItem } from "@imbricate-hummingbird/configuration";
import { FormattedRadioGroup, FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { FaDirections } from "react-icons/fa";
import { useConfigFormat } from "../../internationalization/hook";
import { CONFIG_PROFILE } from "../../internationalization/profile";

export const ConfigImbriScriptLogCard = () => {

    const configFormat = useConfigFormat();

    const imbriScriptLogPreviewEditorOption = usePreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.IMBRISCRIPT_LOG_PREVIEW_EDITOR_DEFAULT_OPTION,
    );

    return (<StyledCard>
        <UICardHeader>
            <FaDirections />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.IMBRISCRIPT_LOG_VIEWER)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-2"
        >
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                    className="text-sm text-gray-500"
                >
                    {configFormat.get(CONFIG_PROFILE.IMBRISCRIPT_LOG_VIEWER_DEFAULT_OPTION_DESCRIPTION)}
                </FormattedText>
            </div>
            <FormattedRadioGroup
                label={configFormat.get(CONFIG_PROFILE.IMBRISCRIPT_LOG_VIEWER_DEFAULT_OPTION)}
                selected={imbriScriptLogPreviewEditorOption.preference}
                onSelectedChange={(newSelected: string) => {

                    imbriScriptLogPreviewEditorOption.updatePreference(newSelected as HUMMINGBIRD_IMBRISCRIPT_LOG_PREVIEW_EDITOR_OPTION);
                }}
                format={configFormat}
                items={[
                    {
                        itemKey: HUMMINGBIRD_IMBRISCRIPT_LOG_PREVIEW_EDITOR_OPTION.PURE_TEXT,
                        itemProfileItem: CONFIG_PROFILE.IMBRISCRIPT_LOG_VIEWER_PURE_TEXT,
                    },
                    {
                        itemKey: HUMMINGBIRD_IMBRISCRIPT_LOG_PREVIEW_EDITOR_OPTION.MONACO,
                        itemProfileItem: CONFIG_PROFILE.IMBRISCRIPT_LOG_VIEWER_MONACO,
                    },
                ]}
            />
        </UICardBody>
    </StyledCard>);
};
