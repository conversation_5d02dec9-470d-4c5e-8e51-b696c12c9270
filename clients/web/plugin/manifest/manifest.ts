/**
 * <AUTHOR>
 * @namespace Manifest
 * @description Manifest
 */

import type { VitePWAOptions } from "vite-plugin-pwa";

// 10MB
const MAX_FILE_SIZE = 10000000;

export const manifestForPlugin: Partial<VitePWAOptions> = {

    injectRegister: false,
    strategies: "injectManifest",
    srcDir: "src",
    filename: "service-worker.ts",
    includeAssets: [
        "icon.png",
    ],
    injectManifest: {
        maximumFileSizeToCacheInBytes: MAX_FILE_SIZE,
    },
    workbox: {
        maximumFileSizeToCacheInBytes: MAX_FILE_SIZE,
    },
    manifestFilename: "manifest.json",
    scope: "/",
    manifest: {
        id: "imbricate-hummingbird",
        name: "Imbricate",
        short_name: "Imbricate",
        description: "The full functional web client for Imbricate. A fully customizable, automatable, open source notebook for engineers.",
        start_url: "/",
        display: "standalone",
        orientation: "portrait",
        background_color: "#FFFFFF",
        theme_color: "#FFFFFF",
        categories: [
            "productivity",
            "database",
            "notebook",
            "engineering",
        ],
        icons: [
            {
                purpose: "any",
                src: "/icons/144x144-transparent.png",
                sizes: "144x144",
                type: "image/png",
            },
            {
                purpose: "maskable",
                src: "/icons/144x144.png",
                sizes: "144x144",
                type: "image/png",
            },
            {
                purpose: "any",
                src: "/icons/512x512-transparent.png",
                sizes: "512x512",
                type: "image/png",
            },
            {
                purpose: "maskable",
                src: "/icons/512x512.png",
                sizes: "512x512",
                type: "image/png",
            },
        ],
        screenshots: [
            {
                src: "/screenshots/desktop.png",
                sizes: "1920x1080",
                type: "image/png",
                form_factor: "wide",
            },
        ],
    },
};
