/**
 * <AUTHOR>
 * @namespace Database_Hooks
 * @description Use Database Schema Magic Button
 */

import { useMagicButtonInitialItems } from "@/common/components/magic-button/hooks/use-initial-items";
import { UseMagicButtonResult, useMagicButton } from "@/common/components/magic-button/hooks/use-magic-button";
import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { useNavigateDatabaseDocumentsView } from "@imbricate-hummingbird/react-navigation";
import { FaDatabase } from "react-icons/fa6";
import { useDatabaseFormat } from "../internationalization/hook";
import { DATABASE_PROFILE } from "../internationalization/profile";

export const useDatabaseSchemaMagicButton = (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
): UseMagicButtonResult => {

    const format = useDatabaseFormat();

    const navigateToDatabaseDocuments = useNavigateDatabaseDocumentsView();

    const magicButtonResult = useMagicButton([
        {
            categoryKey: "database",
            categoryTitle: format.get(DATABASE_PROFILE.DATABASE),
            categoryOrder: 2,
        },
    ]);

    useMagicButtonInitialItems(
        magicButtonResult,
        !isFormatLoading(format),
        [
            {
                categoryKey: "database",
                itemKey: "view-database",
                icon: FaDatabase,
                title: format.get(DATABASE_PROFILE.VIEW_DOCUMENTS),
                description: format.get(DATABASE_PROFILE.VIEW_DOCUMENTS_DESCRIPTION),
                onPress: () => {

                    navigateToDatabaseDocuments(
                        originUniqueIdentifier,
                        databaseUniqueIdentifier,
                        {
                            replace: true,
                        },
                    );
                },
            },
        ],
    );

    return magicButtonResult;
};
