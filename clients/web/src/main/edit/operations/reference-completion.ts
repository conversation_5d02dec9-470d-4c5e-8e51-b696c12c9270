/**
 * <AUTHOR>
 * @namespace Edit_Actions
 * @description Reference Completion
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const findDatabaseForReferenceCompletion = (
    databaseUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Find Database For Reference Completion",
        actionDescription: `Find the database from [${databaseUniqueIdentifier}] for reference completion`,
        executerMetadata: metadata,
    };
};

export const findDocumentForReferenceCompletion = (
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Find Document For Reference Completion",
        actionDescription: `Find the document from [${databaseUniqueIdentifier}] with identifier [${documentUniqueIdentifier}] for reference completion`,
        executerMetadata: metadata,
    };
};
