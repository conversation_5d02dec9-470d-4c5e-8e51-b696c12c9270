/**
 * <AUTHOR>
 * @namespace Config
 * @description Configuration Card
 */

import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { StyledCard } from "@imbricate-hummingbird/react-components";
import { useNavigateConfigConfigurationExportView, useNavigateConfigConfigurationImportView } from "@imbricate-hummingbird/react-navigation";
import { UIButton, UICardBody, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { FaCog, FaFileExport, FaFileImport } from "react-icons/fa";
import { useConfigFormat } from "../internationalization/hook";
import { CONFIG_PROFILE } from "../internationalization/profile";

export const ConfigurationCard = () => {

    const configFormat = useConfigFormat();

    const navigateToConfigurationImport = useNavigateConfigConfigurationImportView();
    const navigateToConfigurationExport = useNavigateConfigConfigurationExportView();

    if (isFormatLoading(configFormat)) {
        return null;
    }

    return (<StyledCard>
        <UICardHeader>
            <FaCog />
            <UISpacer />
            {configFormat.get(CONFIG_PROFILE.APPLICATION_CONFIGURATION)}
        </UICardHeader>
        <UIDivider />
        <UICardBody>
            <UIButton
                radius="sm"
                variant="flat"
                startContent={<FaFileExport />}
                onPress={() => {
                    navigateToConfigurationExport();
                }}
            >
                {configFormat.get(CONFIG_PROFILE.EXPORT_CONFIGURATION)}
            </UIButton>
            <UISpacer />
            <UIButton
                radius="sm"
                variant="flat"
                startContent={<FaFileImport />}
                color="primary"
                onPress={() => {
                    navigateToConfigurationImport();
                }}
            >
                {configFormat.get(CONFIG_PROFILE.IMPORT_CONFIGURATION)}
            </UIButton>
        </UICardBody>
    </StyledCard>);
};