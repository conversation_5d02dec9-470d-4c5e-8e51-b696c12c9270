/**
 * <AUTHOR>
 * @namespace Lens_Components_Renders
 * @description Render Imbriscript
 */

import { LENS_CONFIG_SOURCE, LensConfigItem } from "@imbricate-hummingbird/configuration";
import { FC } from "react";
import { UsePropertyResponse } from "../../../property/types/use-property";
import { useText } from "../../../text/hooks/use-text";
import { RenderImbriscriptText } from "./render-imbriscript-text";

export type RenderImbriscriptLensProps = {

    readonly version: number;
    readonly triggerThrottleReload: () => void;

    readonly lensItem: LensConfigItem<LENS_CONFIG_SOURCE.IMBRISCRIPT>;
    readonly property: UsePropertyResponse;

    readonly originUniqueIdentifier: string;
    readonly textIdentifier?: string;
};

export const RenderImbriscriptLens: FC<RenderImbriscriptLensProps> = (
    props: RenderImbriscriptLensProps,
) => {

    const textContent = useText(
        props.originUniqueIdentifier,
        props.textIdentifier,
        [props.version],
    );

    const textContentSummary = typeof textContent === "symbol"
        ? textContent.toString()
        : textContent.textContent.length;

    if (typeof textContent === "symbol") {
        return null;
    }

    return (<RenderImbriscriptText
        imbriscriptText={textContent.textContent}
        context={{
            originUniqueIdentifier: props.originUniqueIdentifier,
        }}
        deps={[props.version, textContentSummary]}
    />);
};
