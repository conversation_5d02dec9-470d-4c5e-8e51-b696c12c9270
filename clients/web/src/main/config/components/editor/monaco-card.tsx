/**
 * <AUTHOR>
 * @namespace Main_Config_Components_Editor_MonacoCard
 * @description Monaco Card
 */

import { FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UIButton, UICardBody, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { FaCode } from "react-icons/fa";
import { FaRegFileCode } from "react-icons/fa6";
import { useConfigFormat } from "../../internationalization/hook";
import { CONFIG_PROFILE } from "../../internationalization/profile";
import { useNavigateConfigEditorMonacoRoute } from "../../navigation/hooks/use-config-route";

export type ConfigEditorMonacoCardProps = {
};

export const ConfigEditorMonacoCard: FC<ConfigEditorMonacoCardProps> = (
    _props: ConfigEditorMonacoCardProps,
) => {

    const configFormat = useConfigFormat();

    const navigateToConfigEditorMonaco = useNavigateConfigEditorMonacoRoute();

    return (<StyledCard>
        <UICardHeader>
            <FaCode />
            <UISpacer />
            {configFormat.get(CONFIG_PROFILE.MONACO_EDITOR)}
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-2"
        >
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                >
                    {configFormat.get(CONFIG_PROFILE.MONACO_EDITOR_DESCRIPTION)}
                </FormattedText>
            </div>
            <UIButton
                radius="sm"
                variant="flat"
                color="primary"
                startContent={<FaRegFileCode />}
                onPress={() => {
                    navigateToConfigEditorMonaco();
                }}
            >
                {configFormat.get(CONFIG_PROFILE.LAUNCH_MONACO_EDITOR_CONFIGURATION)}
            </UIButton>
        </UICardBody>
    </StyledCard>);
};
