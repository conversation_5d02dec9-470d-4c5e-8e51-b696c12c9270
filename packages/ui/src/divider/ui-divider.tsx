/**
 * <AUTHOR>
 * @package UI
 * @namespace Divider
 * @description Divider
 */

import { Divider } from "@heroui/divider";
import clsx from "clsx";
import { UIDividerOrientation } from "./types";

export type UIDividerProps = {

    readonly className?: string;

    readonly orientation?: UIDividerOrientation;
};

export const UIDivider: React.FC<UIDividerProps> = (
    props: UIDividerProps,
) => {

    if (props.orientation === "vertical") {

        return (<div
            className={clsx(
                "shrink-0 bg-divider border-none w-divider",
                props.className,
            )}
        />);
    }

    return (<Divider
        className={props.className}
        orientation={props.orientation}
    />);
};
