/**
 * <AUTHOR>
 * @namespace External
 * @description Vercel
 */

import { rootLogger } from "@/main/log/logger";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/react";
import React from "react";

const logger = rootLogger.fork({
    scopes: [
        "External",
        "Vercel",
    ],
});

export const VercelMonitoring: React.FC = () => {

    const isDevelopment = process.env.NODE_ENV === "development";

    if (isDevelopment) {

        logger.warning("Vercel is disabled in development mode");
        return null;
    }

    return (<React.Fragment>
        <Analytics />
        <SpeedInsights />
    </React.Fragment>);
};
