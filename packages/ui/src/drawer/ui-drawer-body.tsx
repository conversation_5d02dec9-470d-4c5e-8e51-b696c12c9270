/**
 * <AUTHOR>
 * @package UI
 * @namespace Drawer
 * @description Drawer Body
 */

import { DrawerBody } from "@heroui/drawer";
import type { FC, ReactNode } from "react";

export type UIDrawerBodyProps = {

    readonly className?: string;

    readonly children?: ReactNode;
};

export const UIDrawerBody: FC<UIDrawerBodyProps> = (
    props: UIDrawerBodyProps,
) => {

    return (<DrawerBody
        className={props.className}
    >
        {props.children}
    </DrawerBody>);
};
