/**
 * <AUTHOR>
 * @namespace Document_Views_DocumentView
 * @description Document View
 */

import { HeaderGroup } from "@/common/components/header-group/header-group";
import { HEADER_GROUP_GROUP } from "@/common/components/header-group/types";
import { MagicButton } from "@/common/components/magic-button/magic-button";
import { useOldDocument } from "@/common/transfer/hooks/use-document";
import { $ERROR, HookSymbol } from "@imbricate-hummingbird/global-symbol";
import { ActionDescription } from "@imbricate-hummingbird/interceptor-core";
import { getRouteDatabaseDocumentsView } from "@imbricate-hummingbird/navigation-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { useAsyncTitle } from "@imbricate-hummingbird/react-common";
import { LoadingWrapper } from "@imbricate-hummingbird/react-components";
import { TransferDocument, TransferDocumentProperty, findPrimaryTransferDocumentProperty } from "@imbricate-hummingbird/transfer-core";
import { UIBreadcrumbs, UINavbar } from "@imbricate-hummingbird/ui";
import { IImbricateDatabase } from "@imbricate/core";
import { FC } from "react";
import { useParams } from "react-router-dom";
import { ErrorScreen } from "../../../common/components/error-screen";
import { UseDatabaseResponseSymbol, useDatabase } from "../../../database/hooks/use-database";
import { DocumentPropertyCards } from "../../components/property-card/property-cards";
import { useDocumentViewMagicButton } from "../../hooks/use-document-view-magic-button";
import { useDocumentFormat } from "../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../internationalization/profile";

const createGetDocumentAction = (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
): ActionDescription => {

    return {
        actionName: "Get Document",
        actionDescription: `Get document from database ${databaseUniqueIdentifier} for document ${documentUniqueIdentifier}`,
        executerMetadata: {
            executer: import.meta.url,
        },
        actionPayload: {
            originUniqueIdentifier,
            databaseUniqueIdentifier,
            documentUniqueIdentifier,
        },
    };
};

export type DocumentViewProps = {

    readonly origin: ImbricateOriginObject;
};

// LAZY LOAD ONLY
const DocumentView: FC<DocumentViewProps> = (
    props: DocumentViewProps,
) => {

    const documentFormat = useDocumentFormat();

    const params = useParams();
    const databaseUniqueIdentifier: string =
        params["database-unique-identifier"] as string;
    const documentUniqueIdentifier: string =
        params["document-unique-identifier"] as string;

    const database: IImbricateDatabase | UseDatabaseResponseSymbol =
        useDatabase(databaseUniqueIdentifier);

    const document: TransferDocument | HookSymbol =
        useOldDocument(
            props.origin.origin.uniqueIdentifier,
            databaseUniqueIdentifier,
            documentUniqueIdentifier,
            createGetDocumentAction(
                props.origin.origin.uniqueIdentifier,
                databaseUniqueIdentifier,
                documentUniqueIdentifier,
            ),
        );

    useAsyncTitle(
        () => typeof document !== "symbol" && typeof database !== "symbol",
        () => {

            if (typeof document === "symbol" || typeof database === "symbol") {
                return [];
            }

            const primaryProperty: TransferDocumentProperty | null =
                findPrimaryTransferDocumentProperty(
                    database.schema,
                    document.properties,
                );

            return [
                primaryProperty?.propertyValue ?? document.documentUniqueIdentifier,
                "Document",
            ];
        },
        [typeof document],
    );

    const magicButtonResult = useDocumentViewMagicButton(
        props.origin,
        databaseUniqueIdentifier,
        documentUniqueIdentifier,
    );

    if (document === $ERROR) {

        return (<ErrorScreen
            color="warning"
            title={documentFormat.get(DOCUMENT_PROFILE.UNABLE_TO_LOAD_DOCUMENT)}
            message={<div>
                <div>{documentFormat.get(DOCUMENT_PROFILE.DATABASE)}: {databaseUniqueIdentifier}</div>
                <div>{documentFormat.get(DOCUMENT_PROFILE.DOCUMENT)}: {documentUniqueIdentifier}</div>
            </div>}
        />);
    }

    if (!props.origin
        || typeof document === "symbol"
        || typeof database === "symbol") {

        return (<LoadingWrapper
            debugDescription="Document View"
            fullHeight
        />);
    }

    const primary: string | null = findPrimaryTransferDocumentProperty(
        database.schema,
        document.properties,
    )?.propertyValue ?? document.documentUniqueIdentifier;

    return (<div
        className="h-full overflow-auto"
    >
        <UINavbar
            isFullWidth
            isBordered
            startContent={<HeaderGroup
                group={HEADER_GROUP_GROUP.DOCUMENT}
                payload={{
                    originUniqueIdentifier: props.origin.origin.uniqueIdentifier,
                    databaseUniqueIdentifier: database.uniqueIdentifier,
                    documentUniqueIdentifier: document.documentUniqueIdentifier,
                }}
            />}
            coreContent={<p className="font-bold text-xl">
                {primary}
            </p>}
            endContent={<MagicButton
                magicButtonResult={magicButtonResult}
            />}
        />
        <UIBreadcrumbs
            items={[
                {
                    label: database.databaseName,
                    href: getRouteDatabaseDocumentsView(database.uniqueIdentifier),
                },
                {
                    label: document.documentUniqueIdentifier,
                },
            ]}
        />
        <DocumentPropertyCards
            databaseUniqueIdentifier={databaseUniqueIdentifier}
            documentUniqueIdentifier={documentUniqueIdentifier}
            schema={database.schema}
            document={document}
        />
    </div>);
};
export default DocumentView;
