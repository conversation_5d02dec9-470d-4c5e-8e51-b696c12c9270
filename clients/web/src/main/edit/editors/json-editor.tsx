/**
 * <AUTHOR>
 * @namespace Edit_Editors
 * @description Json Editor
 */

import { UseMagicButtonResult } from "@/common/components/magic-button/hooks/use-magic-button";
import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { CreateMonacoEditorResponse } from "@imbricate-hummingbird/monaco-core";
import { createMonacoEditorForJson } from "@imbricate-hummingbird/monaco-json";
import { IImbricateProperty, IMBRICATE_PROPERTY_TYPE, ImbricateTextManagerCreateTextOutcome } from "@imbricate/core";
import * as monaco from "monaco-editor";
import React, { FC, useEffect } from "react";
import { OldDataCentral } from "../../data/old-data-central";
import { useText } from "../../text/hooks/use-text";
import { S_TextLoading, S_TextNotFound, S_TextNotInitialized } from "../../text/types/use-text";
import { createFindOriginForEditorAction } from "../actions/find-origin-for-editor";
import { GetValueRef } from "../types/editor-refs";

export type EditJsonEditorProps = {

    readonly darkMode: boolean;

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;
    readonly propertyUniqueIdentifier: string;

    readonly textUniqueIdentifier?: string;

    readonly primaryValue: IImbricateProperty<IMBRICATE_PROPERTY_TYPE> | null;

    readonly getValueRef: GetValueRef;
    readonly onValueChange: () => void;

    readonly magicButtonResult: UseMagicButtonResult;
};

// LAZY LOAD ONLY
const EditJsonEditor: FC<EditJsonEditorProps> = (props: EditJsonEditorProps) => {

    const editorRef = React.useRef<monaco.editor.IStandaloneCodeEditor | null>(null);
    const onboardedRef = React.useRef(false);

    const textContent = useText(
        props.originUniqueIdentifier,
        props.textUniqueIdentifier,
    );

    useEffect(() => {

        if (textContent === S_TextLoading
            || textContent === S_TextNotFound
        ) {
            return;
        }

        const editorValue = textContent === S_TextNotInitialized
            ? "{}"
            : textContent.textContent;

        const container = document.getElementById("edit-view-monaco");
        if (!container) {
            return;
        }

        if (onboardedRef.current) {
            return;
        }
        onboardedRef.current = true;

        const editorResponse: CreateMonacoEditorResponse =
            createMonacoEditorForJson(
                container,
                editorValue,
                props.darkMode,
            );
        const editor = editorResponse.editor;

        const model = editor.getModel();
        if (!model) {
            return;
        }

        model.onDidChangeContent(() => {
            props.onValueChange();
        });

        editorRef.current = editor;

        props.getValueRef.current = async () => {

            const updatedTextContent = editor.getValue();

            const origin = await ActionCentral.getInstance().executeAction(
                async (
                    actionIdentifier: string,
                    recordIdentifier: string,
                ) => {
                    return OldDataCentral.getInstance()
                        .getOrigin(
                            props.originUniqueIdentifier,
                            actionIdentifier,
                            recordIdentifier,
                        );
                },
                createFindOriginForEditorAction(
                    props.originUniqueIdentifier,
                    {
                        executer: import.meta.url,
                    },
                ),
            );

            if (!origin) {
                throw new Error("Origin not found");
            }

            const createdText: ImbricateTextManagerCreateTextOutcome =
                await origin.origin.getTextManager()
                    .createText(updatedTextContent);

            if (typeof createdText === "symbol") {
                throw new Error("Failed to create text");
            }

            return createdText.text.uniqueIdentifier;
        };

        return () => {
            editor.dispose();

            editorRef.current = null;
            onboardedRef.current = false;

            props.getValueRef.current = null;
        };
    }, [typeof textContent === "symbol" ? textContent.toString() : typeof textContent]);

    return (<div
        id="edit-view-monaco"
        className="flex-1"
    />);
};
export default EditJsonEditor;
