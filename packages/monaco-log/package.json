{"name": "@imbricate-hummingbird/monaco-log", "description": "Log implementation for Monaco", "private": true, "main": "src/index.ts", "scripts": {"test": "jest", "coverage": "jest --coverage", "compile": "tsc --project ./typescript/tsconfig.compile.json", "lint": "eslint src"}, "dependencies": {"@imbricate-hummingbird/global-constant": "workspace:*", "@imbricate-hummingbird/logger": "workspace:*", "@imbricate-hummingbird/monaco-core": "workspace:*", "@imbricate-hummingbird/react-components": "workspace:*", "@imbricate-hummingbird/script-core": "workspace:*", "@imbricate-hummingbird/theme-core": "workspace:*", "@sudoo/internationalization": "2.1.0", "monaco-editor": "catalog:", "react": "catalog:"}, "devDependencies": {"@imbricate-hummingbird/config": "workspace:*", "@types/react": "catalog:", "eslint": "catalog:"}}