/**
 * <AUTHOR>
 * @namespace Config_Components
 * @description Brand Banner
 */

import React, { FC } from "react";

export type ConfigBrandBannerProps = {

    readonly className?: string;
};

export const ConfigBrandBanner: FC<ConfigBrandBannerProps> = (
    props: ConfigBrandBannerProps,
) => {

    const constantText = "IMBRICATE".repeat(24).split("").join(" ");
    const classNames: string[] = [
        "font-mono text-tiny select-none pointer-events-none overflow-hidden -mt-1 text-nowrap whitespace-nowrap",
        "h-full",
    ];

    if (props.className) {
        classNames.push(props.className);
    }

    return (<div
        style={{
            writingMode: "vertical-rl",
            textOrientation: "mixed",
        }}
        className={classNames.join(" ")}
    >
        <div
            className="break-keep text-nowrap h-full overflow-hidden"
        >
            A T E {constantText}
        </div>
        <div
            className="break-keep text-nowrap h-full overflow-hidden"
        >
            {constantText}
        </div>
        <div
            className="break-keep text-nowrap h-full overflow-hidden"
        >
            R I C A T E {constantText}
        </div>
        <div
            className="break-keep text-nowrap h-full overflow-hidden"
        >
            A T E {constantText}
        </div>
        <div
            className="break-keep text-nowrap h-full overflow-hidden"
        >
            {constantText}
        </div>
    </div>);
};
