/**
 * <AUTHOR>
 * @package web
 * @namespace Main_Document_Hooks
 * @description Use Document View Magic Button
 */

import { useMagicButtonInitialItems } from "@/common/components/magic-button/hooks/use-initial-items";
import { UseMagicButtonResult, useMagic<PERSON>utton } from "@/common/components/magic-button/hooks/use-magic-button";
import { RECENT_TYPE, removeRecentItem } from "@imbricate-hummingbird/configuration";
import { DOC_CAST_SOURCE_TYPE, useDocCastModal } from "@imbricate-hummingbird/doc-cast";
import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { isDefaultEmptyFormat } from "@imbricate-hummingbird/internationalization";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { OriginWorkerDataCentral } from "@imbricate-hummingbird/origin-central-web-worker";
import { openSuccessToaster } from "@imbricate-hummingbird/react-components";
import { useNavigateDatabaseDocumentsView, useNavigateDocumentEditHistoryView } from "@imbricate-hummingbird/react-navigation";
import { FaTrash } from "react-icons/fa";
import { MdHistory } from "react-icons/md";
import { TbTransfer } from "react-icons/tb";
import { createDeleteDocumentAction } from "../actions/delete-document";
import { useDocumentFormat } from "../internationalization/hook";
import { DOCUMENT_PROFILE } from "../internationalization/profile";

export const useDocumentViewMagicButton = (
    origin: ImbricateOriginObject,
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
): UseMagicButtonResult => {

    const documentFormat = useDocumentFormat();

    const navigateToDocumentEditHistory = useNavigateDocumentEditHistoryView();
    const navigateToDatabaseDocuments = useNavigateDatabaseDocumentsView();

    const docCastModal = useDocCastModal();

    const magicButtonResult = useMagicButton([
        {
            categoryKey: "document",
            categoryTitle: documentFormat.get(DOCUMENT_PROFILE.DOCUMENT),
        },
        {
            categoryKey: "danger-zone",
            categoryTitle: documentFormat.get(DOCUMENT_PROFILE.DANGER_ZONE),
        },
    ]);

    useMagicButtonInitialItems(
        magicButtonResult,
        !isDefaultEmptyFormat(documentFormat),
        [
            {
                categoryKey: "document",
                itemKey: "edit-history",
                icon: MdHistory,
                title: documentFormat.get(DOCUMENT_PROFILE.EDIT_HISTORY),
                description: documentFormat.get(DOCUMENT_PROFILE.VIEW_EDIT_HISTORY),
                redirectIndicator: true,
                onPress: () => {

                    navigateToDocumentEditHistory(
                        origin.origin.uniqueIdentifier,
                        databaseUniqueIdentifier,
                        documentUniqueIdentifier,
                        {
                            replace: true,
                        },
                    );
                },
            },
            {
                categoryKey: "document",
                itemKey: "start-doc-cast",
                icon: TbTransfer,
                title: documentFormat.get(DOCUMENT_PROFILE.ARRANGE_DOCUMENT),
                description: documentFormat.get(DOCUMENT_PROFILE.ARRANGE_DOCUMENT_DESCRIPTION),
                quickAccess: true,
                onPress: () => {

                    docCastModal.addSources([{
                        type: DOC_CAST_SOURCE_TYPE.DOCUMENT_STACK_IDENTIFIER,
                        payload: {
                            originUniqueIdentifier: origin.origin.uniqueIdentifier,
                            databaseUniqueIdentifier,
                            documentUniqueIdentifier,
                        },
                    }]);
                },
            },
            {
                categoryKey: "danger-zone",
                itemKey: "delete-document",
                icon: FaTrash,
                title: documentFormat.get(DOCUMENT_PROFILE.DELETE_DOCUMENT),
                description: documentFormat.get(DOCUMENT_PROFILE.DELETE_DOCUMENT_DESCRIPTION),
                color: "danger",
                onPress: async () => {

                    const deleteResult = await ActionCentral.getInstance().executeAction(
                        async (
                            actionIdentifier: string,
                            recordIdentifier: string,
                        ) => {

                            return await OriginWorkerDataCentral.getInstance().deleteDocument(
                                actionIdentifier,
                                recordIdentifier,
                                origin.origin.uniqueIdentifier,
                                databaseUniqueIdentifier,
                                documentUniqueIdentifier,
                            );
                        },
                        createDeleteDocumentAction(
                            databaseUniqueIdentifier,
                            documentUniqueIdentifier,
                            {
                                executer: import.meta.url,
                            },
                        ),
                    );

                    if (deleteResult.success) {

                        removeRecentItem(
                            RECENT_TYPE.DOCUMENT,
                            {
                                originUniqueIdentifier: origin.origin.uniqueIdentifier,
                                databaseUniqueIdentifier,
                                documentUniqueIdentifier,
                            },
                        );

                        navigateToDatabaseDocuments(
                            origin.origin.uniqueIdentifier,
                            databaseUniqueIdentifier,
                            {
                                replace: true,
                            },
                        );

                        openSuccessToaster(
                            documentFormat.get(DOCUMENT_PROFILE.DOCUMENT_DELETE_SUCCESS),
                            {
                                description: documentFormat.get(DOCUMENT_PROFILE.DOCUMENT_DELETE_SUCCESS_DESCRIPTION),
                            },
                        );
                    }
                },
            },
        ],
    );

    return magicButtonResult;
};
