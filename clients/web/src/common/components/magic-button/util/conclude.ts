/**
 * <AUTHOR>
 * @namespace Common_Components_MagicButton_Util
 * @description Conclude
 */

import { UseMagicButtonResultCategory } from "../hooks/use-magic-button";
import { MagicButtonItem } from "../types/button-item";

export type ConcludeMagicButtonCategory = {

    readonly categoryKey: string;

    readonly items: MagicButtonItem[];
};

export const concludeMagicButtonCategories = (
    items: MagicButtonItem[],
    categories: UseMagicButtonResultCategory[],
): ConcludeMagicButtonCategory[] => {

    const categoriesKeys = items.map((item) => item.categoryKey);
    const uniqueCategories = [...new Set(categoriesKeys)];

    const categoriesMap = uniqueCategories.flatMap((category) => {

        const matchedItems = items.filter((item) => {

            return item.categoryKey === category;
        });

        if (matchedItems.length === 0) {
            return [];
        }

        const sortedItems = matchedItems.sort((a, b) => {

            return (a.order ?? 0) - (b.order ?? 0);
        });

        return {
            categoryKey: category,
            items: sortedItems,
        };
    });

    const sortedCategories = categoriesMap.sort((a, b) => {

        const aCategory = categories.find((category) => category.categoryKey === a.categoryKey);
        const bCategory = categories.find((category) => category.categoryKey === b.categoryKey);

        return (aCategory?.categoryOrder ?? 0) - (bCategory?.categoryOrder ?? 0);
    });

    return sortedCategories;
};
