/**
 * <AUTHOR>
 * @namespace Document_Components_EditHistoryOperation
 * @description Unknown
 */

import { DocumentEditOperation, IMBRICATE_DOCUMENT_EDIT_TYPE } from "@imbricate/core";
import React, { FC } from "react";

export type DocumentEditRecordsUnknownOperationProps = {

    readonly operation: DocumentEditOperation<IMBRICATE_DOCUMENT_EDIT_TYPE>;
};

export const DocumentEditRecordsUnknownOperation: FC<DocumentEditRecordsUnknownOperationProps> = (
    props: DocumentEditRecordsUnknownOperationProps,
) => {

    return (<div
        className="flex gap-1"
    >
        {JSON.stringify(props.operation.value, null, 2)}
    </div>);
};
