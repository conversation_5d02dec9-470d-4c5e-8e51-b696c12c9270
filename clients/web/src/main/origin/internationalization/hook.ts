/**
 * <AUTHOR>
 * @namespace Origin_Internationalization
 * @description Hook
 */

import { defaultEmptyFormat, useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { useEffect, useState } from "react";
import { originInternationalization } from "./intl";
import { ORIGIN_PROFILE } from "./profile";

export const useOldOriginFormat = (): SudooFormat<ORIGIN_PROFILE> => {

    const locale = useLocale();

    const [format, setFormat] = useState<SudooFormat<ORIGIN_PROFILE>>(defaultEmptyFormat);

    useEffect(() => {

        const execute = async () => {

            const format = await originInternationalization.format(locale);
            setFormat(format);
        };

        execute();
    }, [locale]);

    return format;
};
