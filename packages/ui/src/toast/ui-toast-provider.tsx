/**
 * <AUTHOR>
 * @package UI
 * @namespace Toast
 * @description UI Toast Provider
 */

import { ToastProvider } from "@heroui/toast";
import { UIToastPlacement } from "./types";

const DEFAULT_TOASTER_DURATION = 5000;

export type UIToastProviderProps = {

    readonly timeout?: number;
    readonly maxVisibleToasts?: number;

    readonly placement?: UIToastPlacement;
};

export const UIToastProvider: React.FC<UIToastProviderProps> = (
    props: UIToastProviderProps,
) => {

    return (
        <ToastProvider
            toastProps={{
                timeout: props.timeout ?? DEFAULT_TOASTER_DURATION,
            }}
            placement={props.placement}
            maxVisibleToasts={props.maxVisibleToasts}
        />
    );
};
