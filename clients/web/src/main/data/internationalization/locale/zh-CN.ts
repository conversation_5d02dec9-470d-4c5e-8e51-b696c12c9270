/**
 * <AUTHOR>
 * @namespace Data_Internationalization_Locale
 * @description Zh-CN
 */

import { DATA_PROFILE } from "../profile";

export const zhCNDataProfile: Record<DATA_PROFILE, string> = {

    [DATA_PROFILE.CONFIRM_PROCEED_WITH_ERROR]: "确认忽略错误并继续",
    [DATA_PROFILE.ERROR_OCCURRED]: "出现了错误",
    [DATA_PROFILE.ERROR_OCCURRED_WHEN_PROCESSING_DATA]: "处理数据时出现了错误",
    [DATA_PROFILE.PROCEED_WITH_ERROR]: "在错误中继续",
    [DATA_PROFILE.PROCEED_WITH_ERROR_DESCRIPTION]: "您可以继续使用应用程序，但应用程序可能无法按预期工作。如果您确定要在错误中继续，请确保在继续之前导出您的数据和未保存的数据。",
    [DATA_PROFILE.REASON]: "原因：",
    [DATA_PROFILE.RETRY]: "重试",
    [DATA_PROFILE.RETRY_AGAIN]: "再次重试",
};
