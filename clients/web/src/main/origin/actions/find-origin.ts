/**
 * <AUTHOR>
 * @namespace Origin_Actions
 * @description Find Origin
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createFindOriginAction = (
    databaseUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Find Origin",
        actionDescription: `Find the origin for [${databaseUniqueIdentifier}]`,
        executerMetadata: metadata,
    };
};
