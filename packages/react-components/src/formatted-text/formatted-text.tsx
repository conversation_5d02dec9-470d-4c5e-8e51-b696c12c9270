/**
 * <AUTHOR>
 * @package React Components
 * @namespace FormattedText
 * @description Formatted Text
 */

import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { UISkeleton } from "@imbricate-hummingbird/ui";
import { SudooFormat } from "@sudoo/internationalization";
import clsx from "clsx";
import type { FC, ReactNode } from "react";

export type FormattedTextProps = {

    readonly usedFormats: SudooFormat<any>[];

    readonly children: ReactNode;

    readonly className?: string;
    readonly placeholderClassName?: string;
};

const getIsReady = (
    props: FormattedTextProps,
): boolean => {

    const usingFormat: boolean = props.usedFormats.length > 0;

    if (!usingFormat) {
        return true;
    }

    return props.usedFormats.every((each: SudooFormat<any>) => {
        return !isFormatLoading(each);
    });
};

export const FormattedText: FC<FormattedTextProps> = (
    props: FormattedTextProps,
) => {

    const isReady: boolean = getIsReady(props);

    if (!isReady) {

        return (<UISkeleton
            className={clsx(
                "w-32 h-3 rounded-sm",
                props.placeholderClassName,
            )}
        />);
    }

    return (<span
        className={clsx(
            props.className,
        )}
    >
        {props.children}
    </span>);
};
