/**
 * <AUTHOR>
 * @namespace Application
 * @description Application Wrapper
 */

import "../index.css";

import { FullLoadingWrapper, LazyLoadComponent } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const Application = LazyLoadComponent(
    () => import("./application"),
    "Application",
);

export const ApplicationWrapper: FC = () => {

    return (<React.Suspense
        fallback={<FullLoadingWrapper
            debugDescription="Application"
        />}
    >
        <Application />
    </React.Suspense>);
};
