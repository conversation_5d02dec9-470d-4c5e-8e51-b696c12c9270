/**
 * <AUTHOR>
 * @namespace Landing_ImportConfiguration
 * @description Landing Import Configuration View
 */

import { useTitle } from "@imbricate-hummingbird/react-common";
import { FC } from "react";
import { ConfigConfigurationImportCard } from "../../config/configuration/import/import-card";
import { LandingMainWrapper } from "../components/landing-wrapper";

export type LandingImportConfigurationViewProps = {
};

// LAZY LOAD ONLY
const LandingImportConfigurationView: FC<LandingImportConfigurationViewProps> = (
    _props: LandingImportConfigurationViewProps,
) => {

    useTitle([
        "Import Configuration",
    ], []);

    return (<LandingMainWrapper>
        <div
            className="w-full p-4"
        >
            <ConfigConfigurationImportCard />
        </div>
    </LandingMainWrapper>);
};
export default LandingImportConfigurationView;
