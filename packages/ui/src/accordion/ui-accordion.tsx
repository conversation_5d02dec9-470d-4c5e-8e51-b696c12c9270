/**
 * <AUTHOR>
 * @package UI
 * @namespace Accordion
 * @description Accordion
 */

import { Accordion, AccordionItem } from "@heroui/accordion";
import React from "react";
import { UIAccordionVariant } from "./types";

export type UIAccordionProps = {

    readonly className?: string;

    readonly itemKey: string;
    readonly itemTitle: string;

    readonly isCompact?: boolean;

    readonly variant?: UIAccordionVariant;

    readonly children?: React.ReactNode;
    readonly startContent?: React.ReactNode;
};

export const UIAccordion: React.FC<UIAccordionProps> = (
    props: UIAccordionProps,
) => {

    return (
        <Accordion
            isCompact={props.isCompact}
            variant={props.variant}
            className={props.className}
        >
            <AccordionItem
                key={props.itemKey}
                aria-label={props.itemTitle}
                title={props.itemTitle}
                startContent={props.startContent}
            >
                {props.children}
            </AccordionItem>
        </Accordion>
    );
};
