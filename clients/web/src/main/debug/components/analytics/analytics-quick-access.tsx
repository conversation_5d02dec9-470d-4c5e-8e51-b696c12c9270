/**
 * <AUTHOR>
 * @namespace Debug_Components_Analytics
 * @description Analytics Quick Access
 */

import { StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody } from "@imbricate-hummingbird/ui";
import * as Sentry from "@sentry/react";
import React, { useMemo } from "react";

export const AnalyticsQuickAccess: React.FC = () => {

    const sentryTraceId = useMemo(() => {

        const traceId = Sentry.getCurrentScope().getPropagationContext().traceId;
        return traceId;
    }, []);

    return (<StyledCard>
        <UICardBody>
            <div
                className="flex gap-2 font-mono"
            >
                <div
                    className="text-tiny text-foreground-500"
                >
                    Sentry Trace ID:
                </div>
                <div
                    className="text-tiny"
                >
                    {sentryTraceId}
                </div>
            </div>
        </UICardBody>
    </StyledCard>);
};
