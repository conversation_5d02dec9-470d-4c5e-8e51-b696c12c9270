/**
 * <AUTHOR>
 * @namespace Navigation_Actions
 * @description Get Random Document
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createGetRandomDocumentAction = (
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Get Random Document",
        actionDescription: "Get a random document to surprise you",
        executerMetadata: metadata,
    };
};
