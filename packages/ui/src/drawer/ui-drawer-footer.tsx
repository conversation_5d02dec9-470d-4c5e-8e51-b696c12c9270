/**
 * <AUTHOR>
 * @package UI
 * @namespace Drawer
 * @description Drawer Footer
 */

import { DrawerFooter } from "@heroui/drawer";
import type { FC, ReactNode } from "react";

export type UIDrawerFooterProps = {

    readonly className?: string;

    readonly children?: ReactNode;
};

export const UIDrawerFooter: FC<UIDrawerFooterProps> = (
    props: UIDrawerFooterProps,
) => {

    return (<DrawerFooter
        className={props.className}
    >
        {props.children}
    </DrawerFooter>);
};   
