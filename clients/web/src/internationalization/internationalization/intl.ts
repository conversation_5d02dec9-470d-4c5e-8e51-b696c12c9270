/**
 * <AUTHOR>
 * @namespace Config_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooLazyInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";

export const internationalizationInternationalization: SudooLazyInternationalization<IETF_LOCALE> =
    SudooLazyInternationalization.create<IETF_LOCALE>(
        DEFAULT_LOCALE,
    );

internationalizationInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/en-US").then(
        (module) => module.enUSInternationalizationProfile,
    ),
);

internationalizationInternationalization.mergeLazyProfile(
    IETF_LOCALE.SPANISH_SPAIN,
    import("./locale/es-ES").then(
        (module) => module.esESInternationalizationProfile,
    ),
);

internationalizationInternationalization.mergeLazyProfile(
    IETF_LOCALE.FRENCH_FRANCE,
    import("./locale/fr-FR").then(
        (module) => module.frFRInternationalizationProfile,
    ),
);

internationalizationInternationalization.mergeLazyProfile(
    IETF_LOCALE.JAPANESE_JAPAN,
    import("./locale/ja-JP").then(
        (module) => module.jaJPInternationalizationProfile,
    ),
);

internationalizationInternationalization.mergeLazyProfile(
    IETF_LOCALE.KOREAN_KOREA,
    import("./locale/ko-KR").then(
        (module) => module.koKRInternationalizationProfile,
    ),
);

internationalizationInternationalization.mergeLazyProfile(
    IETF_LOCALE.RUSSIAN_RUSSIA,
    import("./locale/ru-RU").then(
        (module) => module.ruRUInternationalizationProfile,
    ),
);

internationalizationInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    import("./locale/zh-CN").then(
        (module) => module.zhCNInternationalizationProfile,
    ),
);

internationalizationInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_TRADITIONAL,
    import("./locale/zh-TW").then(
        (module) => module.zhTWInternationalizationProfile,
    ),
);

export const languageInternationalization: SudooLazyInternationalization<IETF_LOCALE> =
    SudooLazyInternationalization.create<IETF_LOCALE>(
        IETF_LOCALE.ENGLISH_UNITED_STATES,
    );

languageInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/language").then(
        (module) => module.languageInternationalizationProfile,
    ),
);
