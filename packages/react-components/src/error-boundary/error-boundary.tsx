/**
 * <AUTHOR>
 * @namespace ErrorBoundary
 * @description Styled Error Boundary
 */
import { UIColor } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { ErrorBoundary } from "react-error-boundary";
import { ErrorMessageAlert } from "../error-message/error-alert";
import { ImbricateLogo } from "../logo/imbricate-logo";

export type StyledErrorBoundaryProps = {

    readonly title?: string;
    readonly message?: React.ReactNode;

    readonly color?: UIColor;

    readonly children: React.ReactNode;
};

export const StyledErrorBoundary: FC<StyledErrorBoundaryProps> = (
    props: StyledErrorBoundaryProps,
) => {

    return (<ErrorBoundary
        fallback={<div
            className="flex flex-col gap-2 h-full p-1 justify-center items-center"
        >
            <div
                className="flex flex-col gap-2 items-center w-full"
            >
                <ImbricateLogo
                    size="large"
                />
                <ErrorMessageAlert
                    title={props.title}
                    message={props.message}
                    color={props.color}
                />
            </div>
        </div>}
    >
        {props.children}
    </ErrorBoundary>);
};
