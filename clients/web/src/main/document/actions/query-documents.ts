/**
 * <AUTHOR>
 * @namespace Document_Actions
 * @description Query Documents
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createQueryDocumentsAction = (
    databaseUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Query Documents",
        actionDescription: `Query the documents from [${databaseUniqueIdentifier}]`,
        executerMetadata: metadata,
    };
};
