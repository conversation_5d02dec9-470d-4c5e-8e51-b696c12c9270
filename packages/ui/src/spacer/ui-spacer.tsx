/**
 * <AUTHOR>
 * @package UI
 * @namespace Spacer
 * @description Spacer
 */

import { Spacer, SpacerProps } from "@heroui/spacer";
import clsx from "clsx";
import { UISize } from "../common/types";
import { getUISpacerSizing } from "./get-sizing";

export type UISpacerProps = {

    readonly className?: string;

    readonly isFlexFill?: boolean;

    readonly x?: UISize;
    readonly y?: UISize;
};

export const UISpacer: React.FC<UISpacerProps> = (
    props: UISpacerProps,
) => {

    const xSpace: SpacerProps["x"] | undefined =
        props.x ? getUISpacerSizing(props.x) : undefined;
    const ySpace: SpacerProps["y"] | undefined =
        props.y ? getUISpacerSizing(props.y) : undefined;

    return (<Spacer
        className={clsx({
            "flex-1": props.isFlexFill,
        }, props.className)}
        x={xSpace}
        y={ySpace}
    />);
};
