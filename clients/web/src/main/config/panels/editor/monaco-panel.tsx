/**
 * <AUTHOR>
 * @namespace Main_Config_Panels_Editor_MonacoPanel
 * @description Monaco Panel
 */

import { FC } from "react";
import { ConfigEditorMonacoFontSizeCard } from "../../components/editor/monaco/font-size-card";
import { ConfigEditorMonacoLineNumberCard } from "../../components/editor/monaco/line-number-card";
import { ConfigEditorMonacoMinimapCard } from "../../components/editor/monaco/minimap-card";

export type ConfigEditorMonacoPanelProps = {
};

export const ConfigEditorMonacoPanel: FC<ConfigEditorMonacoPanelProps> = (
    _props: ConfigEditorMonacoPanelProps,
) => {

    return (<div
        className="flex flex-col gap-2 pr-2 py-2 flex-1"
    >
        <ConfigEditorMonacoLineNumberCard />
        <ConfigEditorMonacoFontSizeCard />
        <ConfigEditorMonacoMinimapCard />
    </div>);
};
