/**
 * <AUTHOR>
 * @package UI
 * @namespace Card
 * @description Card Footer
 */

import { CardFooter } from "@heroui/card";
import type { FC, ReactNode } from "react";

export type UICardFooterProps = {

    readonly className?: string;

    readonly children?: ReactNode;
};

export const UICardFooter: FC<UICardFooterProps> = (
    props: UICardFooterProps,
) => {

    return (<CardFooter
        className={props.className}
    >
        {props.children}
    </CardFooter>);
};
