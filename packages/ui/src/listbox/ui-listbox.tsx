/**
 * <AUTHOR>
 * @package UI
 * @namespace Listbox
 * @description Listbox
 */

import { Listbox, ListboxItem } from "@heroui/listbox";
import clsx from "clsx";
import React from "react";
import { UIListboxItem, UIListboxVariant } from "./types";

export type UIListboxProps = {

    readonly ariaLabel: string;

    readonly className?: string;

    readonly variant?: UIListboxVariant;

    readonly items: UIListboxItem[];
};

export const UIListbox: React.FC<UIListboxProps> = (props: UIListboxProps) => {

    const disabledKeys: string[] = React.useMemo(() => {

        return props.items
            .filter((item: UIListboxItem) => item.isDisabled)
            .map((item: UIListboxItem) => item.key);
    }, [props.items]);

    return (
        <Listbox
            aria-label={props.ariaLabel}
            className={props.className}
            variant={props.variant}
            disabledKeys={disabledKeys}
        >
            {props.items.map((
                item: UIListboxItem,
            ) => {

                return (<ListboxItem
                    key={item.key}
                    className={item.className}
                    classNames={{
                        title: item.titleClassName,
                        description: clsx(
                            "text-wrap",
                            item.descriptionClassName,
                        ),
                    }}
                    href={item.href}
                    color={item.color}
                    startContent={item.startContent}
                    endContent={item.endContent}
                    description={item.descriptionContent}
                >
                    {item.titleContent}
                </ListboxItem>);
            })}
        </Listbox>
    );
};
