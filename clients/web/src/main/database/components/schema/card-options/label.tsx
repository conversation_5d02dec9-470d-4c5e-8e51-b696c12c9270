/**
 * <AUTHOR>
 * @namespace Database_Components_Schema_CardOptions
 * @description Label
 */

import { useDatabaseFormat } from "@/main/database/internationalization/hook";
import { DATABASE_PROFILE } from "@/main/database/internationalization/profile";
import { UIButton, UICheckbox } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchema, ImbricateDatabaseSchemaProperty, ImbricateDatabaseSchemaPropertyOptionsLabel, ImbricateDatabaseSchemaPropertyOptionsLabelOption } from "@imbricate/core";
import { UUIDVersion1 } from "@sudoo/uuid";
import { FC } from "react";
import { FaPlus } from "react-icons/fa";
import { DatabaseSchemaPropertyCardOptionsLabelColorSelect } from "./label-color-select";

export type DatabaseSchemaPropertyCardOptionsLabelProps = {

    readonly property: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE.LABEL>;
    readonly schema: ImbricateDatabaseSchema;
    readonly setSchema: (schema: ImbricateDatabaseSchema) => void;
};

export const DatabaseSchemaPropertyCardOptionsLabel: FC<DatabaseSchemaPropertyCardOptionsLabelProps> = (
    props: DatabaseSchemaPropertyCardOptionsLabelProps,
) => {

    const databaseFormat = useDatabaseFormat();

    const currentSchema = props.schema.properties.find((each) => {
        return each.propertyIdentifier === props.property.propertyIdentifier;
    });

    if (!currentSchema) {
        return null;
    }

    const options: ImbricateDatabaseSchemaPropertyOptionsLabel =
        currentSchema.propertyOptions as ImbricateDatabaseSchemaPropertyOptionsLabel;

    const allowMultiple: boolean = options.allowMultiple ?? false;
    const labelOptions: ImbricateDatabaseSchemaPropertyOptionsLabelOption[] = options.labelOptions ?? [];

    return (<div
        className="w-full flex flex-col gap-2"
    >
        <div
            className="flex justify-center items-center"
        >
            <div
                className="flex-1"
            >
                <UICheckbox
                    className="flex-1"
                    defaultSelected={allowMultiple}
                    onValueChange={(newSelected: boolean) => {

                        props.setSchema({
                            ...props.schema,
                            properties: props.schema.properties.map((each) => {
                                if (each.propertyIdentifier === props.property.propertyIdentifier) {
                                    return {
                                        ...each,
                                        propertyOptions: {
                                            ...options,
                                            allowMultiple: newSelected,
                                        },
                                    };
                                }
                                return each;
                            }),
                        });
                    }}
                >
                    {databaseFormat.get(DATABASE_PROFILE.ALLOW_MULTIPLE)}
                </UICheckbox>
            </div>
            <div>
                <UIButton
                    color="primary"
                    variant="flat"
                    size="lg"
                    radius="sm"
                    isIconOnly
                    onPress={() => {

                        const newOptions: ImbricateDatabaseSchemaPropertyOptionsLabelOption[] = [
                            ...labelOptions,
                            {
                                labelIdentifier: UUIDVersion1.generateString(),
                                labelName: "label",
                                labelColor: "blue",
                            },
                        ];

                        props.setSchema({
                            ...props.schema,
                            properties: props.schema.properties.map((each) => {
                                if (each.propertyIdentifier === props.property.propertyIdentifier) {
                                    return {
                                        ...each,
                                        propertyOptions: {
                                            ...options,
                                            labelOptions: newOptions,
                                        },
                                    };
                                }
                                return each;
                            }),
                        });
                    }}
                >
                    <FaPlus />
                </UIButton>
            </div>
        </div>
        {labelOptions.length > 0 && <div
            className="w-full flex flex-col gap-1"
        >
            {labelOptions.map((each) => {
                return (<DatabaseSchemaPropertyCardOptionsLabelColorSelect
                    key={each.labelIdentifier}
                    property={props.property}
                    schema={props.schema}
                    setSchema={props.setSchema}
                    labelIdentifier={each.labelIdentifier}
                    currentSchema={currentSchema as ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE.LABEL>}
                />);
            })}
        </div>}
    </div>);
};
