/**
 * <AUTHOR>
 * @namespace Data_Hooks
 * @description Use Recent Actions
 */

import { RecentActionItem, RecentActions } from "@imbricate-hummingbird/interceptor-core";
import { WORKER_WEB_WORKER } from "@imbricate-hummingbird/web-worker-core";
import { useCallback, useEffect } from "react";
import { useForceUpdate } from "../../common/hooks/use-version";

export type UseRecentActionsResponse = {

    readonly actions: RecentActionItem<WORKER_WEB_WORKER>[];
};

export const useRecentActions = (
    generic?: boolean,
): UseRecentActionsResponse => {

    const forceUpdate = useForceUpdate();

    const recentActions: RecentActions<WORKER_WEB_WORKER> =
        RecentActions.getInstance<WORKER_WEB_WORKER>();

    const updateFunction = useCallback(() => {
        forceUpdate();
    }, [forceUpdate]);

    useEffect(() => {

        if (generic) {
            recentActions.addGenericListener(updateFunction);
        } else {
            recentActions.addFailedListener(updateFunction);
        }

        return () => {
            if (generic) {
                recentActions.removeGenericListener(updateFunction);
            } else {
                recentActions.removeFailedListener(updateFunction);
            }
        };
    }, [generic]);

    return {
        actions: recentActions.getRecentActions(),
    };
};
