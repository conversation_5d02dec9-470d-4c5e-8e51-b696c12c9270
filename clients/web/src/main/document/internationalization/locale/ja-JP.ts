/**
 * <AUTHOR>
 * @namespace Document_Internationalization_Locale
 * @description Ja-<PERSON>
 */

import { DOCUMENT_PROFILE } from "../profile";

export const jaJPDocumentProfile: Record<DOCUMENT_PROFILE, string> = {

    [DOCUMENT_PROFILE.ARRANGE_DOCUMENT]: "ドキュメントを整理",
    [DOCUMENT_PROFILE.ARRANGE_DOCUMENT_DESCRIPTION]: "現在のドキュメントを他のスロット、データベース、またはオリジンに移動します",
    [DOCUMENT_PROFILE.$1_BINARIES]: "{} バイナリファイル",
    [DOCUMENT_PROFILE.CREATE_MARKDOWN_DOCUMENT]: "Markdown ドキュメントを作成",
    [DOCUMENT_PROFILE.DANGER_ZONE]: "危険ゾーン",
    [DOCUMENT_PROFILE.DELETE_DOCUMENT]: "ドキュメントを削除",
    [DOCUMENT_PROFILE.DELETE_DOCUMENT_DESCRIPTION]: "ドキュメントを完全に削除",
    [DOCUMENT_PROFILE.DATABASE]: "データベース",
    [DOCUMENT_PROFILE.DOCUMENT]: "ドキュメント",
    [DOCUMENT_PROFILE.DOCUMENT_DELETE_SUCCESS]: "ドキュメントを削除",
    [DOCUMENT_PROFILE.DOCUMENT_DELETE_SUCCESS_DESCRIPTION]: "ドキュメントが削除されました",
    [DOCUMENT_PROFILE.DOCUMENT_DETAILS]: "ドキュメントの詳細",
    [DOCUMENT_PROFILE.EDIT_HISTORY]: "編集履歴",
    [DOCUMENT_PROFILE.EDIT_MARKDOWN_DOCUMENT]: "Markdown ドキュメントを編集",
    [DOCUMENT_PROFILE.INVALID_DATE]: "無効な日付",
    [DOCUMENT_PROFILE.LINK_DOCUMENT]: "ドキュメントをリンク",
    [DOCUMENT_PROFILE.NO_BINARY]: "バイナリがありません",
    [DOCUMENT_PROFILE.NO_DATA]: "データがありません",
    [DOCUMENT_PROFILE.NO_REFERENCE]: "参照がありません",
    [DOCUMENT_PROFILE.ONE_BINARY]: "1 つのバイナリファイル",
    [DOCUMENT_PROFILE.ONE_REFERENCE]: "1 つの参照",
    [DOCUMENT_PROFILE.PRIMARY]: "主キー",
    [DOCUMENT_PROFILE.PRIMARY_KEY]: "主キー",
    [DOCUMENT_PROFILE.PRIMARY_PROPERTY_IDENTIFIER]: "主キーのプロパティ識別子",
    [DOCUMENT_PROFILE.PROPERTY_IDENTIFIER]: "プロパティ識別子",
    [DOCUMENT_PROFILE.$1_REFERENCES]: "{} 参照",
    [DOCUMENT_PROFILE.SAVE_CHANGES]: "変更を保存",
    [DOCUMENT_PROFILE.UNABLE_TO_LOAD_DOCUMENT]: "ドキュメントを読み込めませんでした",
    [DOCUMENT_PROFILE.UPLOAD_BINARY]: "バイナリをアップロード",
    [DOCUMENT_PROFILE.VIEW_EDIT_HISTORY]: "編集履歴を表示",
    [DOCUMENT_PROFILE.VIEW_MARKDOWN_DOCUMENT]: "Markdown ドキュメントを表示",
};
