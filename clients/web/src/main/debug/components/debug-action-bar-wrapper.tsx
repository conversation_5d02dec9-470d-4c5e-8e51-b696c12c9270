/**
 * <AUTHOR>
 * @namespace Debug_Components
 * @description Debug Action Bar Wrapper
 */

import { useDebugMode } from "@imbricate-hummingbird/debug";
import { LazyLoadComponent } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const DebugActionBar = LazyLoadComponent(
    () => import("./debug-action-bar"),
    "Debug Action Bar",
);

export const DebugActionBarWrapper: FC = () => {

    const { isDebugMode } = useDebugMode();

    if (!isDebugMode) {
        return null;
    }

    return (<React.Suspense>
        <DebugActionBar />
    </React.Suspense>);
};
