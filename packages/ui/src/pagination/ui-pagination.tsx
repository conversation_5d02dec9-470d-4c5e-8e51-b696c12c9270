/**
 * <AUTHOR>
 * @package UI
 * @namespace Pagination
 * @description Pagination
 */

import { Pagination } from "@heroui/pagination";
import React from "react";
import { UIColor } from "../common/types";
import { UIPaginationVariant } from "./types";

export type UIPaginationProps = {

    readonly className?: string;
    readonly itemClassName?: string;

    readonly color?: UIColor;
    readonly variant?: UIPaginationVariant;

    readonly current: number;
    readonly total: number;
    readonly onChange: (newPage: number) => void;
};

export const UIPagination: React.FC<UIPaginationProps> = (
    props: UIPaginationProps,
) => {

    return (<Pagination
        className={props.className}
        classNames={{
            item: props.itemClassName,
        }}
        page={props.current}
        total={props.total}
        color={props.color}
        variant={props.variant}
        onChange={props.onChange}
    />);
};
