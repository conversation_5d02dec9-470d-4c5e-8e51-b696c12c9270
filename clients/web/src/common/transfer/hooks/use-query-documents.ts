/**
 * <AUTHOR>
 * @namespace Common_Transfer_Hooks
 * @description Use Query Documents
 */
// ONLY RUN IN RENDERER

import { $ERROR, $LOADING, HookSymbol } from "@imbricate-hummingbird/global-symbol";
import { ActionCentral, ActionDescription } from "@imbricate-hummingbird/interceptor-core";
import { OriginWorkerDataCentral } from "@imbricate-hummingbird/origin-central-web-worker";
import { ReactDependency } from "@imbricate-hummingbird/react-common";
import { TransferDocument } from "@imbricate-hummingbird/transfer-core";
import { ImbricateDatabaseQuery } from "@imbricate/core";
import { useEffect, useState } from "react";

export type TransferQueryDocumentsResponse = {

    readonly documents: TransferDocument[];
    readonly count: number;
};

export const useQueryDocuments = (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
    query: ImbricateDatabaseQuery,
    actionDescription: ActionDescription,
    ...dependencies: ReactDependency[]
): TransferQueryDocumentsResponse | HookSymbol => {

    const [errored, setErrored] = useState<boolean>(false);

    const [documents, setDocuments] = useState<TransferDocument[] | null>(null);
    const [count, setCount] = useState<number>(0);

    useEffect(() => {

        const execute = async () => {

            setDocuments(null);

            const documentsResult = await ActionCentral.getInstance().executeAction(
                async (
                    actionIdentifier: string,
                    recordIdentifier: string,
                ) => {

                    return await OriginWorkerDataCentral.getInstance().queryDocuments(
                        actionIdentifier,
                        recordIdentifier,
                        originUniqueIdentifier,
                        databaseUniqueIdentifier,
                        query,
                    );
                },
                actionDescription,
            );

            if (typeof documentsResult === "symbol") {

                setErrored(true);
                return;
            }

            setDocuments(documentsResult.documents);
            setCount(documentsResult.count);
        };

        execute();
    }, [
        originUniqueIdentifier,
        databaseUniqueIdentifier,
        JSON.stringify(query),
        ...dependencies,
    ]);

    if (errored) {
        return $ERROR;
    }

    if (documents === null) {
        return $LOADING;
    }

    const result: TransferQueryDocumentsResponse = {
        documents,
        count,
    };
    return result;
};
