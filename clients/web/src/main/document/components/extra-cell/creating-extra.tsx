/**
 * <AUTHOR>
 * @namespace Document_Components_ExtraCell
 * @description Creating Extra
 */

import { UIButton } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { FaRegSave } from "react-icons/fa";
import { IoClose } from "react-icons/io5";
import { DocumentEditingController } from "../../controller/editing-controller";

export type DocumentsTableCreatingExtraCellProps = {

    readonly creatingKey: string;
    readonly editingController: DocumentEditingController;
};

export const DocumentsTableCreatingExtraCell: FC<DocumentsTableCreatingExtraCellProps> = (
    props: DocumentsTableCreatingExtraCellProps,
) => {

    const items: React.ReactElement[] = [];

    items.push(<div
        key="editing-status"
        className="flex gap-1"
    >
        <UIButton
            isIconOnly
            color="success"
            variant="solid"
            size="sm"
            onPress={() => {
                props.editingController.saveCreatingDocument(props.creatingKey);
            }}
        >
            <FaRegSave />
        </UIButton>
        <UIButton
            isIconOnly
            color="danger"
            variant="solid"
            size="sm"
            onPress={() => {
                props.editingController.cancelCreatingDocument(props.creatingKey);
            }}
        >
            <IoClose />
        </UIButton>
    </div>);

    return (<div
        className="flex gap-1"
    >
        {items}
    </div>);
};
