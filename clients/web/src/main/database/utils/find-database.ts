/**
 * <AUTHOR>
 * @namespace Database_Utils
 * @description Find Database
 */

import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { ImbricateDatabaseManagerQueryDatabasesOutcome } from "@imbricate/core";
import { IMBRICATE_ORIGIN_FEATURE, checkImbricateOriginFeatureSupported } from "@imbricate/core/origin/feature";
import { OldDataCentral } from "../../data/old-data-central";
import { createFindDatabasesAction } from "../actions/find-databases";
import { createFindOriginForDatabaseAction } from "../actions/find-origin-for-database";
import { ImbricateDatabasesObject } from "../hooks/use-databases";

export const findDatabase = async (
    originUniqueIdentifier: string,
): Promise<ImbricateDatabasesObject[]> => {

    const response: ImbricateDatabasesObject[] = [];

    const origin: ImbricateOriginObject | null = await ActionCentral.getInstance().executeAction(
        async (
            actionIdentifier: string,
            recordIdentifier: string,
        ) => {
            return OldDataCentral.getInstance().getOrigin(
                originUniqueIdentifier,
                actionIdentifier,
                recordIdentifier,
            );
        },
        createFindOriginForDatabaseAction(
            originUniqueIdentifier,
            {
                executer: import.meta.url,
            },
        ),
    );

    if (!origin) {
        return [];
    }

    const isOriginSupported: boolean = checkImbricateOriginFeatureSupported(
        origin.origin.supportedFeatures,
        IMBRICATE_ORIGIN_FEATURE.ORIGIN_DATABASE_MANAGER,
    );

    if (!isOriginSupported) {
        return [];
    }

    const databases: ImbricateDatabaseManagerQueryDatabasesOutcome = await ActionCentral.getInstance().executeAction(
        async (
            actionIdentifier: string,
            recordIdentifier: string,
        ) => {

            return await OldDataCentral.getInstance().getDatabases(
                originUniqueIdentifier,
                actionIdentifier,
                recordIdentifier,
            );
        },
        createFindDatabasesAction(
            originUniqueIdentifier,
            {
                executer: import.meta.url,
            },
        ),
    );

    if (typeof databases === "symbol") {
        return [];
    }

    for (const database of databases.databases) {

        response.push({
            originUniqueIdentifier,
            originName: origin.originName,
            database,
        });
    }

    return response;
};
