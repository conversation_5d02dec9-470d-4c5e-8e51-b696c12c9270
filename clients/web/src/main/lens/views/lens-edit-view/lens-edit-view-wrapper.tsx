/**
 * <AUTHOR>
 * @namespace Lens
 * @description Lens Edit View Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const LensEditViewFetcher = LazyLoadComponent(
    () => import("./lens-edit-view-fetcher"),
    "Lens Edit View Fetcher",
);

export const LensEditViewWrapper: FC = () => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Lens Edit View Fetcher"
            fullHeight
        />}
    >
        <LensEditViewFetcher />
    </React.Suspense>);
};
