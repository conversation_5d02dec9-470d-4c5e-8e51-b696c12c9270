/**
 * <AUTHOR>
 * @namespace Origin
 * @description Use Wide Origin
 */

import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { useEffect, useState } from "react";
import { OldDataCentral } from "../../data/old-data-central";
import { createFindOriginAction } from "../actions/find-origin";

export const useWideOrigin = (
    databaseUniqueIdentifier: string,
): ImbricateOriginObject | null => {

    const [origin, setOrigin] = useState<ImbricateOriginObject | null>(null);

    useEffect(() => {

        const execute = async () => {

            const origin = await ActionCentral.getInstance().executeAction(
                async (
                    actionIdentifier: string,
                    recordIdentifier: string,
                ) => {

                    return await OldDataCentral.getInstance().wideOriginGetOrigin(
                        databaseUniqueIdentifier,
                        actionIdentifier,
                        recordIdentifier,
                    );
                },
                createFindOriginAction(
                    databaseUniqueIdentifier,
                    {
                        executer: import.meta.url,
                    },
                ),
            );

            setOrigin(origin);
        };

        execute();
    }, [databaseUniqueIdentifier]);

    return origin;
};
