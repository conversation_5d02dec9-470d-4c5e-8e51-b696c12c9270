/**
 * <AUTHOR>
 * @namespace Common_Database_Label_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooLazyInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { LABEL_COLOR } from "../label-color";

export const commonDatabaseLabelInternationalization: SudooLazyInternationalization<LABEL_COLOR> =
    SudooLazyInternationalization.create<LABEL_COLOR>(
        DEFAULT_LOCALE,
    );

commonDatabaseLabelInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/en-US").then(
        (module) => module.enUSCommonDatabaseLabelProfile,
    ),
);

commonDatabaseLabelInternationalization.mergeLazyProfile(
    IETF_LOCALE.JAPANESE_JAPAN,
    import("./locale/ja-JP").then(
        (module) => module.jaJPCommonDatabaseLabelProfile,
    ),
);

commonDatabaseLabelInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    import("./locale/zh-CN").then(
        (module) => module.zhCNCommonDatabaseLabelProfile,
    ),
);
