/**
 * <AUTHOR>
 * @namespace Main_Debug_Components_CachedEntries
 * @description Cached Entries Quick Access
 */

import { formatTime } from "@/main/common/util/format-time";
import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { CentralGetCachedEntriesResponse } from "@imbricate-hummingbird/origin-central";
import { OriginWorkerDataCentral } from "@imbricate-hummingbird/origin-central-web-worker";
import { StyledCard } from "@imbricate-hummingbird/react-components";
import { UIButton, UICardBody, UIDivider } from "@imbricate-hummingbird/ui";
import React from "react";
import { FaSync } from "react-icons/fa";

export const CachedEntriesQuickAccess: React.FC = () => {

    const [cachedEntries, setCachedEntries] =
        React.useState<CentralGetCachedEntriesResponse | null>(null);

    const refresh = async () => {

        const result = await ActionCentral.getInstance().executeAction(
            async (
                actionIdentifier: string,
                recordIdentifier: string,
            ) => {

                return await OriginWorkerDataCentral.getInstance().getCachedEntries(
                    actionIdentifier,
                    recordIdentifier,
                );
            },
            {
                actionName: "Get Cached Entries",
                actionDescription: "Get all cached entries",
                executerMetadata: {
                    executer: import.meta.url,
                },
            },
        );

        if (typeof result === "symbol") {
            return;
        }

        setCachedEntries(result);
    };

    React.useEffect(() => {
        refresh();
    }, []);

    if (cachedEntries === null) {
        return null;
    }

    if (typeof cachedEntries === "symbol") {
        return null;
    }

    return (<StyledCard>
        <UICardBody
            className="flex flex-col gap-1 p-1"
        >
            <UIButton
                size="sm"
                radius="sm"
                color="secondary"
                variant="flat"
                startContent={<FaSync />}
                onPress={() => {
                    refresh();
                }}
            >
                Refresh
            </UIButton>
        </UICardBody>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-1 p-1"
        >
            {cachedEntries.cachedEntries.map((cachedEntry) => (
                <div
                    key={cachedEntry.cacheKey}
                    className="text-tiny font-mono text-foreground-500"
                >
                    <div
                        className="max-w-lg overflow-hidden text-ellipsis whitespace-nowrap"
                    >
                        {cachedEntry.cacheKey}
                    </div>
                    <div>
                        Expired At: {formatTime(cachedEntry.expiredAt)}
                    </div>
                </div>
            ))}
        </UICardBody>
    </StyledCard>);
};
