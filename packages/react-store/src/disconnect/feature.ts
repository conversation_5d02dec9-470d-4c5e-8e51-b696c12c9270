/**
 * <AUTHOR>
 * @namespace Store_Feature
 * @description Disconnect
 */

import { createSlice } from "@reduxjs/toolkit";
import { useSelector } from "react-redux";
import { DisconnectConfig, DisconnectedItem } from "./types/disconnect";

export type DisconnectSliceState = {

    readonly disconnectConfig: DisconnectConfig;
};

export type DisconnectSliceReducers = {

    readonly markAsDisconnected: (
        state: DisconnectSliceState,
        action: {
            payload: DisconnectedItem,
        },
    ) => DisconnectSliceState;
    readonly markAsResolved: (
        state: DisconnectSliceState,
        action: {
            payload: string,
        },
    ) => DisconnectSliceState;
};

export const DisconnectSlice = createSlice<
    DisconnectSliceState,
    DisconnectSliceReducers,
    "disconnect",
    any
>({
    name: "disconnect",
    initialState: {
        disconnectConfig: {
            items: [],
        },
    },
    reducers: {
        markAsDisconnected: (state: DisconnectSliceState, action: {
            payload: DisconnectedItem,
        }) => {

            const alreadyMarked = state.disconnectConfig.items.some((
                item: DisconnectedItem,
            ) => {
                return item.originUniqueIdentifier === action.payload.originUniqueIdentifier;
            });

            if (alreadyMarked) {
                return state;
            }

            return {
                ...state,
                disconnectConfig: {
                    ...state.disconnectConfig,
                    items: [
                        ...state.disconnectConfig.items,
                        action.payload,
                    ],
                },
            };
        },
        markAsResolved: (state: DisconnectSliceState, action: {
            payload: string,
        }) => {

            return {
                ...state,
                disconnectConfig: {
                    ...state.disconnectConfig,
                    items: state.disconnectConfig.items.filter((item: DisconnectedItem) => {
                        return item.originUniqueIdentifier !== action.payload;
                    }),
                },
            };
        },
    },
});

export const useDisconnectSlice = (): DisconnectSliceState => {

    const disconnectState: DisconnectSliceState = useSelector((state: any) => {
        return state.disconnect;
    });

    return disconnectState;
};
