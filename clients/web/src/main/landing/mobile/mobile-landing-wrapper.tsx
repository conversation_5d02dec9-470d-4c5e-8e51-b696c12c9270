/**
 * <AUTHOR>
 * @namespace Landing_Mobile
 * @description Mobile Landing Wrapper
 */

import { useIsNotSkippedMobileWarning } from "@/main/common/hooks/use-is-mobile";
import { getRouteRootView } from "@imbricate-hummingbird/navigation-core";
import { FullLoadingWrapper, LazyLoadComponent } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import { Navigate } from "react-router-dom";

const MobileLanding = LazyLoadComponent(
    () => import("./mobile-landing"),
    "Mobile Landing",
);

export const MobileLandingWrapper: FC = () => {

    const isSkipped = useIsNotSkippedMobileWarning();

    if (!isSkipped) {

        return (<Navigate
            to={getRouteRootView()}
            replace={true}
        />);
    }

    return (<React.Suspense
        fallback={<FullLoadingWrapper
            debugDescription="Mobile Landing"
        />}
    >
        <MobileLanding />
    </React.Suspense>);
};
