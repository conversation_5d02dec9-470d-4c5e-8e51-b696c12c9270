/**
 * <AUTHOR>
 * @namespace Internationalization_Internationalization_Locale
 * @description Language
 */

import { IETF_LOCALE } from "@sudoo/locale";

// spell-checker: disable
export const languageInternationalizationProfile: Record<IETF_LOCALE, string> = {

    [IETF_LOCALE.CHINESE_SIMPLIFIED]: "简体中文",
    [IETF_LOCALE.CHINESE_TRADITIONAL]: "正體字中文",
    [IETF_LOCALE.ENGLISH_CANADA]: "English (Canada)",
    [IETF_LOCALE.ENGLISH_UNITED_KINGDOM]: "English (UK)",
    [IETF_LOCALE.ENGLISH_UNITED_STATES]: "English (US)",
    [IETF_LOCALE.FRENCH_CANADA]: "Français (Canada)",
    [IETF_LOCALE.FRENCH_FRANCE]: "Français (France)",
    [IETF_LOCALE.JAPANESE_JAPAN]: "日本語",
    [IETF_LOCALE.KOREAN_KOREA]: "한국어",
    [IETF_LOCALE.MAORI_NEW_ZEALAND]: "<PERSON><PERSON>",
    [IETF_LOCALE.RUSSIAN_RUSSIA]: "Русский",
    [IETF_LOCALE.SPANISH_MEXICO]: "Español (México)",
    [IETF_LOCALE.SPANISH_SPAIN]: "Español (España)",
};
// spell-checker: enable
