/**
 * <AUTHOR>
 * @namespace Common_Util
 * @description Global Data
 */

const GLOBAL_DATA_KEY: string = "IMBRICATE_HUMMINGBIRD_GLOBAL_DATA";

const anyGlobalThis: any = globalThis;

export const addGlobalData = (key: string, value: any): void => {

    anyGlobalThis[GLOBAL_DATA_KEY] = {
        ...anyGlobalThis[GLOBAL_DATA_KEY],
        [key]: value,
    };
};

export const getGlobalData = (key: string): any => {

    if (typeof anyGlobalThis[GLOBAL_DATA_KEY] === "undefined") {
        return void 0;
    }

    return anyGlobalThis[GLOBAL_DATA_KEY][key];
};
