/**
 * <AUTHOR>
 * @namespace Database_Components_Header
 * @description Items Per Page Switch
 */

import { UIDropdown, createUIButton } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { TbPageBreak } from "react-icons/tb";
import { useDatabaseFormat } from "../../internationalization/hook";
import { DATABASE_PROFILE } from "../../internationalization/profile";

export type DatabaseHeaderItemItemsPerPageSwitchProps = {

    readonly itemsPerPage: number;
    readonly onItemsPerPageChange: (
        itemsPerPage: number,
    ) => void;
};

export const DatabaseHeaderItemItemsPerPageSwitch: FC<DatabaseHeaderItemItemsPerPageSwitchProps> = (
    props: DatabaseHeaderItemItemsPerPageSwitchProps,
) => {

    const databaseFormat = useDatabaseFormat();

    return (<UIDropdown
        ariaLabel="items-per-page-switch"
        variant="faded"
        selectionMode="single"
        trigger={createUIButton({
            className: "min-w-15 px-3",
            variant: "light",
            color: "primary",
            startContent: <TbPageBreak />,
            children: props.itemsPerPage,
        })}
        categories={[
            {
                categoryKey: "items-per-page",
                title: databaseFormat.get(DATABASE_PROFILE.ITEM_PER_PAGE_FOR_THIS_DATABASE),
                items: [5, 10, 25, 50].map((item) => {
                    return {
                        itemKey: String(item),
                        textValue: String(item),
                        className: item === props.itemsPerPage ? "text-primary" : "",
                        color: item === props.itemsPerPage ? "primary" : "default",
                        isSelected: item === props.itemsPerPage,
                        content: databaseFormat.get(DATABASE_PROFILE.ITEM_$1_PER_PAGE, item),
                        onPress: () => {
                            props.onItemsPerPageChange(item);
                        },
                    };
                }),
            },
        ]}
    />);
};
