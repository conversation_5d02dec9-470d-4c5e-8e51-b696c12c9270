/**
 * <AUTHOR>
 * @namespace Property_Utils_FindDraft
 * @description Find Draft
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { IMBRICATE_PROPERTY_TYPE, ImbricatePropertyKey, ImbricatePropertyVariant, getImbricateDefaultValueOfProperty } from "@imbricate/core";
import { UseEditableDraftsResult } from "../hooks/use-editable-drafts";

export const findDraft = (
    drafts: UseEditableDraftsResult,
    propertyKey: ImbricatePropertyKey,
    propertyType: IMBRICATE_PROPERTY_TYPE,
    propertyVariant: ImbricatePropertyVariant,
): TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> => {

    if (typeof drafts === "symbol") {

        return {
            key: propertyKey,
            type: propertyType,
            value: getImbricateDefaultValueOfProperty(propertyType),
            variant: propertyVariant,
        };
    }

    const result = drafts.mergedDrafts.find((draft) => {
        return draft.key === propertyKey;
    });

    if (!result) {

        return {
            key: propertyKey,
            type: propertyType,
            value: getImbricateDefaultValueOfProperty(propertyType),
            variant: propertyVariant,
        };
    }

    return result;
};
