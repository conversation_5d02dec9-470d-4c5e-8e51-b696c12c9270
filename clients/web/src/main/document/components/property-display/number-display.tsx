/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyDisplay
 * @description Number Display
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";

export type DocumentNumberDisplayProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.NUMBER>;
};

export const DocumentNumberDisplay: FC<DocumentNumberDisplayProps> = (
    props: DocumentNumberDisplayProps,
) => {

    return (<div>
        {String(props.property.value)}
    </div>);
};
