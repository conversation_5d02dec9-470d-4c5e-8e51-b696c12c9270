/**
 * <AUTHOR>
 * @namespace Database_Hooks
 * @description Use Raw Database View Magic Button
 */

import { useMagicButtonInitialItems } from "@/common/components/magic-button/hooks/use-initial-items";
import { UseMagicButtonResult, useMagicButton } from "@/common/components/magic-button/hooks/use-magic-button";
import { DocumentEditingController } from "@/main/document/controller/editing-controller";
import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { useNavigateDatabaseSchemaView } from "@imbricate-hummingbird/react-navigation";
import { FaEdit } from "react-icons/fa";
import { MdEditDocument } from "react-icons/md";
import { useDatabaseFormat } from "../internationalization/hook";
import { DATABASE_PROFILE } from "../internationalization/profile";

export const useRawDatabaseViewMagicButton = (
    databaseUniqueIdentifier: string,
    editingControllerRef: React.RefObject<DocumentEditingController | undefined>,
    dependencies: React.DependencyList = [],
): UseMagicButtonResult => {

    const format = useDatabaseFormat();

    const navigateToDatabaseSchema = useNavigateDatabaseSchemaView();

    const magicButtonResult = useMagicButton([
        {
            categoryKey: "document",
            categoryTitle: format.get(DATABASE_PROFILE.DOCUMENT),
            categoryOrder: 1,
        },
        {
            categoryKey: "database",
            categoryTitle: format.get(DATABASE_PROFILE.DATABASE),
            categoryOrder: 2,
        },
    ]);

    useMagicButtonInitialItems(
        magicButtonResult,
        !isFormatLoading(format),
        [
            {
                categoryKey: "document",
                itemKey: "create-document",
                icon: MdEditDocument,
                title: format.get(DATABASE_PROFILE.CREATE_DOCUMENT),
                description: format.get(DATABASE_PROFILE.CREATE_DOCUMENT_DESCRIPTION),
                quickAccess: true,
                defaultQuickAccess: true,
                color: "success",
                onPress: () => {

                    if (typeof editingControllerRef.current === "undefined") {
                        return;
                    }

                    editingControllerRef.current.startCreatingDocument();
                },
            },
            {
                categoryKey: "database",
                itemKey: "edit-database-schema",
                icon: FaEdit,
                title: format.get(DATABASE_PROFILE.EDIT_DATABASE_SCHEMA),
                description: format.get(DATABASE_PROFILE.EDIT_DATABASE_SCHEMA_DESCRIPTION),
                onPress: () => {

                    navigateToDatabaseSchema(
                        databaseUniqueIdentifier,
                        {
                            replace: true,
                        },
                    );
                },
            },
        ],
        dependencies,
    );

    return magicButtonResult;
};
