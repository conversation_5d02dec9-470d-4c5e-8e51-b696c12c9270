/**
 * <AUTHOR>
 * @package UI
 * @namespace DatePicker
 * @description Parse
 */

import { DateValue, getLocalTimeZone, parseDate } from "@internationalized/date";

export const uiDatePickerStringDateToUIDate = (dateString: string): DateValue | null => {

    if (!dateString) {
        return null;
    }

    const date = new Date(dateString);

    if (isNaN(date.getTime())) {
        return null;
    }

    return uiDatePickerDateToUIDate(date);
};

export const uiDatePickerDateToUIDate = (date: Date): DateValue => {

    const paddedYear: string = date.getFullYear().toString().padStart(4, "0");
    const paddedMonth: string = (date.getMonth() + 1).toString().padStart(2, "0");
    const paddedDate: string = date.getDate().toString().padStart(2, "0");

    const ISOString = `${paddedYear}-${paddedMonth}-${paddedDate}`;

    return parseDate(ISOString);
};

export const uiDatePickerUIDateToDate = (date: DateValue): Date => {

    return date.toDate(getLocalTimeZone());
};
