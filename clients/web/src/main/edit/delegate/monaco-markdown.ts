/**
 * <AUTHOR>
 * @namespace Edit_Delegate
 * @description Monaco Markdown
 */

import { IMonacoMarkdownActionDelegate, OpenMarkdownTableEditorInput } from "@imbricate-hummingbird/monaco-markdown";
import { getRouteDocumentView } from "@imbricate-hummingbird/navigation-core";
import { DRAWER_TYPE, UseDrawerActionResponse } from "@imbricate-hummingbird/react-store";
import { markdownTableEditorDynamicOpener } from "../components/markdown-table-editor/markdown-table-editor";

export class MonacoMarkdownActionDelegate implements IMonacoMarkdownActionDelegate {

    public static create(
        drawerActions: UseDrawerActionResponse,
    ): MonacoMarkdownActionDelegate {

        return new MonacoMarkdownActionDelegate(
            drawerActions,
        );
    }

    private readonly _drawerActions: UseDrawerActionResponse;

    public constructor(
        drawerActions: UseDrawerActionResponse,
    ) {

        this._drawerActions = drawerActions;
    }

    public openDocument(
        databaseUniqueIdentifier: string,
        documentUniqueIdentifier: string,
    ): void {

        this._drawerActions.openDrawer({
            type: DRAWER_TYPE.DOCUMENT_VIEW,
            title: "Document View",
            fullScreenLink: getRouteDocumentView(
                databaseUniqueIdentifier,
                documentUniqueIdentifier,
            ),
            payload: {
                databaseUniqueIdentifier,
                documentUniqueIdentifier,
            },
        });
    }

    public openMarkdownTableEdit(
        input: OpenMarkdownTableEditorInput,
    ): void {

        markdownTableEditorDynamicOpener.open(input);
    }
}
