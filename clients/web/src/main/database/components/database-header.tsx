/**
 * <AUTHOR>
 * @namespace Database_Components
 * @description Database Header
 */

import { HeaderGroup } from "@/common/components/header-group/header-group";
import { HEADER_GROUP_GROUP } from "@/common/components/header-group/types";
import { HeaderTitle } from "@/common/components/header-title/header-title";
import { HEADER_TITLE_SECONDARY_ITEM_TYPE } from "@/common/components/header-title/types";
import { UseMagicButtonResult } from "@/common/components/magic-button/hooks/use-magic-button";
import { MagicButton } from "@/common/components/magic-button/magic-button";
import { getRouteDatabaseSchemaView } from "@imbricate-hummingbird/navigation-core";
import { UINavbar } from "@imbricate-hummingbird/ui";
import { IImbricateDatabase } from "@imbricate/core";
import { FC } from "react";
import { FaEdit } from "react-icons/fa";
import { useDatabaseFormat } from "../internationalization/hook";
import { DATABASE_PROFILE } from "../internationalization/profile";
import { DatabaseHeaderItemSwitcher } from "./database-header-item-switcher";

export type DatabaseHeaderProps = {

    readonly originUniqueIdentifier: string;
    readonly database: IImbricateDatabase;

    readonly itemPerPage: number;
    readonly onItemPerPageChange: (itemPerPage: number) => void;

    readonly displayedProperties: string[];
    readonly onDisplayedPropertiesChange: (displayedProperties: string[]) => void;

    readonly magicButtonResult: UseMagicButtonResult;
};

export const DatabaseHeader: FC<DatabaseHeaderProps> = (props: DatabaseHeaderProps) => {

    const format = useDatabaseFormat();

    return (<UINavbar
        isFullWidth
        isBordered
        startContent={<HeaderGroup
            group={HEADER_GROUP_GROUP.DATABASE}
            payload={{
                originUniqueIdentifier: props.originUniqueIdentifier,
                databaseUniqueIdentifier: props.database.uniqueIdentifier,
            }}
        />}
        coreContent={<HeaderTitle
            title={props.database.databaseName}
            secondary={{
                type: HEADER_TITLE_SECONDARY_ITEM_TYPE.LINK,
                link: getRouteDatabaseSchemaView(props.database.uniqueIdentifier),
                title: format.get(DATABASE_PROFILE.DATABASE_SCHEMA),
                icon: FaEdit,
            }}
            formatDependencies={[format]}
        />}
        endContent={<div
            className="flex items-center gap-2"
        >
            <DatabaseHeaderItemSwitcher
                itemsPerPage={props.itemPerPage}
                onItemsPerPageChange={props.onItemPerPageChange}
                databaseSchema={props.database.schema}
                displayedProperties={props.displayedProperties}
                onDisplayedPropertiesChange={props.onDisplayedPropertiesChange}
            />
            <MagicButton
                magicButtonResult={props.magicButtonResult}
            />
        </div>}
    />);
};
