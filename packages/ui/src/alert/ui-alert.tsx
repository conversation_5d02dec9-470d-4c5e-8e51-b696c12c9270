/**
 * <AUTHOR>
 * @package UI
 * @namespace Alert
 * @description Alert
 */

import { Alert } from "@heroui/alert";
import React from "react";
import { UIColor } from "../common/types";
import { UIAlertVariant } from "./types";

export type UIAlertProps = {

    readonly className?: string;
    readonly titleClassName?: string;
    readonly descriptionClassName?: string;

    readonly isHideIcon?: boolean;

    readonly color?: UIColor;
    readonly variant?: UIAlertVariant;

    readonly children?: React.ReactNode;

    readonly title?: React.ReactNode;
    readonly description?: React.ReactNode;
};

export const UIAlert: React.FC<UIAlertProps> = (
    props: UIAlertProps,
) => {

    return (<Alert
        className={props.className}
        classNames={{
            title: props.titleClassName,
            description: props.descriptionClassName,
        }}

        hideIcon={props.isHideIcon}

        color={props.color}
        variant={props.variant}

        title={props.title}
        description={props.description}
    >
        {props.children}
    </Alert>);
};
