/**
 * <AUTHOR>
 * @namespace External
 * @description Setup Sentry
 */
// ONLY RUN IN RENDERER

import { ApplicationVersion } from "@/common/types/app-version";
import { rootLogger } from "@/main/log/logger";
import * as Sentry from "@sentry/react";

const logger = rootLogger.fork({
    scopes: [
        "External",
        "SetupSentry",
    ],
});

const SENTRY_DSN = "https://<EMAIL>/4508977939349504";

export const setupSentry = () => {

    const isDevelopment = process.env.NODE_ENV === "development";

    if (isDevelopment) {

        logger.warning("Sentry is disabled in development mode");
        return;
    }

    Sentry.init({

        dsn: SENTRY_DSN,
        release: ApplicationVersion,
        integrations: [
            Sentry.browserTracingIntegration(),
            Sentry.replayIntegration(),
        ],
        tracesSampleRate: 1.0,
        tracePropagationTargets: [
            "https://imbricate.app/",
        ],
        replaysSessionSampleRate: 0.1,
        replaysOnErrorSampleRate: 1.0,
    });
};
