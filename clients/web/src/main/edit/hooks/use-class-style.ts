/**
 * <AUTHOR>
 * @namespace Main_Edit_Hooks
 * @description Use Class Style
 */

import { useDarkMode } from "@imbricate-hummingbird/theme-core";
import { useEffect } from "react";

export const useClassStyle = (
    stylesheetLight: string,
    stylesheetDark: string,
) => {

    const isDarkMode = useDarkMode();

    useEffect(() => {

        const style = document.createElement("style");
        style.innerHTML = isDarkMode ? stylesheetDark : stylesheetLight;
        document.head.appendChild(style);

        return () => {
            document.head.removeChild(style);
        };
    }, [isDarkMode, stylesheetLight, stylesheetDark]);
};
