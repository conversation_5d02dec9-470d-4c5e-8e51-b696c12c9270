/**
 * <AUTHOR>
 * @package React Components
 * @namespace ErrorMessage_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { enUSReactComponentsErrorMessageProfile } from "./locale/en-US";
import { jaJPReactComponentsErrorMessageProfile } from "./locale/ja-JP";
import { zhCNReactComponentsErrorMessageProfile } from "./locale/zh-CN";
import { REACT_COMPONENTS_ERROR_MESSAGE_PROFILE } from "./profile";

export const reactComponentsErrorMessageInternationalization: SudooInternationalization<REACT_COMPONENTS_ERROR_MESSAGE_PROFILE> =
    SudooInternationalization.create<REACT_COMPONENTS_ERROR_MESSAGE_PROFILE>(
        DEFAULT_LOCALE,
    );

reactComponentsErrorMessageInternationalization.merge(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    enUSReactComponentsErrorMessageProfile,
);

reactComponentsErrorMessageInternationalization.merge(
    IETF_LOCALE.JAPANESE_JAPAN,
    jaJPReactComponentsErrorMessageProfile,
);

reactComponentsErrorMessageInternationalization.merge(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    zhCNReactComponentsErrorMessageProfile,
);
