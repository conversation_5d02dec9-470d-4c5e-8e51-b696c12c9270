/**
 * <AUTHOR>
 * @namespace Origin_Internationalization_Locale
 * @description Zh-CN
 */

import { ORIGIN_PROFILE } from "../profile";

export const zhCNOriginProfile: Record<ORIGIN_PROFILE, string> = {

    [ORIGIN_PROFILE.NOT_SURE_WHICH_TYPE_OF_ORIGIN_TO_USE]: "不确定使用哪种类型的数据源？",
    [ORIGIN_PROFILE.NOT_SURE_WHICH_TYPE_OF_ORIGIN_TO_USE_DESCRIPTION]: "Imbricate 支持多种数据源，允许您将您的数据放置在数据库，文件系统，内存，云端，可以说是任何地方。如果您不确定使用哪种类型的数据源，请参照",
    [ORIGIN_PROFILE.VIEW_DOCUMENT]: "Imbricate 文档",
};
