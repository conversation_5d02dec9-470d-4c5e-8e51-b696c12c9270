/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyCard
 * @description Property Card
 */

import { StyledCard, getPropertyIcon } from "@imbricate-hummingbird/react-components";
import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { U<PERSON>ard<PERSON>ody, UICardHeader, UIChip, UIDivider, UITooltip } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaProperty } from "@imbricate/core";
import React from "react";
import { CommonCopyItem } from "../../../common/components/copy-item";
import { useDocumentFormat } from "../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../internationalization/profile";
import { DocumentPropertyCardContent } from "./property-card-content";

export type DocumentPropertyCardProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;

    readonly schema: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>;
    readonly propertyDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>;
    readonly deleteProperty: () => void;
    readonly updateProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>) => void;
};

export const DocumentPropertyCard: React.FC<DocumentPropertyCardProps> = (
    props: DocumentPropertyCardProps,
) => {

    const documentFormat = useDocumentFormat();

    const isPrimary: boolean = props.schema.isPrimaryKey ?? false;

    return (<StyledCard>
        <UICardHeader
            className="flex gap-2 items-center"
        >
            <UITooltip
                content={<CommonCopyItem
                    startContent="Property Identifier"
                    content={props.schema.propertyIdentifier}
                />}
                delay={1000}
                placement="bottom-start"
            >
                <div className="flex flex-1 gap-1 items-center">
                    <div>
                        {getPropertyIcon(props.schema.propertyType)}
                    </div>
                    <div
                        className="flex-1"
                    >
                        {props.schema.propertyName}
                    </div>
                </div>
            </UITooltip>
            {isPrimary && <UIChip
                color="primary"
                size="sm"
            >
                {documentFormat.get(DOCUMENT_PROFILE.PRIMARY)}
            </UIChip>}
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="items-start"
        >
            <DocumentPropertyCardContent
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                schema={props.schema}
                property={props.propertyDraft}
                deleteProperty={props.deleteProperty}
                updateProperty={props.updateProperty}
            />
        </UICardBody>
    </StyledCard>);
};
