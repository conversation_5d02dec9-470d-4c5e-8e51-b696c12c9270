/**
 * <AUTHOR>
 * @namespace Origin
 * @description Origins View
 */

import { getRouteOriginNewView } from "@imbricate-hummingbird/navigation-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { useTitle } from "@imbricate-hummingbird/react-common";
import { UIButtonLink, UINavbar, UISpacer } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { FaPlus } from "react-icons/fa";
import { TbWorld } from "react-icons/tb";
import { OriginCard } from "./components/origin-card";
import { useOldOrigins } from "./hooks/use-origins";

// LAZY LOAD ONLY
const OriginsView: FC = () => {

    const origins = useOldOrigins();

    useTitle([
        "Origins",
    ], []);

    return (<div
        className="flex flex-col gap-2"
    >
        <UINavbar
            isFullWidth
            isBordered
            startContent={<React.Fragment>
                <TbWorld
                    className="text-2xl"
                />
                <UISpacer />
                <p
                    className="font-mono"
                >
                    Origins
                </p>
            </React.Fragment>}
            coreContent={<p className="font-bold text-xl">
                Origins
            </p>}
            endContent={<UIButtonLink
                href={getRouteOriginNewView()}
                startContent={<FaPlus
                    className="text-small"
                />}
            >
                Add Origin
            </UIButtonLink>}
        />
        <div
            className="pr-2 flex flex-col gap-2"
        >
            {origins.map((origin: ImbricateOriginObject) => {
                return (
                    <OriginCard
                        key={origin.origin.uniqueIdentifier}
                        origin={origin}
                    />
                );
            })}
        </div>
    </div>);
};
export default OriginsView;
