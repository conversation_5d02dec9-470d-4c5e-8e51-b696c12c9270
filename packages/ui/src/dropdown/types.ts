/**
 * <AUTHOR>
 * @package UI
 * @namespace Dropdown
 * @description Types
 */

import type { ReactNode } from "react";
import { UIColor } from "../common/types";

export type UIDropdownItemData = {

    readonly itemKey: string;

    readonly className?: string;
    readonly descriptionClassName?: string;

    readonly isSelected?: boolean;

    readonly color?: UIColor;
    readonly variant?: UIDropdownItemVariant;

    readonly shortcut?: string;

    readonly content: ReactNode;
    readonly description?: ReactNode;
    readonly startContent?: ReactNode;
    readonly endContent?: ReactNode;

    readonly textValue?: string;

    readonly onPress?: () => void;
};

export type UIDropdownItemCategory = {

    readonly categoryKey: string;

    readonly title?: ReactNode;

    readonly items: UIDropdownItemData[];
};

export type UIDropdownSelectionMode =
    | "single"
    | "multiple";

export type UIDropdownBackdrop =
    | "blur"
    | "transparent"
    | "opaque";

export type UIDropdownPlacement =
    | "top"
    | "bottom"
    | "right"
    | "left"
    | "top-start"
    | "top-end"
    | "bottom-start"
    | "bottom-end"
    | "left-start"
    | "left-end"
    | "right-start"
    | "right-end";

export type UIDropdownVariant =
    | "flat"
    | "faded"
    | "solid"
    | "bordered"
    | "light"
    | "shadow";

export type UIDropdownItemVariant =
    | "flat"
    | "faded"
    | "solid"
    | "bordered"
    | "light"
    | "shadow";
