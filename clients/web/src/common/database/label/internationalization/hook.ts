/**
 * <AUTHOR>
 * @namespace Common_Database_Label_Internationalization
 * @description Hook
 */

import { defaultEmptyFormat, useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { useEffect, useState } from "react";
import { LABEL_COLOR } from "../label-color";
import { commonDatabaseLabelInternationalization } from "./intl";

export const useCommonDatabaseLabelFormat = (): SudooFormat<LABEL_COLOR> => {

    const locale = useLocale();

    const [format, setFormat] = useState<SudooFormat<LABEL_COLOR>>(defaultEmptyFormat);

    useEffect(() => {

        const execute = async () => {

            const format = await commonDatabaseLabelInternationalization.format(locale);
            setFormat(format);
        };

        execute();
    }, [locale]);

    return format;
};
