/**
 * <AUTHOR>
 * @namespace Config_Panels
 * @description Navigation Panel
 */

import React, { FC } from "react";
import { ConfigNavigationBottomButtonsCard } from "../components/navigation/bottom-buttons-card";
import { ConfigNavigationSubActionCard } from "../components/navigation/sub-action-card";
import { ConfigNavigationTabsCard } from "../components/navigation/tabs-card";

export type ConfigNavigationPanelProps = {
};

// LAZY LOAD ONLY
const ConfigNavigationPanel: FC<ConfigNavigationPanelProps> = (
    _props: ConfigNavigationPanelProps,
) => {

    return (<div
        className="flex flex-col gap-2 pr-2 py-2 flex-1"
    >
        <ConfigNavigationSubActionCard />
        <ConfigNavigationTabsCard />
        <ConfigNavigationBottomButtonsCard />
    </div>);
};
export default ConfigNavigationPanel;
