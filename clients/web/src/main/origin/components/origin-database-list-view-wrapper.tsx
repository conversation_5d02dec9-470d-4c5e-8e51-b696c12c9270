/**
 * <AUTHOR>
 * @namespace Origin_Components
 * @description Origin Database List View Wrapper
 */

import { IImbricateOrigin } from "@imbricate/core";
import { IMBRICATE_ORIGIN_FEATURE, checkImbricateOriginFeatureSupported } from "@imbricate/core/origin/feature";
import React, { FC } from "react";
import { OriginDatabaseListView } from "./origin-database-list-view";
import { OriginUnsupportedWarning } from "../../common/components/unsupported/origin-unsupported-warning";

export type OriginDatabaseListViewProps = {

    readonly originName: string;
    readonly origin: IImbricateOrigin;
};

export const OriginDatabaseListViewWrapper: FC<OriginDatabaseListViewProps> = (
    props: OriginDatabaseListViewProps,
) => {

    const originSupported = checkImbricateOriginFeatureSupported(
        props.origin.supportedFeatures,
        IMBRICATE_ORIGIN_FEATURE.ORIGIN_DATABASE_MANAGER,
    );

    if (!originSupported) {

        return (<OriginUnsupportedWarning
            originName={props.originName}
            origin={props.origin}
            feature={IMBRICATE_ORIGIN_FEATURE.ORIGIN_DATABASE_MANAGER}
            variant="faded"
        />);
    }

    return (<OriginDatabaseListView
        originName={props.originName}
        origin={props.origin}
    />);
};
