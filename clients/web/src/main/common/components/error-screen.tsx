/**
 * <AUTHOR>
 * @namespace Common_Components
 * @description Error Screen
 */

import { ErrorMessageAlert, ImbricateLogo } from "@imbricate-hummingbird/react-components";
import { UIColor } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { BrandFooter } from "./footer";

export type ErrorScreenProps = {

    readonly title?: string;
    readonly message?: React.ReactNode;

    readonly color?: UIColor;
    readonly showFooter?: boolean;
};

export const ErrorScreen: FC<ErrorScreenProps> = (
    props: ErrorScreenProps,
) => {

    return (<div
        className="h-full flex flex-col justify-center items-center font-mono"
    >
        <div
            className="flex-1 flex flex-col justify-center items-center"
        >
            <div
                className="flex flex-col gap-4 items-center min-w-96 pr-2"
            >
                <ImbricateLogo
                    size="large"
                />
                <ErrorMessageAlert
                    title={props.title}
                    message={props.message}
                    color={props.color}
                />
            </div>
        </div>
        {props.showFooter && <div
            className="w-full pb-2 px-4"
        >
            <BrandFooter
                noBranding
            />
        </div>}
    </div>);
};
