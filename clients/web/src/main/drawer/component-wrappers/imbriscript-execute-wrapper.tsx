/**
 * <AUTHOR>
 * @namespace Drawer_Component_Wrappers
 * @description ImbriScript Execute Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import { DrawerImbriScriptExecuteProps } from "../components/imbriscript-execute";

const DrawerImbriScriptExecute = LazyLoadComponent(
    () => import("../components/imbriscript-execute"),
    "Drawer ImbriScript Execute",
);

export const DrawerImbriScriptExecuteWrapper: FC<DrawerImbriScriptExecuteProps> = (
    props: DrawerImbriScriptExecuteProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Drawer ImbriScript Execute"
            label={["execute"]}
            fullHeight
        />}
    >
        <DrawerImbriScriptExecute
            {...props}
        />
    </React.Suspense>);
};
