/**
 * <AUTHOR>
 * @namespace Origin_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooLazyInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { ORIGIN_PROFILE } from "./profile";

export const originInternationalization: SudooLazyInternationalization<ORIGIN_PROFILE> =
    SudooLazyInternationalization.create<ORIGIN_PROFILE>(
        DEFAULT_LOCALE,
    );

originInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    import("./locale/zh-CN").then(
        (module) => module.zhCNOriginProfile,
    ),
);

originInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/en-US").then(
        (module) => module.enUSOriginProfile,
    ),
);
