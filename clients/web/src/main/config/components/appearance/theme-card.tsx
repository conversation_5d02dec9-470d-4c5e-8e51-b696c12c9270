/**
 * <AUTHOR>
 * @namespace Config_Components
 * @description Theme Card
 */

import { FormattedRadioGroup, FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { useControlledTheme } from "@imbricate-hummingbird/theme-core";
import { UICard<PERSON>ody, UICard<PERSON><PERSON>er, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { FaPaintBrush } from "react-icons/fa";
import { useConfigFormat } from "../../internationalization/hook";
import { CONFIG_PROFILE } from "../../internationalization/profile";

export const ConfigThemeCard = () => {

    const heroTheme = useControlledTheme();

    const configFormat = useConfigFormat();

    const themeValue = heroTheme.theme;

    return (<StyledCard>
        <UICardHeader>
            <FaPaintBrush />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.THEME)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody>
            <FormattedRadioGroup
                label={configFormat.get(CONFIG_PROFILE.APPLICATION_THEME)}
                selected={themeValue}
                onSelectedChange={(newSelected: string) => {

                    const theme = newSelected;
                    heroTheme.setTheme(theme);
                }}
                format={configFormat}
                items={[
                    {
                        itemKey: "system",
                        itemProfileItem: CONFIG_PROFILE.SYSTEM_THEME,
                    },
                    {
                        itemKey: "light",
                        itemProfileItem: CONFIG_PROFILE.LIGHT,
                    },
                    {
                        itemKey: "dark",
                        itemProfileItem: CONFIG_PROFILE.DARK,
                    },
                ]}
            />
        </UICardBody>
    </StyledCard>);
};
