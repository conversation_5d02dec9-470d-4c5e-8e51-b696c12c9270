/**
 * <AUTHOR>
 * @namespace Document_Components_TableCells
 * @description JSON Cell
 */

import { openEditWindow, openViewWindow } from "@imbricate-hummingbird/navigation-core";
import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIButton, UIButtonGroup, UIPopover, UITooltip, createUIButton } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { FaEye } from "react-icons/fa6";
import { MdAddCircleOutline, MdEdit, MdOutlineInfo } from "react-icons/md";
import { CommonCopyItem } from "../../../common/components/copy-item";
import { DocumentTableCellContent } from "./cell-content";

export type DocumentTableJsonCellProps = {

    readonly originUniqueIdentifier: string | null;
    readonly databaseUniqueIdentifier: string | null;
    readonly documentUniqueIdentifier: string | null;

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.JSON>;
    readonly getEditingProperty: () => TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.JSON> | undefined;
    readonly updateEditingProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.JSON>) => void;
    readonly editing: boolean;
};

export const DocumentTableJsonCell: FC<DocumentTableJsonCellProps> = (
    props: DocumentTableJsonCellProps,
) => {

    if (props.editing) {

        return (<div
            className="flex items-center gap-1"
        >
            <div>
                N/A
            </div>
            <UIPopover
                placement="left"
                trigger={createUIButton({
                    isIconOnly: true,
                    color: "primary",
                    variant: "light",
                    size: "sm",
                    children: (<MdOutlineInfo />),
                })}
            >
                JSON property is not editable, please edit the JSON file directly after saving the document.
            </UIPopover>
        </div>);
    }

    return (<DocumentTableCellContent
        schemaType={IMBRICATE_PROPERTY_TYPE.JSON}
        property={props.property}
        render={(
            propertyDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> | null,
        ) => {

            const value: string | undefined =
                typeof propertyDraft === "undefined" || propertyDraft === null
                    ? undefined
                    : propertyDraft.value as string;

            const fileAlreadyExists = typeof value === "string" && value.length > 0;

            if (fileAlreadyExists) {

                return (<UITooltip
                    content={<CommonCopyItem
                        startContent="Text Unique Identifier"
                        content={value as string}
                    />}
                    delay={1000}
                    placement="bottom"
                >
                    <UIButtonGroup
                        color="default"
                        size="sm"
                        variant="flat"
                    >
                        <UIButton
                            startContent={<MdEdit />}
                            color="secondary"
                            onPress={() => {

                                if (
                                    !props.originUniqueIdentifier
                                    || !props.databaseUniqueIdentifier
                                    || !props.documentUniqueIdentifier) {
                                    throw new Error("[Imbricate] Database or document unique identifier not found");
                                }

                                openEditWindow(
                                    props.originUniqueIdentifier,
                                    props.databaseUniqueIdentifier,
                                    props.documentUniqueIdentifier,
                                    props.propertyKey,
                                );
                            }}
                        >
                            JSON
                        </UIButton>
                        <UIButton
                            isIconOnly
                            onPress={() => {

                                if (
                                    !props.originUniqueIdentifier
                                    || !props.databaseUniqueIdentifier
                                    || !props.documentUniqueIdentifier) {
                                    throw new Error("[Imbricate] Database or document unique identifier not found");
                                }

                                openViewWindow(
                                    props.originUniqueIdentifier,
                                    props.databaseUniqueIdentifier,
                                    props.documentUniqueIdentifier,
                                    props.propertyKey,
                                );
                            }}
                        >
                            <FaEye />
                        </UIButton>
                    </UIButtonGroup>
                </UITooltip>);
            }

            return (<UIButton
                startContent={<MdAddCircleOutline />}
                color="primary"
                size="sm"
                variant="flat"
                onPress={() => {

                    if (!props.originUniqueIdentifier
                        || !props.databaseUniqueIdentifier
                        || !props.documentUniqueIdentifier) {
                        throw new Error("[Imbricate] Database or document unique identifier not found");
                    }

                    openEditWindow(
                        props.originUniqueIdentifier,
                        props.databaseUniqueIdentifier,
                        props.documentUniqueIdentifier,
                        props.propertyKey,
                    );
                }}
            >
                JSON
            </UIButton>);
        }}
    />);
};
