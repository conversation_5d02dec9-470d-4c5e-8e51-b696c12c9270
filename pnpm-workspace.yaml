packages:
  - clients/*
  - packages/*

catalog:
  "@imbricate/core": 3.32.0
  "@types/react": 19.1.8
  "@types/react-dom": 19.1.6
  "@xyflow/react": 12.8.2
  clsx: 2.1.1
  eslint: 9.32.0
  monaco-editor: 0.52.2
  react: 19.1.0
  react-dom: 19.1.0
  react-error-boundary: 6.0.0
  react-icons: 5.5.0
  react-router-dom: 7.7.1

onlyBuiltDependencies:
  - "@heroui/shared-utils"
  - "@sentry/cli"
  - "@tailwindcss/oxide"
  - "@vercel/speed-insights"
  - electron
  - esbuild
  - unrs-resolver

updateConfig:
  ignoreDependencies:
    - "@heroui/*"
    - "@storybook/*"
    - electron
    - eslint-plugin-storybook
    - storybook
    - vite
