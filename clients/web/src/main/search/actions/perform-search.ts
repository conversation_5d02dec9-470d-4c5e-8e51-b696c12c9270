/**
 * <AUTHOR>
 * @namespace Search_Actions
 * @description Perform Search
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createPerformSearchAction = (
    keyword: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Perform Search",
        actionDescription: `Search all origins for keyword: [${keyword}]`,
        actionPayload: {
            keyword,
        },
        executerMetadata: metadata,
    };
};
