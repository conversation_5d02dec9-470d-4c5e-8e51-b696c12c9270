/**
 * <AUTHOR>
 * @namespace Debug
 * @description Debug Tabs Button
 */

import { UIButton } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { FaClipboardList } from "react-icons/fa6";

export type DebugTabsButtonProps = {

    readonly onToggle: () => void;
};

export const DebugTabsButton: FC<DebugTabsButtonProps> = (
    props: DebugTabsButtonProps,
) => {

    return (<UIButton
        size="lg"
        isIconOnly
        variant="flat"
        color="secondary"
        onPress={props.onToggle}
    >
        <FaClipboardList
            className="text-xl"
        />
    </UIButton>);
};
