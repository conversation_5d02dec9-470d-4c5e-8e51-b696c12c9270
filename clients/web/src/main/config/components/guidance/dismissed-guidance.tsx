/**
 * <AUTHOR>
 * @namespace ConfigComponents_Guidance_DismissedGuidance
 * @description Dismissed Guidance
 */

import { UseAllDismissedGuidanceItem, useAllDismissedGuidance } from "@imbricate-hummingbird/configuration";
import { FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody, UICardHeader, UIDivider, UISpacer, UISwitch } from "@imbricate-hummingbird/ui";
import { FaBook } from "react-icons/fa";
import { FaEye, FaEyeSlash } from "react-icons/fa6";
import { useConfigFormat } from "../../internationalization/hook";
import { CONFIG_PROFILE } from "../../internationalization/profile";
import { useConfigurationDismissedGuidanceFormat } from "./dismissed-guidance/hook";

export const ConfigGuidanceDismissedGuidanceCard = () => {

    const configFormat = useConfigFormat();
    const dismissedGuidanceFormat = useConfigurationDismissedGuidanceFormat();

    const dismissedGuidances = useAllDismissedGuidance(dismissedGuidanceFormat);

    return (<StyledCard>
        <UICardHeader>
            <FaBook />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.DISMISSED_GUIDANCE)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-2"
        >
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                    placeholderClassName="h-4 rounded-md"
                >
                    {configFormat.get(CONFIG_PROFILE.DISMISSED_GUIDANCE_DESCRIPTION)}
                </FormattedText>
            </div>
            {dismissedGuidances.dismissedGuidanceList.map((
                item: UseAllDismissedGuidanceItem,
            ) => {
                return (<div key={item.key}>
                    <UISwitch
                        isSelected={!item.value}
                        size="lg"
                        width="50px"
                        onSelectedChange={(isSelected: boolean) => {
                            item.onSetDismissed(!isSelected);
                        }}
                        thumbIcon={!item.value ? <FaEye /> : <FaEyeSlash />}
                        thumbIconClassName="w-4 h-4"
                    >
                        {item.description}
                    </UISwitch>
                </div>);
            })}
        </UICardBody>
    </StyledCard>);
};
