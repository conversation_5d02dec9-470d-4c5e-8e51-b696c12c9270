/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyValue
 * @description Markdown Value
 */

import { openEditWindow } from "@imbricate-hummingbird/navigation-core";
import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIButton } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { MdAddCircleOutline } from "react-icons/md";
import { useDocumentFormat } from "../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../internationalization/profile";
import { DocumentMarkdownValueEditFullMode } from "./markdown/edit-full-mode";
import { DocumentMarkdownValueEditLiteMode } from "./markdown/edit-lite-mode";

export type DocumentMarkdownValueProps = {

    readonly liteMode?: boolean;

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.MARKDOWN>;
    readonly updateProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.MARKDOWN>) => void;
};

export const DocumentMarkdownValue: FC<DocumentMarkdownValueProps> = (
    props: DocumentMarkdownValueProps,
) => {

    const documentFormat = useDocumentFormat();

    const markdownAlreadyExists = props.property.value?.length > 0;

    if (markdownAlreadyExists) {

        if (props.liteMode) {

            return (<DocumentMarkdownValueEditLiteMode
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                propertyKey={props.propertyKey}
                property={props.property}
                updateProperty={props.updateProperty}
            />);
        }

        return (<DocumentMarkdownValueEditFullMode
            originUniqueIdentifier={props.originUniqueIdentifier}
            databaseUniqueIdentifier={props.databaseUniqueIdentifier}
            documentUniqueIdentifier={props.documentUniqueIdentifier}
            propertyKey={props.propertyKey}
            property={props.property}
            updateProperty={props.updateProperty}
        />);
    }

    return (<UIButton
        isFullWidth
        startContent={<MdAddCircleOutline />}
        color="primary"
        variant="flat"
        onPress={() => {

            if (
                !props.originUniqueIdentifier
                || !props.databaseUniqueIdentifier
                || !props.documentUniqueIdentifier) {
                throw new Error("[Imbricate] Database or document unique identifier not found");
            }

            openEditWindow(
                props.originUniqueIdentifier,
                props.databaseUniqueIdentifier,
                props.documentUniqueIdentifier,
                props.propertyKey,
            );
        }}
    >
        {documentFormat.get(DOCUMENT_PROFILE.CREATE_MARKDOWN_DOCUMENT)}
    </UIButton>);
};
