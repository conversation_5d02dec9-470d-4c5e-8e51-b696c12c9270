/**
 * <AUTHOR>
 * @namespace Transfer_Hooks
 * @description Use Document
 */
// ONLY RUN IN RENDERER

import { rootLogger } from "@/main/log/logger";
import { $ERROR, $LOADING, HookSymbol } from "@imbricate-hummingbird/global-symbol";
import { ActionCentral, ActionDescription } from "@imbricate-hummingbird/interceptor-core";
import { OriginWorkerDataCentral } from "@imbricate-hummingbird/origin-central-web-worker";
import { TransferDocument } from "@imbricate-hummingbird/transfer-core";
import { useEffect, useState } from "react";

const logger = rootLogger.fork({
    scopes: [
        "Transfer",
        "Hooks",
        "UseDocument",
    ],
});

export const useOldDocument = (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    actionDescription: ActionDescription,
): TransferDocument | HookSymbol => {

    const [errored, setErrored] = useState<boolean>(false);
    const [document, setDocument] = useState<TransferDocument | null>(null);

    useEffect(() => {

        const execute = async () => {

            try {

                const documentResult = await ActionCentral.getInstance().executeAction(
                    async (
                        actionIdentifier: string,
                        recordIdentifier: string,
                    ) => {

                        return await OriginWorkerDataCentral.getInstance().getDocument(
                            actionIdentifier,
                            recordIdentifier,
                            originUniqueIdentifier,
                            databaseUniqueIdentifier,
                            documentUniqueIdentifier,
                        );
                    },
                    actionDescription,
                );

                setDocument(documentResult.document);
            } catch (error) {

                logger.error(error);
                setErrored(true);
            }
        };

        execute();
    }, [
        originUniqueIdentifier,
        databaseUniqueIdentifier,
        documentUniqueIdentifier,
    ]);

    if (errored) {
        return $ERROR;
    }

    if (!document) {
        return $LOADING;
    }

    return document;
};
