/**
 * <AUTHOR>
 * @namespace Lens_Components_Renders
 * @description Render Imbriscript Wrapper
 */

import { useMagicButtonAdjustItems } from "@/common/components/magic-button/hooks/use-adjust-items";
import { UseMagicButtonResult } from "@/common/components/magic-button/hooks/use-magic-button";
import { rootLogger } from "@/main/log/logger";
import { LENS_CONFIG_SOURCE, LensConfigItem } from "@imbricate-hummingbird/configuration";
import { isDefaultEmptyFormat } from "@imbricate-hummingbird/internationalization";
import { useNavigateDocumentView } from "@imbricate-hummingbird/react-navigation";
import { DRAWER_TYPE, useDrawerAction } from "@imbricate-hummingbird/react-store";
import { FC } from "react";
import { FaEdit } from "react-icons/fa";
import { TbLayoutSidebarRightExpandFilled } from "react-icons/tb";
import { useWideOrigin } from "../../../origin/hooks/use-wide-origin";
import { useProperty } from "../../../property/hooks/use-property";
import { useLensFormat } from "../../internationalization/hook";
import { LENS_PROFILE } from "../../internationalization/profile";
import { RenderImbriscriptLens } from "./render-imbriscript";

const logger = rootLogger.fork({
    scopes: [
        "Lens",
        "Renders",
        "Imbriscript",
        "Wrapper",
    ],
});

export type RenderImbriscriptLensWrapperProps = {

    readonly version: number;
    readonly triggerThrottleReload: () => void;

    readonly lensItem: LensConfigItem<LENS_CONFIG_SOURCE.IMBRISCRIPT>;

    readonly magicButtonResult: UseMagicButtonResult;
};

export const RenderImbriscriptLensWrapper: FC<RenderImbriscriptLensWrapperProps> = (
    props: RenderImbriscriptLensWrapperProps,
) => {

    const lensFormat = useLensFormat();

    const navigateToDocumentView = useNavigateDocumentView();

    const drawerActions = useDrawerAction();

    const property = useProperty(
        props.lensItem.target.databaseUniqueIdentifier,
        props.lensItem.target.documentUniqueIdentifier,
        props.lensItem.target.propertyKey,
        {
            databaseDependencies: [props.version],
            documentDependencies: [props.version],
        },
    );

    const origin = useWideOrigin(
        props.lensItem.target.databaseUniqueIdentifier,
    );

    useMagicButtonAdjustItems(
        props.magicButtonResult,
        !isDefaultEmptyFormat(lensFormat) && Boolean(origin),
        (result: UseMagicButtonResult) => {

            result.addItem({
                categoryKey: "imbriscript",
                itemKey: "edit-imbriscript",
                icon: FaEdit,
                title: lensFormat.get(LENS_PROFILE.EDIT_IMBRISCRIPT),
                description: lensFormat.get(LENS_PROFILE.EDIT_IMBRISCRIPT_DESCRIPTION),
                onPress: () => {

                    logger.debug("Edit Imbricate Script", {
                        originUniqueIdentifier: origin!.origin.uniqueIdentifier,
                        databaseUniqueIdentifier: props.lensItem.target.databaseUniqueIdentifier,
                        documentUniqueIdentifier: props.lensItem.target.documentUniqueIdentifier,
                    });

                    navigateToDocumentView(
                        origin!.origin.uniqueIdentifier,
                        props.lensItem.target.databaseUniqueIdentifier,
                        props.lensItem.target.documentUniqueIdentifier,
                    );
                },
            });

            result.addItem({
                categoryKey: "imbriscript",
                itemKey: "open-imbriscript-document-drawer",
                icon: TbLayoutSidebarRightExpandFilled,
                title: lensFormat.get(LENS_PROFILE.OPEN_IMBRISCRIPT_DOCUMENT_IN_DRAWER),
                description: lensFormat.get(LENS_PROFILE.OPEN_IMBRISCRIPT_DOCUMENT_IN_DRAWER_DESCRIPTION),
                quickAccess: true,
                onPress: () => {

                    logger.debug("Open Drawer", {
                        originUniqueIdentifier: origin!.origin.uniqueIdentifier,
                        databaseUniqueIdentifier: props.lensItem.target.databaseUniqueIdentifier,
                        documentUniqueIdentifier: props.lensItem.target.documentUniqueIdentifier,
                    });

                    drawerActions.openDrawer({
                        type: DRAWER_TYPE.DOCUMENT_VIEW,
                        title: lensFormat.get(LENS_PROFILE.EDIT_IMBRISCRIPT),
                        payload: {
                            originUniqueIdentifier: origin!.origin.uniqueIdentifier,
                            databaseUniqueIdentifier: props.lensItem.target.databaseUniqueIdentifier,
                            documentUniqueIdentifier: props.lensItem.target.documentUniqueIdentifier,
                        },
                    });
                },
            });

            return () => {
                result.removeItem("edit-imbriscript");
                result.removeItem("open-imbriscript-document-drawer");
            };
        },
        [
            typeof origin,
            props.lensItem.target.databaseUniqueIdentifier,
            props.lensItem.target.documentUniqueIdentifier,
            props.lensItem.target.propertyKey,
        ],
    );

    if (typeof property === "symbol"
        || !origin) {
        return null;
    }

    if (
        typeof property.documentProperty === "undefined"
        || property.documentProperty.propertyValue === null
    ) {
        return null;
    }

    return (<RenderImbriscriptLens
        version={props.version}
        triggerThrottleReload={props.triggerThrottleReload}
        lensItem={props.lensItem}
        property={property}
        originUniqueIdentifier={origin.origin.uniqueIdentifier}
        textIdentifier={property.documentProperty.propertyValue as string | undefined}
    />);
};
