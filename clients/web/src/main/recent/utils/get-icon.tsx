/**
 * <AUTHOR>
 * @namespace Recent_Utils
 * @description Get Icon
 */

import { RECENT_TYPE } from "@imbricate-hummingbird/configuration";
import React from "react";
import { AiFillFileUnknown } from "react-icons/ai";
import { FaDatabase } from "react-icons/fa";
import { IoIosDocument } from "react-icons/io";
import { MdLabel } from "react-icons/md";
import { RiCameraLensFill } from "react-icons/ri";

export const getRecentIcon = (
    recentType: RECENT_TYPE,
    className?: string,
): React.ReactNode => {

    // RECENT_TYPE SWITCH
    switch (recentType) {

        case RECENT_TYPE.DATABASE:
            return (<FaDatabase
                className={className}
            />);
        case RECENT_TYPE.DOCUMENT:
            return (<IoIosDocument
                className={className}
            />);
        case RECENT_TYPE.LENS:
            return (<RiCameraLensFill
                className={className}
            />);
        case RECENT_TYPE.PROPERTY:
            return (<MdLabel
                className={className}
            />);
    }

    return (<AiFillFileUnknown
        className={className}
    />);
};
