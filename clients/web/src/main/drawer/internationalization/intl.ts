/**
 * <AUTHOR>
 * @namespace Drawer_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooLazyInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { DRAWER_PROFILE } from "./profile";

export const drawerInternationalization: SudooLazyInternationalization<DRAWER_PROFILE> =
    SudooLazyInternationalization.create<DRAWER_PROFILE>(
        DEFAULT_LOCALE,
    );

drawerInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    import("./locale/zh-CN").then(
        (module) => module.zhCNDrawerProfile,
    ),
);

drawerInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/en-US").then(
        (module) => module.enUSDrawerProfile,
    ),
);
