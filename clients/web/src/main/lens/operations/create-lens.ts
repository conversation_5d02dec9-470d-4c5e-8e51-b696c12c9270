/**
 * <AUTHOR>
 * @namespace Lens_Operations
 * @description Create Lens
 */

import { HUMMINGBIRD_MANAGED_CONFIG_ITEM, LENS_CONFIG_SOURCE, LensConfig, LensConfigItem, LensConfigTarget, ManagedConfigController } from "@imbricate-hummingbird/configuration";
import { UUIDVersion1 } from "@sudoo/uuid";

export type CreateLensResponse = {

    readonly newLens: LensConfigItem<LENS_CONFIG_SOURCE.IMBRISCRIPT>;
    readonly updated: LensConfig;
};

export const createLens = (
    lensName: string,
    lensTarget: LensConfigTarget<LENS_CONFIG_SOURCE>,
): CreateLensResponse => {

    const current: LensConfig = ManagedConfigController.getInstance()
        .getManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.LENS_CONFIG);

    const newItem: LensConfigItem<LENS_CONFIG_SOURCE.IMBRISCRIPT> = {

        lensIdentifier: UUIDVersion1.generateString(),

        lensName,
        source: LENS_CONFIG_SOURCE.IMBRISCRIPT,
        target: lensTarget,
    };

    const updated: LensConfig = {
        items: [
            ...current.items,
            newItem,
        ],
    };

    ManagedConfigController.getInstance()
        .setManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.LENS_CONFIG, updated);

    return {
        newLens: newItem,
        updated,
    };
};
