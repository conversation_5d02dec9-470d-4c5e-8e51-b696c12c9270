/**
 * <AUTHOR>
 * @package UI
 * @namespace Navbar
 * @description Navbar
 */

import { Navbar, NavbarBrand, NavbarContent } from "@heroui/navbar";
import type { FC, ReactNode } from "react";

export type UINavbarProps = {

    readonly className?: string;
    readonly startClassName?: string;
    readonly coreClassName?: string;
    readonly endClassName?: string;

    readonly isFullWidth?: boolean;
    readonly isBordered?: boolean;

    readonly startContent: ReactNode;
    readonly coreContent: ReactNode;
    readonly endContent?: ReactNode;
};

export const UINavbar: FC<UINavbarProps> = (
    props: UINavbarProps,
) => {

    return (<Navbar
        className={props.className}
        maxWidth={props.isFullWidth ? "full" : undefined}
        isBordered={props.isBordered}
    >
        <NavbarBrand
            className={props.startClassName}
        >
            {props.startContent}
        </NavbarBrand>
        <NavbarContent
            justify="center"
            className={props.coreClassName}
        >
            {props.coreContent}
        </NavbarContent>
        <NavbarContent
            justify="end"
            className={props.endClassName}
        >
            {props.endContent ?? null}
        </NavbarContent>
    </Navbar>);
};
