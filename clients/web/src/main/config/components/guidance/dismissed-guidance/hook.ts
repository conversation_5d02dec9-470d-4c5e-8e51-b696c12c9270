/**
 * <AUTHOR>
 * @namespace Configuration_Internationalization_DismissedGuidance
 * @description Hook
 */

import { HUMMINGBIRD_DISMISSED_GUIDANCE_ITEM } from "@imbricate-hummingbird/configuration";
import { useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { configurationDismissedGuidanceInternationalization } from "./intl";

export const useConfigurationDismissedGuidanceFormat = (): SudooFormat<HUMMINGBIRD_DISMISSED_GUIDANCE_ITEM> => {

    const locale = useLocale();

    return configurationDismissedGuidanceInternationalization.format(locale);
};
