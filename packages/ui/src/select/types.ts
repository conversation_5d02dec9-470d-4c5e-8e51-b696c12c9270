/**
 * <AUTHOR>
 * @package UI
 * @namespace Select
 * @description Types
 */

import type { ReactNode } from "react";
import { UIColor, UISize } from "../common/types";

export type UISelectSelectedItem = {

    readonly itemKey: string;

    readonly itemTextValue?: string;
};

export type UISelectCommonProps = {

    readonly ariaLabel: string;

    readonly className?: string;

    readonly size?: UISize;
    readonly color?: UIColor;

    readonly isFullWidth?: boolean;
    readonly isDisabled?: boolean;
    readonly isLoading?: boolean;
    readonly isInvalid?: boolean;

    readonly label?: ReactNode;
    readonly startContent?: ReactNode;
    readonly endContent?: ReactNode;
    readonly errorMessage?: string;

    readonly renderValue?: (
        selectedItems: UISelectSelectedItem[],
    ) => ReactNode;

    readonly items: UISelectItemData[];
};

export type UISelectItemData = {

    readonly itemKey: string;

    readonly className?: string;

    readonly isDisabled?: boolean;

    readonly color?: UIColor;
    readonly variant?: UISelectItemVariant;

    readonly startContent?: ReactNode;
    readonly endContent?: ReactNode;

    readonly content: ReactNode;
    readonly textValue?: string;
};

export type UISelectItemVariant =
    | "flat"
    | "faded"
    | "solid"
    | "bordered"
    | "light"
    | "shadow";

export type UISelectSelectionMode =
    | "single"
    | "multiple";
