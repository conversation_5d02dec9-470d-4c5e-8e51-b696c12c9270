/**
 * <AUTHOR>
 * @namespace Common_Components_MainWrapper
 * @description Main Wrapper
 */

import { DebugActionBarWrapper } from "@/main/debug/components/debug-action-bar-wrapper";
import { ScriptExecutionProvider } from "@/script/execution-provider";
import { DocCastModalProvider } from "@imbricate-hummingbird/doc-cast";
import { useControlledTheme } from "@imbricate-hummingbird/theme-core";
import clsx from "clsx";
import React, { FC } from "react";
import { DataRecoverWrapper } from "../../data/components/data-recover-wrapper";
import { useDataRecover } from "../../data/hooks/use-data-recover";

export type MainWrapperProps = {

    readonly children: React.ReactNode;

    readonly className?: string;
};

export const MainWrapper: FC<MainWrapperProps> = (
    props: MainWrapperProps,
) => {

    const theme = useControlledTheme();
    const dataRecover = useDataRecover();

    const fixedClassNames = clsx(
        theme.theme,
        "text-foreground bg-background h-dvh",
        props.className,
    );

    const showDataRecover = dataRecover.recoverableItems.length > 0;

    return (<DocCastModalProvider>
        <main
            className={fixedClassNames}
        >
            <div
                className={clsx(
                    showDataRecover ? "h-[24px]" : "h-0",
                    "overflow-hidden",
                    "transition-all duration-150 ease-in-out",
                    "bg-danger",
                )}
            >
                <DataRecoverWrapper
                    dataRecover={dataRecover}
                />
            </div>
            <div
                className={clsx(
                    showDataRecover ? "h-[calc(100vh-24px)]" : "h-full",
                    "transition-all duration-150 ease-in-out",
                )}
            >
                {props.children}
            </div>
            <DebugActionBarWrapper />
            <ScriptExecutionProvider />
        </main>
    </DocCastModalProvider>);
};
