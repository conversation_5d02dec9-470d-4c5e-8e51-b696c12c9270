/**
 * <AUTHOR>
 * @namespace Main_Config_Components_ImbriScript_OutlookCard
 * @description Outlook Card
 */

import { FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UIButton, UICardBody, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { FaRegFileCode } from "react-icons/fa6";
import { RiCameraLensAiFill } from "react-icons/ri";
import { useConfigFormat } from "../../internationalization/hook";
import { CONFIG_PROFILE } from "../../internationalization/profile";
import { useNavigateConfigImbricateScriptOutlookRoute } from "../../navigation/hooks/use-config-route";

export type ConfigImbricateScriptOutlookCardProps = {
};

export const ConfigImbricateScriptOutlookCard: FC<ConfigImbricateScriptOutlookCardProps> = (
    _props: ConfigImbricateScriptOutlookCardProps,
) => {

    const configFormat = useConfigFormat();

    const navigateToConfigImbricateScriptOutlook = useNavigateConfigImbricateScriptOutlookRoute();

    return (<StyledCard>
        <UICardHeader>
            <RiCameraLensAiFill />
            <UISpacer />
            {configFormat.get(CONFIG_PROFILE.IMBRISCRIPT_OUTLOOK)}
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-2"
        >
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                >
                    {configFormat.get(CONFIG_PROFILE.IMBRISCRIPT_OUTLOOK_DESCRIPTION)}
                </FormattedText>
            </div>
            <UIButton
                radius="sm"
                variant="flat"
                color="primary"
                startContent={<FaRegFileCode />}
                onPress={() => {
                    navigateToConfigImbricateScriptOutlook();
                }}
            >
                {configFormat.get(CONFIG_PROFILE.LAUNCH_IMBRISCRIPT_OUTLOOK)}
            </UIButton>
        </UICardBody>
    </StyledCard>);
};
