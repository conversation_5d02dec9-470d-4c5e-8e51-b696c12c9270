/**
 * <AUTHOR>
 * @package Flow Common
 * @namespace Components
 * @description No Drag Div
 */

import clsx from "clsx";
import type { ReactNode } from "react";

export type FlowNoDragDivProps = {

    readonly children: ReactNode;

    readonly className?: string;
};

export const FlowNoDragDiv = (
    props: FlowNoDragDivProps,
) => {

    return (<div
        className={clsx(
            "nodrag",
            props.className,
        )}
    >
        {props.children}
    </div>);
};
