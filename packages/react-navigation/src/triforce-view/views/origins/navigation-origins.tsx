/**
 * <AUTHOR>
 * @namespace Navigation
 * @description Navigation Origins
 */

import { getRouteOriginNewView, getRouteOriginView } from "@imbricate-hummingbird/navigation-core";
import { OriginWorkerDataCentral } from "@imbricate-hummingbird/origin-central-web-worker";
import { useOrigins } from "@imbricate-hummingbird/react-origin-central";
import { FC } from "react";
import { FaPlus } from "react-icons/fa";
import { TransferOrigin } from "../../../../../transfer-core/src";
import { NavigationActionButton } from "../../../components/action-button";
import { useNavigationFormat } from "../../../internationalization/hook";
import { NAVIGATION_PROFILE } from "../../../internationalization/profile";

export const NavigationOrigins: FC = () => {

    const navigationFormat = useNavigationFormat();

    const origins = useOrigins(
        OriginWorkerDataCentral.getInstance(),
        {
            actionName: "Get Origins",
            actionDescription: "Get all origins",
            executerMetadata: {
                executer: import.meta.url,
            },
        },
        [],
    );

    if (typeof origins === "symbol") {
        return null;
    }

    return (<div
        className="flex flex-col gap-1"
    >
        <NavigationActionButton
            Icon={FaPlus}
            title={navigationFormat.get(NAVIGATION_PROFILE.ADD_ORIGIN)}
            description={navigationFormat.get(NAVIGATION_PROFILE.ADD_ORIGIN_DESCRIPTION)}
            href={getRouteOriginNewView()}
            color="primary"
        />
        {origins.map((
            origin: TransferOrigin,
        ) => {

            return (<NavigationActionButton
                key={origin.originUniqueIdentifier}
                title={origin.originName}
                description={origin.originInstance.type}
                href={getRouteOriginView(origin.originUniqueIdentifier)}
            />);
        })}
    </div>);
};
