/**
 * <AUTHOR>
 * @namespace Document_Hooks
 * @description Use Document
 */

import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { ReactDependency } from "@imbricate-hummingbird/react-common";
import { IImbricateDocument } from "@imbricate/core";
import { useEffect, useState } from "react";
import { OldDataCentral } from "../../data/old-data-central";
import { createFindDocumentAction } from "../actions/find-document";

export const S_UseDocumentLoading: unique symbol = Symbol("UseDocumentLoading");
export const S_UseDocumentNotFound: unique symbol = Symbol("UseDocumentNotFound");
export const S_UseDocumentErrored: unique symbol = Symbol("UseDocumentErrored");

export type UseDocumentResponseSymbol =
    | typeof S_UseDocumentLoading
    | typeof S_UseDocumentNotFound
    | typeof S_UseDocumentErrored;

export const useOldDocument = (
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    ...dependencies: ReactDependency[]
): IImbricateDocument | UseDocumentResponseSymbol => {

    const [errored, setErrored] = useState<boolean>(false);
    const [document, setDocument] = useState<IImbricateDocument | null>(null);

    useEffect(() => {

        const execute = async () => {

            const documentResult = await ActionCentral.getInstance().executeAction(
                async (
                    actionIdentifier: string,
                    recordIdentifier: string,
                ) => {

                    return await OldDataCentral.getInstance().wideOriginGetDocument(
                        databaseUniqueIdentifier,
                        documentUniqueIdentifier,
                        actionIdentifier,
                        recordIdentifier,
                    );
                },
                createFindDocumentAction(
                    databaseUniqueIdentifier,
                    documentUniqueIdentifier,
                    {
                        executer: import.meta.url,
                    },
                ),
            );

            if (typeof documentResult === "symbol") {

                setErrored(true);
                return;
            }

            setDocument(documentResult.document);
        };

        execute();
    }, [
        databaseUniqueIdentifier,
        documentUniqueIdentifier,
        ...dependencies,
    ]);

    if (errored) {
        return S_UseDocumentErrored;
    }

    if (!document) {
        return S_UseDocumentLoading;
    }

    return document;
};
