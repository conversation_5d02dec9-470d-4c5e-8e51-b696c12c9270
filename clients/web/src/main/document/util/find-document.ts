/**
 * <AUTHOR>
 * @namespace Document_Utils
 * @description Find Document
 */

import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { IImbricateDocument } from "@imbricate/core";
import { OldDataCentral } from "../../data/old-data-central";
import { createFindDocumentAction } from "../actions/find-document";
import { S_UseDocumentLoading, UseDocumentResponseSymbol } from "../hooks/use-document";

export const findDocument = async (
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
): Promise<IImbricateDocument | UseDocumentResponseSymbol> => {

    const document = await ActionCentral.getInstance().executeAction(
        async (
            actionIdentifier: string,
            recordIdentifier: string,
        ) => {
            return await OldDataCentral.getInstance()
                .wideOriginGetDocument(
                    databaseUniqueIdentifier,
                    documentUniqueIdentifier,
                    actionIdentifier,
                    recordIdentifier,
                );
        },
        createFindDocumentAction(
            databaseUniqueIdentifier,
            documentUniqueIdentifier,
            {
                executer: import.meta.url,
            },
        ),
    );

    if (typeof document === "symbol") {
        return S_UseDocumentLoading;
    }

    return document.document;
};
