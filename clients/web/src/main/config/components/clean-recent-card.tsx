/**
 * <AUTHOR>
 * @namespace Config_Components
 * @description Clean Recent Usage Card
 */

import { HUMMINGBIRD_PASSIVE_DATA_ITEM, usePassiveData } from "@imbricate-hummingbird/configuration";
import { FormattedText, StyledCard, openSuccessToaster } from "@imbricate-hummingbird/react-components";
import { UIButton, UICardBody, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { FaHistory } from "react-icons/fa";
import { FaDeleteLeft } from "react-icons/fa6";
import { useConfigFormat } from "../internationalization/hook";
import { CONFIG_PROFILE } from "../internationalization/profile";

export const ConfigCleanRecentUsageCard = () => {

    const configFormat = useConfigFormat();

    const recentItem = usePassiveData(
        HUMMINGBIRD_PASSIVE_DATA_ITEM.RECENT_ITEMS,
    );

    return (<StyledCard>
        <UICardHeader>
            <FaHistory />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.CLEAN_RECENT_USAGE)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-2"
        >
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                    placeholderClassName="h-4 rounded-md"
                >
                    {configFormat.get(CONFIG_PROFILE.CLEAN_RECENT_USAGE_DESCRIPTION)}
                </FormattedText>
            </div>
            <UIButton
                radius="sm"
                variant="flat"
                color="danger"
                startContent={<FaDeleteLeft />}
                onPress={async () => {
                    recentItem.resetPassiveData();

                    openSuccessToaster(
                        configFormat.get(CONFIG_PROFILE.CLEAN_RECENT_USAGE_SUCCESS),
                    );
                }}
            >
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                >
                    {configFormat.get(CONFIG_PROFILE.CLEAN_RECENT_USAGE)}
                </FormattedText>
            </UIButton>
        </UICardBody>
    </StyledCard>);
};
