/**
 * <AUTHOR>
 * @namespace Landing_AddOrigin
 * @description Landing Add Origin Wrapper
 */

import { getRouteRootView } from "@imbricate-hummingbird/navigation-core";
import { FullLoadingWrapper, LazyLoadComponent } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";
import { Navigate } from "react-router-dom";
import { useOldOrigins } from "../../origin/hooks/use-origins";

const LandingAddOriginView = LazyLoadComponent(
    () => import("./landing-add-origin"),
    "Landing Add Origin View",
);

export const LandingAddOriginWrapper: FC = () => {

    const origins = useOldOrigins();

    if (origins.length > 0) {

        return (<Navigate
            to={getRouteRootView()}
            replace={true}
        />);
    }

    return (<React.Suspense
        fallback={<FullLoadingWrapper
            debugDescription="Landing Add Origin View"
        />}
    >
        <LandingAddOriginView />
    </React.Suspense>);
};
