/**
 * <AUTHOR>
 * @namespace Script_Actions
 * @description Find Origin For Sandbox
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createFindOriginForSandboxAction = (
    databaseUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Find Origin For Sandbox",
        actionDescription: `Find the origin for sandbox from [${databaseUniqueIdentifier}]`,
        executerMetadata: metadata,
    };
};
