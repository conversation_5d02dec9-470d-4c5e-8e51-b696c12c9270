/**
 * <AUTHOR>
 * @namespace Edit_Components
 * @description Edit Title
 */

import { getRouteDatabaseDocumentsView, getRouteDocumentView } from "@imbricate-hummingbird/navigation-core";
import { getPropertyIcon } from "@imbricate-hummingbird/react-components";
import { DRAWER_TYPE, useDrawerAction } from "@imbricate-hummingbird/react-store";
import { IImbricateProperty, IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { FaDatabase } from "react-icons/fa";
import { MdKeyboardDoubleArrowRight } from "react-icons/md";
import { Link } from "react-router-dom";
import { useDatabase } from "../../database/hooks/use-database";
import { useOldDocument } from "../../document/hooks/use-document";
import { UsePropertyResponse } from "../../property/types/use-property";
import { stringifyProperty } from "../../property/utils/stringify";
import { EditTitleLogo } from "./edit-title-logo";

export type EditViewTitleProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;

    readonly primaryValue: IImbricateProperty<IMBRICATE_PROPERTY_TYPE> | null;
    readonly property: UsePropertyResponse;
};

export const EditViewTitle: FC<EditViewTitleProps> = (
    props: EditViewTitleProps,
) => {

    const drawerActions = useDrawerAction();

    const database = useDatabase(props.databaseUniqueIdentifier);
    const document = useOldDocument(props.databaseUniqueIdentifier, props.documentUniqueIdentifier);

    if (typeof database === "symbol" || typeof document === "symbol") {
        return null;
    }

    if (props.primaryValue) {

        const stringifiedValue = stringifyProperty(props.primaryValue);

        return (<div
            className="flex-1 ml-1.5 flex gap-3 items-center mr-5 min-h-0 min-w-0"
        >
            <EditTitleLogo
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                propertyUniqueIdentifier={props.property.schemaProperty.propertyIdentifier}
            />
            <div
                className="flex-1 min-h-0 min-w-0"
            >
                <div
                    className="flex"
                >
                    <div
                        className="font-mono text-current overflow-hidden text-large whitespace-nowrap text-ellipsis cursor-pointer"
                        onClick={() => {

                            drawerActions.openDrawer({
                                type: DRAWER_TYPE.DOCUMENT_VIEW,
                                title: "Document View",
                                payload: {
                                    databaseUniqueIdentifier: database.uniqueIdentifier,
                                    documentUniqueIdentifier: document.uniqueIdentifier,
                                },
                            });
                        }}
                    >
                        {stringifiedValue}
                    </div>
                </div>
                <div
                    className="flex gap-1 items-center"
                >
                    <Link
                        className="text-tiny text-gray-600 flex gap-1 items-center"
                        to={getRouteDatabaseDocumentsView(
                            database.uniqueIdentifier,
                        )}
                        target="_blank"
                    >
                        <FaDatabase />
                        {database.databaseName}
                    </Link>
                    <MdKeyboardDoubleArrowRight
                        className="text-medium text-gray-600"
                    />
                    <Link
                        className="text-tiny text-gray-600 flex gap-1 items-center"
                        to={getRouteDocumentView(
                            database.uniqueIdentifier,
                            document.uniqueIdentifier,
                        )}
                        target="_blank"
                    >
                        {getPropertyIcon(
                            props.property.schemaProperty.propertyType,
                        )}
                        {props.property.schemaProperty.propertyName}
                    </Link>
                </div>
            </div>
        </div>);
    }

    return (<div
        className="flex-1 ml-1.5 flex gap-3 items-center mr-5 min-h-0 min-w-0"
    >
        <EditTitleLogo
            originUniqueIdentifier={props.originUniqueIdentifier}
            databaseUniqueIdentifier={props.databaseUniqueIdentifier}
            documentUniqueIdentifier={props.documentUniqueIdentifier}
            propertyUniqueIdentifier={props.property.schemaProperty.propertyIdentifier}
        />
        <div
            className="flex-1 min-h-0 min-w-0"
        >
            <div
                className="flex gap-1 items-center"
            >
                <Link
                    className="text-tiny text-gray-600 flex gap-1 items-center"
                    to={getRouteDatabaseDocumentsView(
                        database.uniqueIdentifier,
                    )}
                    target="_blank"
                >
                    <FaDatabase />
                    {database.databaseName}
                </Link>
                <MdKeyboardDoubleArrowRight
                    className="text-medium text-gray-600"
                />
                <Link
                    className="text-tiny text-gray-600 flex gap-1 items-center"
                    to={getRouteDocumentView(
                        database.uniqueIdentifier,
                        document.uniqueIdentifier,
                    )}
                    target="_blank"
                >
                    {getPropertyIcon(
                        props.property.schemaProperty.propertyType,
                    )}
                    {props.property.schemaProperty.propertyName}
                </Link>
            </div>
        </div>
    </div>);
};
