/**
 * <AUTHOR>
 * @namespace Debug_Components_TagsManager
 * @description Tags Manager Quick Access
 */

import { StyledCard } from "@imbricate-hummingbird/react-components";
import { HighlightTagManager, MarkdownStyleTagManager, MermaidTagManager } from "@imbricate-hummingbird/react-markdown";
import { UIButton, UICardBody, UIDivider } from "@imbricate-hummingbird/ui";
import React, { useCallback, useEffect, useState } from "react";
import { FaSync } from "react-icons/fa";

export const TagsManagerQuickAccess: React.FC = () => {

    const [markdownStyleUserCount, setMarkdownStyleUserCount] = useState<number>(0);
    const [mermaidUserCount, setMermaidUserCount] = useState<number>(0);
    const [highlightUserCount, setHighlightUserCount] = useState<number>(0);

    const refresh = useCallback(() => {

        const markdownStyleUserCount = MarkdownStyleTagManager.getInstance().getUserCount();
        setMarkdownStyleUserCount(markdownStyleUserCount);

        const mermaidUserCount = MermaidTagManager.getInstance().getUserCount();
        setMermaidUserCount(mermaidUserCount);

        const highlightUserCount = HighlightTagManager.getInstance().getUserCount();
        setHighlightUserCount(highlightUserCount);
    }, []);

    useEffect(() => {
        refresh();
    }, []);

    return (<StyledCard>
        <UICardBody
            className="flex flex-col gap-1 p-1"
        >
            <UIButton
                size="sm"
                radius="sm"
                color="secondary"
                variant="flat"
                startContent={<FaSync />}
                onPress={refresh}
            >
                Refresh
            </UIButton>
        </UICardBody>
        <UIDivider />
        <UICardBody>
            <div
                className="flex gap-2 font-mono"
            >
                <div
                    className="text-tiny text-foreground-500"
                >
                    Markdown Style:
                </div>
                <div
                    className="text-tiny"
                >
                    {markdownStyleUserCount}
                </div>
            </div>
            <div
                className="flex gap-2 font-mono"
            >
                <div
                    className="text-tiny text-foreground-500"
                >
                    Mermaid:
                </div>
                <div
                    className="text-tiny"
                >
                    {mermaidUserCount}
                </div>
            </div>
            <div
                className="flex gap-2 font-mono"
            >
                <div
                    className="text-tiny text-foreground-500"
                >
                    Highlight:
                </div>
                <div
                    className="text-tiny"
                >
                    {highlightUserCount}
                </div>
            </div>
        </UICardBody>
    </StyledCard>);
};
