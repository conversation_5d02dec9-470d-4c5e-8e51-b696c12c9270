/**
 * <AUTHOR>
 * @namespace Database_Views_SchemaView
 * @description Schema View Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const SchemaViewNavigator = LazyLoadComponent(
    () => import("./schema-view-navigator"),
    "Schema View Navigator",
);

export const SchemaViewWrapper: FC = () => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Schema View Navigator"
            fullHeight
        />}
    >
        <SchemaViewNavigator />
    </React.Suspense>);
};
