/**
 * <AUTHOR>
 * @namespace Config_Panels
 * @description Debug Panel
 */

import { useDebugMode } from "@imbricate-hummingbird/debug";
import { FC } from "react";
import { DebugModeCard } from "../components/debug-mode-card";
import { ConfigPWARefreshCard } from "../components/pwa-refresh-card";

export type ConfigDebugPanelProps = {
};

// LAZY LOAD ONLY
const ConfigDebugPanel: FC<ConfigDebugPanelProps> = (
    _props: ConfigDebugPanelProps,
) => {

    const { isDebugMode, setDebugMode, isDevelopmentMode } = useDebugMode();

    return (<div
        className="flex flex-col gap-2 pr-2 py-2 flex-1"
    >
        <DebugModeCard
            isDebugMode={isDebugMode}
            setDebugMode={setDebugMode}
            isDevelopmentMode={isDevelopmentMode}
        />
        <ConfigPWARefreshCard />
    </div>);
};
export default ConfigDebugPanel;
