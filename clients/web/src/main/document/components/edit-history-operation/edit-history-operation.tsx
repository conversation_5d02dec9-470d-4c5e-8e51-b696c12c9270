/**
 * <AUTHOR>
 * @namespace Document_Components_EditHistoryOperation
 * @description Edit History Operation
 */

import { DocumentEditOperation, IMBRICATE_DOCUMENT_EDIT_TYPE, ImbricateDatabaseSchema } from "@imbricate/core";
import React, { FC } from "react";
import { DocumentEditRecordsPutPropertyOperation } from "./put-property";
import { DocumentEditRecordsUnknownOperation } from "./unknown";

export type DocumentEditRecordsOperationProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;

    readonly schema: ImbricateDatabaseSchema;
    readonly operation: DocumentEditOperation<IMBRICATE_DOCUMENT_EDIT_TYPE>;
};

export const DocumentEditRecordsOperation: FC<DocumentEditRecordsOperationProps> = (
    props: DocumentEditRecordsOperationProps,
) => {

    // IMBRICATE_DOCUMENT_EDIT_TYPE SWITCH
    switch (props.operation.action) {

        case IMBRICATE_DOCUMENT_EDIT_TYPE.PUT_PROPERTY: {
            return (<DocumentEditRecordsPutPropertyOperation
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                schema={props.schema}
                operation={props.operation as DocumentEditOperation<IMBRICATE_DOCUMENT_EDIT_TYPE.PUT_PROPERTY>}
            />);
        }
    }

    return (<DocumentEditRecordsUnknownOperation
        operation={props.operation}
    />);
};
