/**
 * <AUTHOR>
 * @namespace Common_Components_MagicButton_Hooks
 * @description Use Controlled Button
 */
// ONLY RUN IN RENDERER

import { getMetaKeyEvent } from "@/common/shortcut/get-meta-key-event";
import { useEffect, useState } from "react";

export const useMagicButtonControlledButton = (
): [boolean, (opened: boolean) => void] => {

    const [opened, setOpened] = useState<boolean>(false);

    useEffect(() => {

        const handleKeyDown = (event: KeyboardEvent) => {

            if (getMetaKeyEvent(event)
                && event.key === "e") {

                event.preventDefault();
                setOpened((prev) => !prev);
            }
        };

        window.addEventListener("keydown", handleKeyDown);

        return () => {
            window.removeEventListener("keydown", handleKeyDown);
        };
    }, []);

    return [opened, setOpened] as const;
};
