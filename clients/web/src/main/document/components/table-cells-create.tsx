/**
 * <AUTHOR>
 * @namespace Document_Components
 * @description Documents Table Cells Create
 */

import { TransferPropertyDraft, getDefaultTransferPropertyVariant } from "@imbricate-hummingbird/transfer-core";
import { TableCell } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaProperty, ImbricateDatabaseSchemaPropertyOptionsBinary, ImbricateDatabaseSchemaPropertyOptionsLabel, ImbricateDatabaseSchemaPropertyOptionsReference, ImbricatePropertyValueObject, getImbricateDefaultValueOfProperty } from "@imbricate/core";
import { JSX } from "react";
import { DocumentEditingController } from "../controller/editing-controller";
import { DocumentsTableCreatingExtraCell } from "./extra-cell/creating-extra";
import { DocumentTableBinaryCell } from "./table-cells/binary-cell";
import { DocumentTableBooleanCell } from "./table-cells/boolean-cell";
import { DocumentTableDateCell } from "./table-cells/date-cell";
import { DocumentTableImbriscriptCell } from "./table-cells/imbriscript-cell";
import { DocumentTableJsonCell } from "./table-cells/json-cell";
import { DocumentTableLabelCell } from "./table-cells/label-cell";
import { DocumentTableMarkdownCell } from "./table-cells/markdown-cell";
import { DocumentTableNumberCell } from "./table-cells/number-cell";
import { DocumentTableReferenceCell } from "./table-cells/reference-cell";
import { DocumentTableStringCell } from "./table-cells/string-cell";

export type DocumentsTableCellsCreateProps = {

    readonly originUniqueIdentifier: string;

    readonly propertyIdentifiers: string[];
    readonly propertyTypesMap: Record<string, IMBRICATE_PROPERTY_TYPE>;
    readonly schemaMap: Record<string, ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>>;
    readonly creatingDocumentKey: string;
    readonly creatingDocumentPropertyDrafts: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>[];
    readonly editingController: DocumentEditingController;
};

export const createDocumentsTableCellsCreate = (
    props: DocumentsTableCellsCreateProps,
): JSX.Element[] => {

    return [
        ...props.propertyIdentifiers.map((
            propertyIdentifier: string,
        ) => {

            const propertyDraft = props.creatingDocumentPropertyDrafts.find((
                draft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>,
            ) => {
                return draft.key === propertyIdentifier;
            });

            const propertyType: IMBRICATE_PROPERTY_TYPE = propertyDraft
                ? propertyDraft.type
                : props.propertyTypesMap[propertyIdentifier];

            const getEditingProperty = <T extends IMBRICATE_PROPERTY_TYPE>(): TransferPropertyDraft<T> => {

                const updatedProperties = props.editingController.getCreatingDocument(props.creatingDocumentKey);

                if (!updatedProperties) {
                    throw new Error("[Imbricate] Updated property not found");
                }

                const targetDraft: TransferPropertyDraft<T> | undefined = updatedProperties.find((
                    draft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>,
                ) => {
                    return draft.key === propertyIdentifier;
                }) as TransferPropertyDraft<T> | undefined;

                const editingDraft: TransferPropertyDraft<T> =
                    targetDraft
                    ?? {
                        key: propertyIdentifier,
                        type: propertyType as T,
                        value: getImbricateDefaultValueOfProperty(propertyType) as ImbricatePropertyValueObject<T>,
                        variant: getDefaultTransferPropertyVariant(propertyType),
                    };

                return editingDraft;
            };

            const updateEditingProperty = (
                value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE>,
            ) => {

                props.editingController.updateCreatingDocument(
                    props.creatingDocumentKey,
                    propertyIdentifier,
                    value,
                );
            };

            // IMBRICATE_PROPERTY_TYPE SWITCH
            switch (propertyType) {

                case IMBRICATE_PROPERTY_TYPE.BINARY: {

                    const options: ImbricateDatabaseSchemaPropertyOptionsBinary =
                        props.schemaMap[propertyIdentifier].propertyOptions as ImbricateDatabaseSchemaPropertyOptionsBinary;

                    return (<TableCell
                        key={propertyIdentifier}
                    >
                        <DocumentTableBinaryCell
                            originUniqueIdentifier={props.originUniqueIdentifier}
                            propertyKey={propertyIdentifier}
                            property={propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY>}
                            getEditingProperty={getEditingProperty<IMBRICATE_PROPERTY_TYPE.BINARY>}
                            updateEditingProperty={updateEditingProperty}
                            editing
                            options={options}
                        />
                    </TableCell>);
                }
                case IMBRICATE_PROPERTY_TYPE.BOOLEAN: {

                    return (<TableCell
                        key={propertyIdentifier}
                    >
                        <DocumentTableBooleanCell
                            propertyKey={propertyIdentifier}
                            property={propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BOOLEAN>}
                            getEditingProperty={getEditingProperty<IMBRICATE_PROPERTY_TYPE.BOOLEAN>}
                            updateEditingProperty={updateEditingProperty}
                            editing
                        />
                    </TableCell>);
                }
                case IMBRICATE_PROPERTY_TYPE.STRING: {

                    return (<TableCell
                        key={propertyIdentifier}
                    >
                        <DocumentTableStringCell
                            propertyKey={propertyIdentifier}
                            property={propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.STRING>}
                            getEditingProperty={getEditingProperty<IMBRICATE_PROPERTY_TYPE.STRING>}
                            updateEditingProperty={updateEditingProperty}
                            editing
                        />
                    </TableCell>);
                }
                case IMBRICATE_PROPERTY_TYPE.NUMBER: {

                    return (<TableCell
                        key={propertyIdentifier}
                    >
                        <DocumentTableNumberCell
                            propertyKey={propertyIdentifier}
                            property={propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.NUMBER>}
                            getEditingProperty={getEditingProperty<IMBRICATE_PROPERTY_TYPE.NUMBER>}
                            updateEditingProperty={updateEditingProperty}
                            editing
                        />
                    </TableCell>);
                }
                case IMBRICATE_PROPERTY_TYPE.DATE: {

                    return (<TableCell
                        key={propertyIdentifier}
                    >
                        <DocumentTableDateCell
                            propertyKey={propertyIdentifier}
                            property={propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.DATE>}
                            getEditingProperty={getEditingProperty<IMBRICATE_PROPERTY_TYPE.DATE>}
                            updateEditingProperty={updateEditingProperty}
                            editing
                        />
                    </TableCell>);
                }
                case IMBRICATE_PROPERTY_TYPE.MARKDOWN: {

                    return (<TableCell
                        key={propertyIdentifier}
                    >
                        <DocumentTableMarkdownCell
                            originUniqueIdentifier={null}
                            databaseUniqueIdentifier={null}
                            documentUniqueIdentifier={null}
                            propertyKey={propertyIdentifier}
                            property={propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.MARKDOWN>}
                            getEditingProperty={getEditingProperty<IMBRICATE_PROPERTY_TYPE.MARKDOWN>}
                            updateEditingProperty={updateEditingProperty}
                            editing
                        />
                    </TableCell>);
                }
                case IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT: {

                    return (<TableCell
                        key={propertyIdentifier}
                    >
                        <DocumentTableImbriscriptCell
                            originUniqueIdentifier={null}
                            databaseUniqueIdentifier={null}
                            documentUniqueIdentifier={null}
                            propertyKey={propertyIdentifier}
                            property={propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT>}
                            getEditingProperty={getEditingProperty<IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT>}
                            updateEditingProperty={updateEditingProperty}
                            editing
                        />
                    </TableCell>);
                }
                case IMBRICATE_PROPERTY_TYPE.JSON: {

                    return (<TableCell
                        key={propertyIdentifier}
                    >
                        <DocumentTableJsonCell
                            originUniqueIdentifier={null}
                            databaseUniqueIdentifier={null}
                            documentUniqueIdentifier={null}
                            propertyKey={propertyIdentifier}
                            property={propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.JSON>}
                            getEditingProperty={getEditingProperty<IMBRICATE_PROPERTY_TYPE.JSON>}
                            updateEditingProperty={updateEditingProperty}
                            editing
                        />
                    </TableCell>);
                }
                case IMBRICATE_PROPERTY_TYPE.LABEL: {

                    const options: ImbricateDatabaseSchemaPropertyOptionsLabel =
                        props.schemaMap[propertyIdentifier].propertyOptions as ImbricateDatabaseSchemaPropertyOptionsLabel;

                    return (<TableCell
                        key={propertyIdentifier}
                    >
                        <DocumentTableLabelCell
                            propertyKey={propertyIdentifier}
                            property={propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.LABEL>}
                            getEditingProperty={getEditingProperty<IMBRICATE_PROPERTY_TYPE.LABEL>}
                            updateEditingProperty={updateEditingProperty}
                            editing

                            options={options}
                        />
                    </TableCell>);
                }
                case IMBRICATE_PROPERTY_TYPE.REFERENCE: {

                    const options: ImbricateDatabaseSchemaPropertyOptionsReference =
                        props.schemaMap[propertyIdentifier].propertyOptions as ImbricateDatabaseSchemaPropertyOptionsReference;

                    return (<TableCell
                        key={propertyIdentifier}
                    >
                        <DocumentTableReferenceCell
                            propertyKey={propertyIdentifier}
                            property={propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.REFERENCE>}
                            getEditingProperty={getEditingProperty<IMBRICATE_PROPERTY_TYPE.REFERENCE>}
                            updateEditingProperty={updateEditingProperty}
                            editing

                            options={options}
                        />
                    </TableCell>);
                }
                default: {

                    return (<TableCell
                        key={propertyIdentifier}
                    >
                        <DocumentTableStringCell
                            propertyKey={propertyIdentifier}
                            property={propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.STRING>}
                            getEditingProperty={getEditingProperty<IMBRICATE_PROPERTY_TYPE.STRING>}
                            updateEditingProperty={updateEditingProperty}
                            editing
                        />
                    </TableCell>);
                }
            }
        }),
        <TableCell
            key="$extra"
        >
            <DocumentsTableCreatingExtraCell
                creatingKey={props.creatingDocumentKey}
                editingController={props.editingController}
            />
        </TableCell>,
    ];
};
