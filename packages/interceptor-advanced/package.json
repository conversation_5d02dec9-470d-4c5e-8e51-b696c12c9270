{"name": "@imbricate-hummingbird/interceptor-advanced", "description": "Advanced implementation for Interceptor", "private": true, "main": "src/index.ts", "scripts": {"test": "jest", "coverage": "jest --coverage", "compile": "tsc --project ./typescript/tsconfig.compile.json", "lint": "eslint src"}, "dependencies": {}, "devDependencies": {"@imbricate-hummingbird/config": "workspace:*", "eslint": "catalog:"}}