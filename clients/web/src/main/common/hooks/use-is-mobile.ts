/**
 * <AUTHOR>
 * @namespace Common_Hooks
 * @description Use Is Mobile
 */

import { ApplicationFlagController, HUMMINGBIRD_APPLICATION_FLAG_ITEM } from "@imbricate-hummingbird/configuration";

const checkIsMobile = (): boolean => {

    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
    return isMobile;
};

export const useIsMobile = (): boolean => {

    return checkIsMobile();
};

export const useIsNotSkippedMobileWarning = (): boolean => {

    if (!checkIsMobile()) {
        return false;
    }

    const notified: boolean = ApplicationFlagController.getInstance()
        .getApplicationFlag(HUMMINGBIRD_APPLICATION_FLAG_ITEM.MOBILE_WARNING_SKIPPED);

    return !notified;
};

export const setSkippedMobileWarning = (): void => {

    ApplicationFlagController.getInstance()
        .setApplicationFlag(HUMMINGBIRD_APPLICATION_FLAG_ITEM.MOBILE_WARNING_SKIPPED, true);

    window.location.reload();
};
