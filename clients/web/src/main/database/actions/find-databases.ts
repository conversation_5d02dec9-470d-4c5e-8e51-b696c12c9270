/**
 * <AUTHOR>
 * @namespace Database_Actions
 * @description Find Databases
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createFindDatabasesAction = (
    originUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Find Databases",
        actionDescription: `Find all databases for origin: [${originUniqueIdentifier}]`,
        executerMetadata: metadata,
    };
};
