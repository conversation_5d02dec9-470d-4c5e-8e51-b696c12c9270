/**
 * <AUTHOR>
 * @namespace Common_Components_HeaderGroup_OriginData
 * @description Origin Snippets
 */

import { COMMON_PROFILE } from "@/common/internationalization/profile";
import { FormattedText } from "@imbricate-hummingbird/react-components";
import { UIColor } from "@imbricate-hummingbird/ui";
import { SudooFormat } from "@sudoo/internationalization";
import React from "react";
import { StyledSnippet } from "../../snippet/styled-snippet";

export type OriginDataSnippetPropsItem = {

    readonly content: string;
    readonly color?: UIColor;
};

export type OriginDataSnippetProps = {

    readonly commonFormat: SudooFormat<COMMON_PROFILE>;
    readonly combinations: Partial<Record<COMMON_PROFILE, OriginDataSnippetPropsItem>>;
};

export const OriginDataSnippet: React.FC<OriginDataSnippetProps> = (
    props: OriginDataSnippetProps,
) => {

    const itemKeys: COMMON_PROFILE[] =
        Object.keys(props.combinations) as COMMON_PROFILE[];

    return (<div
        className="grid grid-cols-[auto_1fr] gap-2 items-center"
    >
        {(itemKeys.map((
            itemKey: COMMON_PROFILE,
        ) => {

            const itemValue =
                props.combinations[itemKey] as OriginDataSnippetPropsItem;

            return (<React.Fragment
                key={itemKey}
            >
                <div
                    className="text-end"
                >
                    <FormattedText
                        usedFormats={[props.commonFormat]}
                    >
                        {props.commonFormat.get(itemKey)}
                    </FormattedText>
                </div>
                <StyledSnippet
                    size="sm"
                    color={itemValue.color}
                    variant="solid"
                    className="max-w-96 min-w-48"
                >
                    {itemValue.content}
                </StyledSnippet>
            </React.Fragment>);
        }))}
    </div>);
};
