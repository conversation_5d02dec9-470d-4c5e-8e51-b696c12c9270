/**
 * <AUTHOR>
 * @package UI
 * @namespace Badge
 * @description Badge
 */

import { Badge } from "@heroui/badge";
import React from "react";
import { UIColor, UISize } from "../common/types";
import { UIBadgePlacement, UIBadgeShape } from "./types";

export type UIBadgeProps = {

    readonly className?: string;

    readonly placement?: UIBadgePlacement;

    readonly color?: UIColor;
    readonly size?: UISize;
    readonly shape?: UIBadgeShape;

    readonly children?: React.ReactNode;
    readonly content?: React.ReactNode;
};

export const UIBadge: React.FC<UIBadgeProps> = (
    props: UIBadgeProps,
) => {

    return (<Badge
        className={props.className}

        placement={props.placement}

        color={props.color}
        size={props.size}
        shape={props.shape}

        content={props.content}
    >
        {props.children}
    </Badge>);
};
