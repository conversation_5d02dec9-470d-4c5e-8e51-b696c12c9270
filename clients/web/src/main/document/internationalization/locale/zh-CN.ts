/**
 * <AUTHOR>
 * @namespace Document_Internationalization_Locale
 * @description Zh-CN
 */

import { DOCUMENT_PROFILE } from "../profile";

export const zhCNDocumentProfile: Record<DOCUMENT_PROFILE, string> = {

    [DOCUMENT_PROFILE.ARRANGE_DOCUMENT]: "整理文档",
    [DOCUMENT_PROFILE.ARRANGE_DOCUMENT_DESCRIPTION]: "将当前文档移动到其他插槽，数据库或数据源",
    [DOCUMENT_PROFILE.$1_BINARIES]: "{} 个二进制文件",
    [DOCUMENT_PROFILE.CREATE_MARKDOWN_DOCUMENT]: "创建 Markdown 文档",
    [DOCUMENT_PROFILE.DANGER_ZONE]: "危险区域",
    [DOCUMENT_PROFILE.DELETE_DOCUMENT]: "删除文档",
    [DOCUMENT_PROFILE.DELETE_DOCUMENT_DESCRIPTION]: "永久删除文档",
    [DOCUMENT_PROFILE.DATABASE]: "数据库",
    [DOCUMENT_PROFILE.DOCUMENT]: "文档",
    [DOCUMENT_PROFILE.DOCUMENT_DELETE_SUCCESS]: "文档已删除",
    [DOCUMENT_PROFILE.DOCUMENT_DELETE_SUCCESS_DESCRIPTION]: "文档删除成功",
    [DOCUMENT_PROFILE.DOCUMENT_DETAILS]: "文档详情",
    [DOCUMENT_PROFILE.EDIT_HISTORY]: "编辑历史",
    [DOCUMENT_PROFILE.EDIT_MARKDOWN_DOCUMENT]: "编辑 Markdown 文档",
    [DOCUMENT_PROFILE.INVALID_DATE]: "无效的日期",
    [DOCUMENT_PROFILE.LINK_DOCUMENT]: "链接文档",
    [DOCUMENT_PROFILE.NO_BINARY]: "没有二进制文件",
    [DOCUMENT_PROFILE.NO_DATA]: "没有数据",
    [DOCUMENT_PROFILE.NO_REFERENCE]: "没有引用",
    [DOCUMENT_PROFILE.ONE_BINARY]: "一个二进制文件",
    [DOCUMENT_PROFILE.ONE_REFERENCE]: "一个引用",
    [DOCUMENT_PROFILE.PRIMARY]: "主键",
    [DOCUMENT_PROFILE.PRIMARY_KEY]: "主键",
    [DOCUMENT_PROFILE.PRIMARY_PROPERTY_IDENTIFIER]: "主键的属性标识",
    [DOCUMENT_PROFILE.PROPERTY_IDENTIFIER]: "属性标识",
    [DOCUMENT_PROFILE.$1_REFERENCES]: "{} 个引用",
    [DOCUMENT_PROFILE.SAVE_CHANGES]: "保存更改",
    [DOCUMENT_PROFILE.UNABLE_TO_LOAD_DOCUMENT]: "无法加载文档",
    [DOCUMENT_PROFILE.UPLOAD_BINARY]: "上传二进制文件",
    [DOCUMENT_PROFILE.VIEW_EDIT_HISTORY]: "查看编辑历史",
    [DOCUMENT_PROFILE.VIEW_MARKDOWN_DOCUMENT]: "查看 Markdown 文档",
};
