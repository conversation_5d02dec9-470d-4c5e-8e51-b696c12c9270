/**
 * <AUTHOR>
 * @namespace Navigation_Internationalization
 * @description Hook
 */

import { defaultEmptyFormat, useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { useLayoutEffect, useState } from "react";
import { navigationInternationalization } from "./intl";
import { NAVIGATION_PROFILE } from "./profile";

export const useNavigationFormat = (): SudooFormat<NAVIGATION_PROFILE> => {

    const locale = useLocale();

    const [format, setFormat] = useState<SudooFormat<NAVIGATION_PROFILE>>(defaultEmptyFormat);

    useLayoutEffect(() => {

        const execute = async () => {

            const format = await navigationInternationalization.format(locale);
            setFormat(format);
        };

        execute();
    }, [locale]);

    return format;
};
