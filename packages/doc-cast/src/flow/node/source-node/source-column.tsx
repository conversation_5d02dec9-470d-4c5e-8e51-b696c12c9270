/**
 * <AUTHOR>
 * @package Doc Cast
 * @namespace Flow_Node_SourceNode
 * @description Static Input Item
 */

import { FlowNoDragDiv } from "@imbricate-hummingbird/flow-common";
import { Handle, Position } from "@xyflow/react";

export type DocCastSourceNodeColumnProps = {
};

export const DocCastSourceNodeColumn = (
    _props: DocCastSourceNodeColumnProps,
) => {

    return (<FlowNoDragDiv
        className="relative"
    >
        <div>
            Hello World
        </div>
        <Handle
            type="source"
            position={Position.Right}
            style={{ width: 10, height: 10 }}
            isConnectable
        />
    </FlowNoDragDiv>);
};
