/**
 * <AUTHOR>
 * @namespace Debug_EnvironmentVariables
 * @description Environment Variables Quick Access
 */

import { StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody } from "@imbricate-hummingbird/ui";
import React from "react";

export const EnvironmentVariablesQuickAccess: React.FC = () => {

    return (<StyledCard>
        <UICardBody>
            <div
                className="flex gap-2 font-mono"
            >
                <div
                    className="text-tiny text-foreground-500"
                >
                    NODE_ENV:
                </div>
                <div
                    className="text-tiny"
                >
                    {process.env.NODE_ENV}
                </div>
            </div>
        </UICardBody>
    </StyledCard>);
};
