/**
 * <AUTHOR>
 * @namespace Lens_Hooks
 * @description Use Throttle Reload
 */

export type UseThrottleReloadResponse = {

    readonly remainingTime: number;
};

export const useThrottleReload = (
    callback: () => void,
    delay: number,
): () => void => {

    let timeout: NodeJS.Timeout | null = null;

    return () => {

        if (timeout) {
            clearTimeout(timeout);
        }

        timeout = setTimeout(() => {
            callback();
        }, delay);
    };
};
