/**
 * <AUTHOR>
 * @namespace Lens_Components_Script
 * @description Error Verbose
 */

import { UIAlert } from "@imbricate-hummingbird/ui";
import { END_SIGNAL, MarkedResult } from "@sudoo/marked";
import { FC } from "react";

const getTitle = (signal: END_SIGNAL): string => {

    switch (signal) {
        case END_SIGNAL.ABORTED: return "ImbriScript execution aborted";
        case END_SIGNAL.EXCEPTION: return "ImbriScript execution exception";
        case END_SIGNAL.FAILED: return "ImbriScript execution failed";
    }

    return "Unknown";
};

const getDescription = (result: MarkedResult): string => {

    switch (result.signal) {
        case END_SIGNAL.ABORTED: return result.error.message;
    }

    return "Unknown";
};

const getBody = (result: MarkedResult): string => {

    switch (result.signal) {
        case END_SIGNAL.ABORTED: return result.error.info ?? "No additional information";
    }

    return "Unknown";
};

export type ScriptErrorVerboseProps = {

    readonly result: MarkedResult;
};

export const ScriptErrorVerbose: FC<ScriptErrorVerboseProps> = (
    props: ScriptErrorVerboseProps,
) => {

    return (<div
        className="p-2 flex flex-col gap-2"
    >
        <UIAlert
            title={getTitle(props.result.signal)}
            description={getDescription(props.result)}
            color="warning"
            variant="faded"
        />
        <div>
            {getBody(props.result)}
        </div>
    </div>);
};
