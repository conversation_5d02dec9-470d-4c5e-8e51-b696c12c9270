/**
 * <AUTHOR>
 * @namespace Database_Internationalization
 * @description Profile
 */

export enum DATABASE_PROFILE {

    ACTIONS = "ACTIONS",
    ADD_PROPERTY = "ADD_PROPERTY",
    ALL = "ALL",
    ALLOW_MULTIPLE = "ALLOW_MULTIPLE",
    BINARY = "BINARY",
    BOOLEAN = "BOOLEAN",
    CREATE_DOCUMENT = "CREATE_DOCUMENT",
    CREATE_DOCUMENT_DESCRIPTION = "CREATE_DOCUMENT_DESCRIPTION",
    DATABASE = "DATABASE",
    DATABASE_SCHEMA = "DATABASE_SCHEMA",
    DATE = "DATE",
    DISPLAY_ALL = "DISPLAY_ALL",
    DISPLAY_ALL_DESCRIPTION = "DISPLAY_ALL_DESCRIPTION",
    DOCUMENT = "DOCUMENT",
    DOCUMENT_LIST = "DOCUMENT_LIST",
    EDIT_DATABASE_SCHEMA = "EDIT_DATABASE_SCHEMA",
    EDIT_DATABASE_SCHEMA_DESCRIPTION = "EDIT_DATABASE_SCHEMA_DESCRIPTION",
    FLOATING_PROPERTIES = "FLOATING_PROPERTIES",
    FLOATING_PROPERTY_DESCRIPTION = "FLOATING_PROPERTY_DESCRIPTION",
    JSON = "JSON",
    IMBRISCRIPT = "IMBRISCRIPT",
    ITEM_$1_PER_PAGE = "ITEM_$1_PER_PAGE",
    ITEM_PER_PAGE_FOR_THIS_DATABASE = "ITEM_PER_PAGE_FOR_THIS_DATABASE",
    LABEL = "LABEL",
    LABEL_COLOR = "LABEL_COLOR",
    LABEL_NAME = "LABEL_NAME",
    MARKDOWN = "MARKDOWN",
    NUMBER = "NUMBER",
    REFRESH_DOCUMENT_LIST = "REFRESH_DOCUMENT_LIST",
    REFRESH_DOCUMENT_LIST_DESCRIPTION = "REFRESH_DOCUMENT_LIST_DESCRIPTION",
    PRIMARY_KEY = "PRIMARY_KEY",
    PROPERTIES_FOR_THIS_DATABASE = "PROPERTIES_FOR_THIS_DATABASE",
    PROPERTY_NAME = "PROPERTY_NAME",
    PROPERTY_TYPE = "PROPERTY_TYPE",
    PROPERTY_UNIQUE_IDENTIFIER = "PROPERTY_UNIQUE_IDENTIFIER",
    REFERENCE = "REFERENCE",
    SAVE = "SAVE",
    SELECT_DATABASE = "SELECT_DATABASE",
    STRING = "STRING",
    VIEW_DOCUMENTS = "VIEW_DOCUMENTS",
    VIEW_DOCUMENTS_DESCRIPTION = "VIEW_DOCUMENTS_DESCRIPTION",
}
