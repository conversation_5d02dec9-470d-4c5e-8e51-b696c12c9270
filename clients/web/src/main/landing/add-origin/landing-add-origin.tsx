/**
 * <AUTHOR>
 * @namespace Landing_AddOrigin
 * @description Landing Add Origin View
 */

import { useTitle } from "@imbricate-hummingbird/react-common";
import { FC } from "react";
import { NewOriginCard } from "../../origin/components/origin-new/origin-new-card";
import { LandingMainWrapper } from "../components/landing-wrapper";

export type LandingAddOriginViewProps = {
};

// LAZY LOAD ONLY
const LandingAddOriginView: FC<LandingAddOriginViewProps> = (
    _props: LandingAddOriginViewProps,
) => {

    useTitle([
        "Add Origin",
    ], []);

    return (<LandingMainWrapper >
        <div
            className="flex flex-col gap-2 w-full p-3"
        >
            <NewOriginCard />
        </div>
    </LandingMainWrapper>);
};
export default LandingAddOriginView;
