/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyValue_Reference
 * @description Reference Popover
 */

import { DocumentSelector, DocumentSelectorResponse } from "@imbricate-hummingbird/react-components";
import { TransferDocument, TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIButton, UIPopover, UIRadius, UISize, createUIButton } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaPropertyOptionsReference, ImbricatePropertyValueObjectReference } from "@imbricate/core";
import React, { FC } from "react";
import { FaCheck, FaLink, FaPlus } from "react-icons/fa";
import { useDocumentFormat } from "../../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../../internationalization/profile";

export type DocumentReferenceValueReferencePopoverProps = {

    readonly size?: UISize;
    readonly radius?: UIRadius;
    readonly iconOnly?: boolean;

    readonly propertyKey: string;
    readonly currentProperty: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.REFERENCE>;
    readonly updateProperty: (
        value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.REFERENCE>,
    ) => void;

    readonly options: ImbricateDatabaseSchemaPropertyOptionsReference;
};

export const DocumentReferenceValueReferencePopover: FC<DocumentReferenceValueReferencePopoverProps> = (
    props: DocumentReferenceValueReferencePopoverProps,
) => {

    const documentFormat = useDocumentFormat();

    const [toBeLinked, setToBeLinked] = React.useState<DocumentSelectorResponse | null>(null);
    const [isOpen, setIsOpen] = React.useState(false);

    const allowedDatabase: string[] = Array.isArray(props.options.databases)
        ? props.options.databases.map((each) => each.databaseUniqueIdentifier)
        : [];

    const isSingleReference = !props.options.allowMultiple;
    const shouldDisableLink = isSingleReference && props.currentProperty.value.length > 0;

    return (<UIPopover
        backdrop="blur"
        isOpen={isOpen}
        onOpenChange={(open) => {
            setIsOpen(open);
        }}
        trigger={createUIButton({
            color: "primary",
            variant: "flat",
            isDisabled: shouldDisableLink,
            radius: props.radius,
            size: props.size,
            isIconOnly: props.iconOnly,
            children: (<React.Fragment>
                {isSingleReference
                    ? <FaLink
                        className={props.iconOnly ? "text-large" : undefined}
                    />
                    : <FaPlus
                        className={props.iconOnly ? "text-large" : undefined}
                    />}
                {props.iconOnly
                    ? ""
                    : documentFormat.get(
                        DOCUMENT_PROFILE.LINK_DOCUMENT,
                    )}
            </React.Fragment>),
        })}
    >
        <DocumentSelector
            allowedDatabases={allowedDatabase}
            filterDocument={(
                document: TransferDocument,
            ) => {
                return !props.currentProperty.value.some((each: ImbricatePropertyValueObjectReference) => {
                    return each.documentUniqueIdentifier === document.documentUniqueIdentifier;
                });
            }}
            onSelectCancel={() => {
                setToBeLinked(null);
            }}
            onSelectConfirm={(response) => {
                setToBeLinked(response);
            }}
        />
        {toBeLinked && <UIButton
            color="success"
            variant="flat"
            onPress={() => {

                if (!toBeLinked) {
                    return;
                }
                setIsOpen(false);

                const newValue: ImbricatePropertyValueObjectReference[] = [
                    ...props.currentProperty.value,
                    {
                        originUniqueIdentifier: toBeLinked.selectedOriginUniqueIdentifier,
                        databaseUniqueIdentifier: toBeLinked.selectedDatabase.databaseUniqueIdentifier,
                        documentUniqueIdentifier: toBeLinked.selectedDocument.documentUniqueIdentifier,
                    },
                ];

                props.updateProperty({
                    key: props.propertyKey,
                    type: IMBRICATE_PROPERTY_TYPE.REFERENCE,
                    value: newValue,
                    variant: props.currentProperty.variant,
                });

                setToBeLinked(null);
            }}
        >
            <FaCheck /> {documentFormat.get(DOCUMENT_PROFILE.LINK_DOCUMENT)}
        </UIButton>}
    </UIPopover>);
};
