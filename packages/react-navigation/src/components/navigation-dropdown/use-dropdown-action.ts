/**
 * <AUTHOR>
 * @namespace Navigation_Components_NavigationDropdown
 * @description Dropdown Action
 */

import { ActionCentral } from "@imbricate-hummingbird/interceptor-core";
import { CentralGetDocumentResponse } from "@imbricate-hummingbird/origin-central";
import { OriginWorkerDataCentral } from "@imbricate-hummingbird/origin-central-web-worker";
import { createGetRandomDocumentAction } from "../../actions/get-random-document";
import { useNavigateDocumentView, useNavigateOriginsView, useNavigateStaticsView } from "../../hooks/use-routes";
import { NAVIGATION_DROPDOWN_KEY } from "./dropdown-key";

export type UseExecuteNavigationBottomButtonDropdownActionExecuter = (
    key: NAVIGATION_DROPDOWN_KEY,
) => Promise<void>;

export const useExecuteNavigationBottomButtonDropdownAction = (
): UseExecuteNavigationBottomButtonDropdownActionExecuter => {

    const navigateToOrigins = useNavigateOriginsView();
    const navigateToStatics = useNavigateStaticsView();
    const navigateToDocument = useNavigateDocumentView();

    return async (
        key: NAVIGATION_DROPDOWN_KEY,
    ): Promise<void> => {

        switch (key) {
            case NAVIGATION_DROPDOWN_KEY.SURPRISE_ME: {

                const document: CentralGetDocumentResponse = await ActionCentral.getInstance()
                    .executeAction(
                        async (
                            actionIdentifier,
                            recordIdentifier,
                        ) => {

                            const document = await OriginWorkerDataCentral.getInstance()
                                .getRandomDocument(
                                    actionIdentifier,
                                    recordIdentifier,
                                );

                            return document;
                        },
                        createGetRandomDocumentAction({
                            executer: import.meta.url,
                        }),
                    );

                navigateToDocument(
                    document.origin.originUniqueIdentifier,
                    document.database.databaseUniqueIdentifier,
                    document.document.documentUniqueIdentifier,
                );
                break;
            }
            case NAVIGATION_DROPDOWN_KEY.MANAGE_ORIGIN: {
                navigateToOrigins();
                break;
            }
            case NAVIGATION_DROPDOWN_KEY.STATIC_DATA: {
                navigateToStatics();
                break;
            }
            default:
                break;
        }
    };
};
