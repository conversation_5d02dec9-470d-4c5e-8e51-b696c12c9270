/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyValue
 * @description String Value
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { UIInput } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaProperty } from "@imbricate/core";
import { FC } from "react";
import { FaStar } from "react-icons/fa";

export type DocumentStringValueProps = {

    readonly showPropertyName?: boolean;

    readonly propertySchema: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE.STRING>;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.STRING>;
    readonly updateProperty: (value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.STRING>) => void;
};

export const DocumentStringValue: FC<DocumentStringValueProps> = (
    props: DocumentStringValueProps,
) => {

    const propertyName: string = props.propertySchema.propertyName;
    const label = props.showPropertyName
        ? (<div
            className="flex gap-1 items-center"
        >
            {props.propertySchema.isPrimaryKey && <FaStar />}
            {propertyName}
        </div>)
        : undefined;

    return (<UIInput
        label={label}
        isFullWidth
        value={props.property.value}
        onValueChange={(
            newValue: string,
        ) => {

            props.updateProperty({
                key: props.propertySchema.propertyIdentifier,
                type: IMBRICATE_PROPERTY_TYPE.STRING,
                value: newValue,
                variant: props.property.variant,
            });
        }}
    />);
};
