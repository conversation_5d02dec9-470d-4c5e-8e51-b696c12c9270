/**
 * <AUTHOR>
 * @package React Origin Central
 * @namespace Actions
 * @description Database Recursive
 */

import { ActionCentral, ActionDescription } from "@imbricate-hummingbird/interceptor-core";
import { CentralGetDatabasesResponse, IOriginCentral } from "@imbricate-hummingbird/origin-central";

export const executeDatabasesRecursive = async (
    originCentral: IOriginCentral,
    actionDescription: ActionDescription,
): Promise<CentralGetDatabasesResponse> => {

    const databases: CentralGetDatabasesResponse =
        await ActionCentral.getInstance().executeAction(
            async (
                actionIdentifier: string,
                recordIdentifier: string,
            ) => {
                return originCentral.getDatabasesRecursive(
                    actionIdentifier,
                    recordIdentifier,
                );
            },
            actionDescription,
        );

    return databases;
};
