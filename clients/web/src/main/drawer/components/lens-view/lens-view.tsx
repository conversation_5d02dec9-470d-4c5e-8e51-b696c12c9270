/**
 * <AUTHOR>
 * @namespace Drawer_Components
 * @description Lens View
 */

import { useLensViewMagicButton } from "@/main/lens/hooks/lens-view/use-lens-view-magic-button";
import { LENS_CONFIG_SOURCE, LensConfigItem } from "@imbricate-hummingbird/configuration";
import { UIDrawerBody } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { useVersion } from "../../../common/hooks/use-version";
import { LensRender } from "../../../lens/components/lens-render";

export type DrawerLensViewProps = {

    readonly targetLens: LensConfigItem<LENS_CONFIG_SOURCE>;
};

export const DrawerLensView: FC<DrawerLensViewProps> = (
    props: DrawerLensViewProps,
) => {

    const [version, updateVersion] = useVersion();

    const magicButtonResult = useLensViewMagicButton({
        targetLens: props.targetLens,
        updateVersion: updateVersion,
    });

    return (<UIDrawerBody>
        <LensRender
            triggerThrottleReload={() => { }}
            version={version}
            lensItem={props.targetLens}
            magicButtonResult={magicButtonResult}
        />
    </UIDrawerBody>);
};
