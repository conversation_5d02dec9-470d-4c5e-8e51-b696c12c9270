/**
 * <AUTHOR>
 * @namespace Lens_Internationalization_Locale
 * @description En-US
 */

import { LENS_PROFILE } from "../profile";

export const enUSLensProfile: Record<LENS_PROFILE, string> = {

    [LENS_PROFILE.COMPLETED_AT]: "Completed at",
    [LENS_PROFILE.CREATE_LENS]: "Create Lens",
    [LENS_PROFILE.CREATE_NEW_LENS]: "Create New Lens",
    [LENS_PROFILE.DANGER_ZONE]: "Danger Zone",
    [LENS_PROFILE.DELETE_LENS]: "Delete Lens",
    [LENS_PROFILE.DELETE_LENS_DESCRIPTION]: "Delete the current lens",
    [LENS_PROFILE.EDIT_IMBRISCRIPT]: "Edit ImbriScript",
    [LENS_PROFILE.EDIT_IMBRISCRIPT_DESCRIPTION]: "Edit the ImbriScript of the current lens",
    [LENS_PROFILE.EDIT_LENS]: "Edit Lens",
    [LENS_PROFILE.EDIT_LENS_DESCRIPTION]: "Edit the current lens configuration",
    [LENS_PROFILE.EDITING_LENS]: "Editing Lens",
    [LENS_PROFILE.HIDE_EXTRA_DETAILS]: "Hide Extra Details",
    [LENS_PROFILE.IMBRISCRIPT]: "ImbriScript",
    [LENS_PROFILE.IMBRISCRIPT_EXECUTION]: "ImbriScript Execution",
    [LENS_PROFILE.IN]: "in",
    [LENS_PROFILE.LENS]: "Lens",
    [LENS_PROFILE.LENS_IDENTIFIER]: "Lens Identifier",
    [LENS_PROFILE.LENS_SOURCE]: "Lens Source",
    [LENS_PROFILE.LENS_NAME]: "Lens Name",
    [LENS_PROFILE.LENS_TARGET]: "Lens Target",
    [LENS_PROFILE.MS]: "ms",
    [LENS_PROFILE.NAVIGATE_TO_LENS_SOURCE]: "Navigate to Lens Source",
    [LENS_PROFILE.OPEN_IMBRISCRIPT_DOCUMENT_IN_DRAWER]: "Open ImbriScript Document in Drawer",
    [LENS_PROFILE.OPEN_IMBRISCRIPT_DOCUMENT_IN_DRAWER_DESCRIPTION]: "Under sub-panel, open the ImbriScript document",
    [LENS_PROFILE.RELOAD_LENS]: "Reload Lens",
    [LENS_PROFILE.RELOAD_LENS_DESCRIPTION]: "Reload current lens to reflect the latest changes",
    [LENS_PROFILE.RENDER_LENS]: "Render Lens",
    [LENS_PROFILE.RENDER_LENS_DESCRIPTION]: "Navigate to the lens render view",
    [LENS_PROFILE.SHOW_EXTRA_DETAILS]: "Show Extra Details",
    [LENS_PROFILE.UPDATE_LENS]: "Update Lens",
    [LENS_PROFILE.VIEW_LENS]: "View Lens",
    [LENS_PROFILE.VIEW_LENS_DESCRIPTION]: "Navigate to the lens view",
};
