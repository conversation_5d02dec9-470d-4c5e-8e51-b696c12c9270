/**
 * <AUTHOR>
 * @namespace Database_Views_SchemaView
 * @description Schema View Navigator
 */

import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { IImbricateDatabase } from "@imbricate/core";
import React, { FC } from "react";
import { useParams } from "react-router-dom";
import { useWideOrigin } from "../../../origin/hooks/use-wide-origin";
import { UseDatabaseResponseSymbol, useDatabase } from "../../hooks/use-database";
import { DatabasesSchemaView } from "./schema-view";

// LAZY LOAD ONLY   
const DatabaseSchemaViewNavigator: FC = () => {

    const params = useParams();
    const databaseUniqueIdentifier: string =
        params["database-unique-identifier"] as string;

    const origin: ImbricateOriginObject | null =
        useWideOrigin(databaseUniqueIdentifier);

    const database: IImbricateDatabase | UseDatabaseResponseSymbol =
        useDatabase(databaseUniqueIdentifier);

    if (!origin || typeof database === "symbol") {
        return null;
    }

    return (<DatabasesSchemaView
        origin={origin}
        database={database}
    />);
};
export default DatabaseSchemaViewNavigator;
