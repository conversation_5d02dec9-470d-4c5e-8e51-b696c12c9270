/**
 * <AUTHOR>
 * @namespace Edit
 * @description Edit Editors
 */

import { UseMagicButtonResult } from "@/common/components/magic-button/hooks/use-magic-button";
import { useDiffStyle } from "@imbricate-hummingbird/monaco-react";
import { useTitle } from "@imbricate-hummingbird/react-common";
import { IImbricateProperty, IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { ErrorScreen } from "../common/components/error-screen";
import { UsePropertyResponse } from "../property/types/use-property";
import { ImbriScriptEditorWrapper } from "./editors/imbriscript-editor-wrapper";
import { JsonEditorWrapper } from "./editors/json-editor-wrapper";
import { MarkdownEditorWrapper } from "./editors/markdown-editor-wrapper";
import { GetValueRef } from "./types/editor-refs";

export type EditEditorsProps = {

    readonly darkMode: boolean;

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;
    readonly propertyUniqueIdentifier: string;

    readonly primaryValue: IImbricateProperty<IMBRICATE_PROPERTY_TYPE> | null;
    readonly usePropertyResponse: UsePropertyResponse;

    readonly getValueRef: GetValueRef;
    readonly onValueChange: () => void;

    readonly startLineNumber?: number;

    readonly magicButtonResult: UseMagicButtonResult;
};

export const EditEditors: FC<EditEditorsProps> = (props: EditEditorsProps) => {

    const titleValue = props.primaryValue
        ? String(props.primaryValue.propertyValue)
        : props.documentUniqueIdentifier;

    useTitle([
        titleValue,
        "Edit",
    ], []);

    useDiffStyle();

    switch (props.usePropertyResponse.schemaProperty.propertyType) {

        case IMBRICATE_PROPERTY_TYPE.MARKDOWN: {

            const textUniqueIdentifier: string | undefined =
                props.usePropertyResponse.documentProperty
                    ? props.usePropertyResponse.documentProperty.propertyValue as string
                    : undefined;

            return (<MarkdownEditorWrapper
                darkMode={props.darkMode}
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                propertyUniqueIdentifier={props.propertyUniqueIdentifier}

                primaryValue={props.primaryValue}
                getValueRef={props.getValueRef}
                onValueChange={props.onValueChange}
                textUniqueIdentifier={textUniqueIdentifier}
                startLineNumber={props.startLineNumber}
                magicButtonResult={props.magicButtonResult}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT: {

            const textUniqueIdentifier: string | undefined =
                props.usePropertyResponse.documentProperty
                    ? props.usePropertyResponse.documentProperty.propertyValue as string
                    : undefined;

            return (<ImbriScriptEditorWrapper
                darkMode={props.darkMode}
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                propertyUniqueIdentifier={props.propertyUniqueIdentifier}

                primaryValue={props.primaryValue}
                getValueRef={props.getValueRef}
                onValueChange={props.onValueChange}
                textUniqueIdentifier={textUniqueIdentifier}
                magicButtonResult={props.magicButtonResult}
            />);
        }
        case IMBRICATE_PROPERTY_TYPE.JSON: {

            const textUniqueIdentifier: string | undefined =
                props.usePropertyResponse.documentProperty
                    ? props.usePropertyResponse.documentProperty.propertyValue as string
                    : undefined;

            return (<JsonEditorWrapper
                darkMode={props.darkMode}
                originUniqueIdentifier={props.originUniqueIdentifier}
                databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                documentUniqueIdentifier={props.documentUniqueIdentifier}
                propertyUniqueIdentifier={props.propertyUniqueIdentifier}

                primaryValue={props.primaryValue}
                getValueRef={props.getValueRef}
                onValueChange={props.onValueChange}
                textUniqueIdentifier={textUniqueIdentifier}
                magicButtonResult={props.magicButtonResult}
            />);
        }
    }

    return (<div>
        <ErrorScreen
            color="warning"
            title="Property Type Not Supported for Standalone Editor"
            message="This property type is not supported for standalone editor, please use the document editor instead."
            showFooter
        />
    </div>);
};
