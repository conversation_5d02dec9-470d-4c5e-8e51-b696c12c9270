/**
 * <AUTHOR>
 * @namespace CrossSection_Node_StaticInput
 * @description Static Input Item
 */

import { FlowNoDragDiv } from "@imbricate-hummingbird/flow-common/src/components/no-drag-div";
import { UIInput } from "@imbricate-hummingbird/ui";
import { Handle, Position } from "@xyflow/react";
import { CrossSectionNodeStaticInputStaticInput } from "../../types/node/static-input";

export type StaticInputNodeItemProps = {

    readonly input: CrossSectionNodeStaticInputStaticInput;
    readonly isConnectable: boolean;
};

export const StaticInputNodeItem = (
    props: StaticInputNodeItemProps,
) => {

    return (<FlowNoDragDiv
        key={props.input.inputIdentifier}
        className="relative"
    >
        <UIInput
            size="sm"
            radius="none"
            label={props.input.name}
            defaultValue={props.input.value}
        />
        <Handle
            type="source"
            position={Position.Right}
            style={{ width: 10, height: 10 }}
            isConnectable={props.isConnectable}
        />
    </FlowNoDragDiv>);
};
