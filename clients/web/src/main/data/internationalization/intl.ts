/**
 * <AUTHOR>
 * @namespace Data_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooLazyInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { DATA_PROFILE } from "./profile";

export const dataInternationalization: SudooLazyInternationalization<DATA_PROFILE> =
    SudooLazyInternationalization.create<DATA_PROFILE>(
        DEFAULT_LOCALE,
    );

dataInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/en-US").then(
        (module) => module.enUSDataProfile,
    ),
);

dataInternationalization.mergeLazyProfile(
    IETF_LOCALE.JAPANESE_JAPAN,
    import("./locale/ja-JP").then(
        (module) => module.jaJPDataProfile,
    ),
);

dataInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    import("./locale/zh-CN").then(
        (module) => module.zhCNDataProfile,
    ),
);
