/**
 * <AUTHOR>
 * @package UI
 * @namespace Toast
 * @description Add Toast
 */

import { addToast } from "@heroui/toast";
import { UIColor } from "../common/types";

export type UIToastAddProps = {

    readonly title: string;
    readonly description?: string;
    readonly color?: UIColor;
};

export const addUIToast = (
    props: UIToastAddProps,
) => {

    addToast({
        title: props.title,
        description: props.description,
        color: props.color,
    });
};
