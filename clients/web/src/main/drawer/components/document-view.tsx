/**
 * <AUTHOR>
 * @namespace Drawer_Components
 * @description Document View
 */

import { useOldDocument } from "@/common/transfer/hooks/use-document";
import { $ERROR, HookSymbol } from "@imbricate-hummingbird/global-symbol";
import { ActionDescription } from "@imbricate-hummingbird/interceptor-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { LoadingWrapper } from "@imbricate-hummingbird/react-components";
import { TransferDocument } from "@imbricate-hummingbird/transfer-core";
import { UIDrawerBody } from "@imbricate-hummingbird/ui";
import { IImbricateDatabase } from "@imbricate/core";
import { FC } from "react";
import { ErrorScreen } from "../../common/components/error-screen";
import { UseDatabaseResponseSymbol, useDatabase } from "../../database/hooks/use-database";
import { DocumentPropertyCards } from "../../document/components/property-card/property-cards";

const createGetDocumentAction = (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
): ActionDescription => {

    return {
        actionName: "Get Document",
        actionDescription: `Get document from database ${databaseUniqueIdentifier} for document ${documentUniqueIdentifier}`,
        executerMetadata: {
            executer: import.meta.url,
        },
        actionPayload: {
            originUniqueIdentifier,
            databaseUniqueIdentifier,
            documentUniqueIdentifier,
        },
    };
};

export type DrawerDocumentViewProps = {

    readonly origin: ImbricateOriginObject;

    readonly databaseUniqueIdentifier: string;
    readonly documentUniqueIdentifier: string;
};

// LAZY LOAD ONLY
const DrawerDocumentView: FC<DrawerDocumentViewProps> = (
    props: DrawerDocumentViewProps,
) => {

    const database: IImbricateDatabase | UseDatabaseResponseSymbol =
        useDatabase(props.databaseUniqueIdentifier);

    const document: TransferDocument | HookSymbol =
        useOldDocument(
            props.origin.origin.uniqueIdentifier,
            props.databaseUniqueIdentifier,
            props.documentUniqueIdentifier,
            createGetDocumentAction(
                props.origin.origin.uniqueIdentifier,
                props.databaseUniqueIdentifier,
                props.documentUniqueIdentifier,
            ),
        );

    if (document === $ERROR) {

        return (<ErrorScreen
            color="warning"
            title="Unable to load document"
            message={<div>
                <div>Database: {props.databaseUniqueIdentifier}</div>
                <div>Document: {props.documentUniqueIdentifier}</div>
            </div>}
        />);
    }

    if (typeof database === "symbol"
        || typeof document === "symbol") {

        return (<LoadingWrapper
            debugDescription="Document View"
            fullHeight
        />);
    }

    return (<UIDrawerBody>
        <DocumentPropertyCards
            databaseUniqueIdentifier={props.databaseUniqueIdentifier}
            documentUniqueIdentifier={props.documentUniqueIdentifier}
            schema={database.schema}
            document={document}
        />
    </UIDrawerBody>);
};
export default DrawerDocumentView;
