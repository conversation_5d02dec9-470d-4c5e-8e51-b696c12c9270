/**
 * <AUTHOR>
 * @namespace Lens_Operations
 * @description Delete Lens
 */

import { HUMMINGBIRD_MANAGED_CONFIG_ITEM, LensConfig, ManagedConfigController } from "@imbricate-hummingbird/configuration";

export type DeleteLensResponse = {

    readonly updated: LensConfig;
};

export const deleteLens = (
    lensIdentifier: string,
): DeleteLensResponse => {

    const current: LensConfig = ManagedConfigController.getInstance()
        .getManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.LENS_CONFIG);

    const updated: LensConfig = {
        items: current.items.filter((item) => item.lensIdentifier !== lensIdentifier),
    };

    ManagedConfigController.getInstance()
        .setManagedConfig(HUMMINGBIRD_MANAGED_CONFIG_ITEM.LENS_CONFIG, updated);

    return {
        updated,
    };
};
