/**
 * <AUTHOR>
 * @namespace Database_Views_RawDatabase
 * @description Raw Database
 */

import { useTitleWithAsyncFormat } from "@imbricate-hummingbird/internationalization";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { IImbricateDatabase } from "@imbricate/core";
import React, { FC } from "react";
import { DocumentEditingController } from "../../../document/controller/editing-controller";
import { DatabaseHeader } from "../../components/database-header";
import { DatabaseDatabaseSet } from "../../components/table/database-set";
import { useDisplayedProperties } from "../../hooks/use-displayed-properties";
import { useItemPerPage } from "../../hooks/use-item-per-page";
import { useRawDatabaseViewMagicButton } from "../../hooks/use-raw-database-view-magic-button";
import { useDatabaseFormat } from "../../internationalization/hook";
import { DATABASE_PROFILE } from "../../internationalization/profile";

export type DatabaseRawDatabaseViewProps = {

    readonly origin: ImbricateOriginObject;
    readonly database: IImbricateDatabase;
};

export const DatabaseRawDatabaseView: FC<DatabaseRawDatabaseViewProps> = (
    props: DatabaseRawDatabaseViewProps,
) => {

    const databaseFormat = useDatabaseFormat();

    const editingControllerRef =
        React.useRef<DocumentEditingController | undefined>(undefined);

    useTitleWithAsyncFormat(
        databaseFormat,
        () => {
            return [
                props.database.databaseName,
                databaseFormat.get(DATABASE_PROFILE.DATABASE),
            ];
        },
        [props.database.databaseName],
    );

    const itemPerPage = useItemPerPage(
        props.origin.origin.uniqueIdentifier,
        props.database.uniqueIdentifier,
    );

    const displayedProperties = useDisplayedProperties(
        props.origin.origin.uniqueIdentifier,
        props.database.uniqueIdentifier,
        [],
    );

    const magicButtonResult = useRawDatabaseViewMagicButton(
        props.database.uniqueIdentifier,
        editingControllerRef,
        [props.database.uniqueIdentifier],
    );

    return (<div
        className="h-full flex flex-col overflow-auto relative"
    >
        <DatabaseHeader
            originUniqueIdentifier={props.origin.origin.uniqueIdentifier}
            database={props.database}
            itemPerPage={itemPerPage.itemPerPage}
            onItemPerPageChange={itemPerPage.setItemPerPage}
            magicButtonResult={magicButtonResult}
            displayedProperties={displayedProperties.displayedProperties}
            onDisplayedPropertiesChange={displayedProperties.setDisplayedProperties}
        />
        <DatabaseDatabaseSet
            itemPerPage={itemPerPage.itemPerPage}
            origin={props.origin}
            database={props.database}
            editingControllerRef={editingControllerRef}
            displayedProperties={displayedProperties.displayedProperties}
            magicButtonResult={magicButtonResult}
        />
    </div>);
};
