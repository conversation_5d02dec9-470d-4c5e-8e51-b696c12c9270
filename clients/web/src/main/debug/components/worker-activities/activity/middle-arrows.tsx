/**
 * <AUTHOR>
 * @namespace WorkerActivities_Activity
 * @description Middle Arrows
 */

import { WORKER_ACTIVITY_INITIATOR, WorkerActivityItem } from "@imbricate-hummingbird/bridge-core";
import React, { FC } from "react";
import { FaArrowLeftLong, FaArrowRightLong } from "react-icons/fa6";
import { TbBuildingBroadcastTowerFilled } from "react-icons/tb";

export type WorkerActivitiesActivityMiddleArrowsProps = {

    activitySet: [WorkerActivityItem | null, WorkerActivityItem | null];
};

export const WorkerActivitiesActivityMiddleArrows: FC<WorkerActivitiesActivityMiddleArrowsProps> = (
    props: WorkerActivitiesActivityMiddleArrowsProps,
) => {

    const activitySet = props.activitySet;

    return (<React.Fragment>
        {Boolean(activitySet[0]?.initiator === WORKER_ACTIVITY_INITIATOR.INITIATOR) &&
            (<div className="flex flex-col justify-center h-full">
                <FaArrowRightLong
                    className="text-4xl self-start text-primary-500"
                />
                {Boolean(activitySet[1]) && <FaArrowLeftLong
                    className="text-xl self-end text-secondary-500"
                />}
            </div>)}
        {Boolean(activitySet[1]?.initiator === WORKER_ACTIVITY_INITIATOR.INITIATOR) &&
            (<div className="flex flex-col justify-center h-full">
                <FaArrowLeftLong
                    className="text-4xl self-end text-secondary-500"
                />
                {Boolean(activitySet[0]) && <FaArrowRightLong
                    className="text-xl self-start text-primary-500"
                />}
            </div>)}
        {Boolean(activitySet[0]?.initiator === WORKER_ACTIVITY_INITIATOR.NOTIFICATION) &&
            (<div className="flex flex-col justify-center h-full">
                <FaArrowRightLong
                    className="text-2xl self-start text-primary-500"
                />
                <TbBuildingBroadcastTowerFilled
                    className="text-4xl self-center text-primary-500"
                />

            </div>)}
        {Boolean(activitySet[1]?.initiator === WORKER_ACTIVITY_INITIATOR.NOTIFICATION) &&
            (<div className="flex flex-col justify-center h-full">
                <FaArrowLeftLong
                    className="text-2xl self-end text-secondary-500"
                />
                <TbBuildingBroadcastTowerFilled
                    className="text-4xl self-center text-secondary-500"
                />
            </div>)}
    </React.Fragment>);
};
