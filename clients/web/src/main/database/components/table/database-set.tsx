/**
 * <AUTHOR>
 * @namespace Database_Components_Table
 * @description Database Set
 */

import { UseMagicButtonResult } from "@/common/components/magic-button/hooks/use-magic-button";
import { useQueryDocuments } from "@/common/transfer/hooks/use-query-documents";
import { DocumentEditingController } from "@/main/document/controller/editing-controller";
import { ActionDescription } from "@imbricate-hummingbird/interceptor-core";
import { ImbricateOriginObject } from "@imbricate-hummingbird/origin-central";
import { useVersion } from "@imbricate-hummingbird/react-common";
import { IImbricateDatabase, ImbricateDocumentQuery } from "@imbricate/core";
import React, { FC, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { useRawDatabaseTableButton } from "../../hooks/use-raw-database-table-button";
import { DatabaseDatabasePagination } from "./database-pagination";
import { DatabaseDatabaseTable } from "./database-table";

const createQueryDocumentsAction = (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
): ActionDescription => {

    return {
        actionName: "Query Documents for database table",
        actionDescription: `Query documents from database ${databaseUniqueIdentifier} for database table`,
        executerMetadata: {
            executer: import.meta.url,
        },
        actionPayload: {
            originUniqueIdentifier,
            databaseUniqueIdentifier,
        },
    };
};

export type DatabaseDatabaseSetProps = {

    readonly itemPerPage: number;

    readonly origin: ImbricateOriginObject;
    readonly database: IImbricateDatabase;

    readonly editingControllerRef: React.RefObject<DocumentEditingController | undefined>;

    readonly displayedProperties: string[];

    readonly magicButtonResult: UseMagicButtonResult;
};

export const DatabaseDatabaseSet: FC<DatabaseDatabaseSetProps> = (
    props: DatabaseDatabaseSetProps,
) => {

    const [initialized, setInitialized] = React.useState<boolean>(false);
    const [searchParams, setSearchParams] = useSearchParams();

    const page = searchParams.get("page");

    const [version, updateVersion] = useVersion();
    const [, forceUpdate] = useVersion();

    const [currentPage, setCurrentPage] = React.useState<number>(
        page ? parseInt(page) : 1,
    );

    const query: ImbricateDocumentQuery = {
        limit: props.itemPerPage,
        skip: (currentPage - 1) * props.itemPerPage,
    };

    useRawDatabaseTableButton(
        props.magicButtonResult,
        {
            origin: props.origin,
            database: props.database,
            query,
            updateVersion,
        },
    );

    const setCurrentPageCallback = (page: number) => {

        setCurrentPage(page);
        setSearchParams({
            page: page.toString(),
        });
    };

    useEffect(() => {

        if (!initialized) {
            setInitialized(true);
            return;
        }

        setCurrentPageCallback(1);
    }, [props.database.uniqueIdentifier, props.itemPerPage]);

    const queryDocuments = useQueryDocuments(
        props.origin.origin.uniqueIdentifier,
        props.database.uniqueIdentifier,
        query,
        createQueryDocumentsAction(
            props.origin.origin.uniqueIdentifier,
            props.database.uniqueIdentifier,
        ),
        version,
        currentPage,
    );

    if (typeof queryDocuments === "symbol") {
        return null;
    }

    return (<React.Fragment>
        <DatabaseDatabaseTable
            origin={props.origin}
            database={props.database}
            documents={queryDocuments.documents}
            query={query}
            forceUpdate={forceUpdate}
            updateVersion={updateVersion}
            editingControllerRef={props.editingControllerRef}
            displayedProperties={props.displayedProperties}
        />
        <div
            className="absolute bottom-4 w-full z-50 flex justify-center pointer-events-none"
        >
            <DatabaseDatabasePagination
                total={Math.ceil(queryDocuments.count / props.itemPerPage)}
                current={currentPage}
                onChange={setCurrentPageCallback}
            />
        </div>
    </React.Fragment>);
};
