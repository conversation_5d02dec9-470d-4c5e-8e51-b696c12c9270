/**
 * <AUTHOR>
 * @package UI
 * @namespace DatePicker
 * @description Date Picker
 */

import { DatePicker } from "@heroui/date-picker";
import { DateValue } from "@internationalized/date";
import { FC, useCallback, useMemo } from "react";
import { uiDatePickerDateToUIDate, uiDatePickerUIDateToDate } from "./parse";
import { UIDatePickerVariant } from "./types";

export type UIDatePickerProps = {

    readonly ariaLabel: string;

    readonly isReadOnly?: boolean;

    readonly variant?: UIDatePickerVariant;

    readonly value: Date | null;
    readonly onChange?: (newDate: Date | null) => void;
};

export const UIDatePicker: FC<UIDatePickerProps> = (
    props: UIDatePickerProps,
) => {

    const hashedDate: string = props.value === null
        ? "null"
        : String(props.value.getTime())
        ;

    const parsedDate: DateValue | null = useMemo(() => {

        if (!props.value) {
            return null;
        }

        return uiDatePickerDateToUIDate(props.value);
    }, [hashedDate]);

    const onChange = useCallback((newDate: DateValue | null) => {

        if (!props.onChange) {
            return;
        }

        if (!newDate) {
            props.onChange(null);
            return;
        }

        const newParsedDate: Date =
            uiDatePickerUIDateToDate(newDate);
        props.onChange(newParsedDate);
    }, [props.onChange]);

    return (<DatePicker
        aria-label={props.ariaLabel}
        isReadOnly={props.isReadOnly}
        variant={props.variant}
        value={parsedDate}
        onChange={props.onChange
            ? onChange
            : undefined}
    />);
};
