/**
 * <AUTHOR>
 * @namespace Common_Components_MagicButton_Types
 * @description Button Item
 */

import { UIColor } from "@imbricate-hummingbird/ui";
import { IconType } from "react-icons";

export type MagicButtonItem = {

    readonly itemKey: string;
    readonly categoryKey: string;

    readonly title: string;
    readonly description: string;

    readonly icon: IconType;

    readonly order?: number;
    readonly color?: UIColor;
    readonly externalIndicator?: boolean;
    readonly redirectIndicator?: boolean;
    readonly quickAccess?: boolean;
    readonly defaultQuickAccess?: boolean;

    readonly onPress: () => void;
};
