/**
 * <AUTHOR>
 * @namespace Navigation_Internationalization
 * @description Intl
 */

import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooLazyInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { NAVIGATION_PROFILE } from "./profile";

export const navigationInternationalization: SudooLazyInternationalization<NAVIGATION_PROFILE> =
    SudooLazyInternationalization.create<NAVIGATION_PROFILE>(
        DEFAULT_LOCALE,
    );

navigationInternationalization.mergeLazyProfile(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    import("./locale/en-US").then(
        (module) => module.enUSNavigationProfile,
    ),
);

navigationInternationalization.mergeLazyProfile(
    IETF_LOCALE.JAPANESE_JAPAN,
    import("./locale/ja-JP").then(
        (module) => module.jaJPNavigationProfile,
    ),
);

navigationInternationalization.mergeLazyProfile(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    import("./locale/zh-CN").then(
        (module) => module.zhCNNavigationProfile,
    ),
);
