/**
 * <AUTHOR>
 * @namespace Debug_Components_RecentActionTracker
 * @description Recent Action Details
 */

import React, { FC } from "react";
import { DisplayRecentAction } from "../../types/display-recent-action";
import { RecentActionItemCard } from "./recent-action-item";

export type RecentActionDetailsProps = {

    readonly showExtraInformation: boolean;

    readonly displayRecentActions: DisplayRecentAction[];
};

export const RecentActionDetails: FC<RecentActionDetailsProps> = (
    props: RecentActionDetailsProps,
) => {

    if (props.displayRecentActions.length === 0) {
        return (<div>
            No actions found
        </div>);
    }

    return (<div
        className="flex flex-col gap-3"
    >
        {props.displayRecentActions.map((
            displayRecentAction: DisplayRecentAction,
        ) => {

            return (<RecentActionItemCard
                key={displayRecentAction.recentAction.recentActionIdentifier}
                showExtraInformation={props.showExtraInformation}
                displayRecentAction={displayRecentAction}
            />);
        })}
    </div>);
};
