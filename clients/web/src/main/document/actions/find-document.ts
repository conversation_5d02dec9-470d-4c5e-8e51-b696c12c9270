/**
 * <AUTHOR>
 * @namespace Document_Actions
 * @description Find Document
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createFindDocumentAction = (
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Find Document",
        actionDescription: `Find the document from [${databaseUniqueIdentifier}] with identifier [${documentUniqueIdentifier}]`,
        executerMetadata: metadata,
    };
};
