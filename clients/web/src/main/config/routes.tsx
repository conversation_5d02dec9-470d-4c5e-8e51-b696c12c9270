/**
 * <AUTHOR>
 * @package Web
 * @namespace Config_Routes
 * @description Routes
 */

import { checkIsDebugOrDevelopmentMode } from "@imbricate-hummingbird/debug-environment";
import { Route } from "react-router-dom";
import { ConfigViewWrapper } from "./config-view-wrapper";
import { ConfigConfigurationExportViewWrapper } from "./configuration/export-view-wrapper";
import { ConfigConfigurationImportViewWrapper } from "./configuration/import-view-wrapper";
import { ConfigAppearancePanelWrapper } from "./panels-wrapper/appearance-panel-wrapper";
import { ConfigDebugPanelWrapper } from "./panels-wrapper/debug-panel-wrapper";
import { ConfigGuidancePanelWrapper } from "./panels-wrapper/guidance-panel-wrapper";
import { ConfigImbriScriptPanelWrapper } from "./panels-wrapper/imbriscript-panel-wrapper";
import { ConfigMarkdownPanelWrapper } from "./panels-wrapper/markdown-panel-wrapper";
import { ConfigNavigationPanelWrapper } from "./panels-wrapper/navigation-panel-wrapper";
import { ConfigAboutPanel } from "./panels/about-panel";
import { ConfigEditorPanel } from "./panels/editor-panel";
import { ConfigEditorMonacoPanel } from "./panels/editor/monaco-panel";
import { ConfigGeneralPanel } from "./panels/general-panel";
import { ConfigImbricateScriptOutlookPanel } from "./panels/imbriscript/outlook-panel";
import { ConfigImportExportPanel } from "./panels/import-export-panel";
import { ConfigRootPanel } from "./panels/root-panel";
import { ConfigUsageHistoryPanel } from "./panels/usage-history-panel";

export const getConfigRoutes = () => {

    const isDebug = checkIsDebugOrDevelopmentMode();

    return (<Route
        path="config"
        element={<ConfigViewWrapper />}
    >
        <Route
            index
            Component={ConfigRootPanel}
        />
        <Route
            path="general"
            Component={ConfigGeneralPanel}
        />
        <Route
            path="appearance"
            Component={ConfigAppearancePanelWrapper}
        />
        <Route
            path="navigation"
            Component={ConfigNavigationPanelWrapper}
        />
        <Route
            path="editor"
        >
            <Route
                index
                Component={ConfigEditorPanel}
            />
            <Route
                path="monaco"
                Component={ConfigEditorMonacoPanel}
            />
        </Route>
        <Route
            path="markdown"
            Component={ConfigMarkdownPanelWrapper}
        />
        <Route
            path="imbriscript"
        >
            <Route
                index
                Component={ConfigImbriScriptPanelWrapper}
            />
            <Route
                path="outlook"
                Component={ConfigImbricateScriptOutlookPanel}
            />
        </Route>
        <Route
            path="guidance"
            Component={ConfigGuidancePanelWrapper}
        />
        <Route
            path="usage-history"
            Component={ConfigUsageHistoryPanel}
        />
        <Route
            path="import-export"
        >
            <Route
                index
                Component={ConfigImportExportPanel}
            />
            <Route
                path="export"
                Component={ConfigConfigurationExportViewWrapper}
            />
            <Route
                path="import"
                Component={ConfigConfigurationImportViewWrapper}
            />
        </Route>
        {isDebug && (
            <Route
                path="debug"
                Component={ConfigDebugPanelWrapper}
            />
        )}
        <Route
            path="about"
            Component={ConfigAboutPanel}
        />
    </Route>);
};
