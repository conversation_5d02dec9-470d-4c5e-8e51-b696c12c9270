/**
 * <AUTHOR>
 * @package React Components
 * @namespace ErrorMessage_Internationalization
 * @description Hook
 */

import { useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { reactComponentsErrorMessageInternationalization } from "./intl";
import { REACT_COMPONENTS_ERROR_MESSAGE_PROFILE } from "./profile";

export const useReactComponentsErrorMessageFormat = (): SudooFormat<REACT_COMPONENTS_ERROR_MESSAGE_PROFILE> => {

    const locale = useLocale();

    return reactComponentsErrorMessageInternationalization.format(locale);
};
