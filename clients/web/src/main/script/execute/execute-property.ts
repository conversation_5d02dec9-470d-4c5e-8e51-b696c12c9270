/**
 * <AUTHOR>
 * @namespace Script_Actions
 * @description Execute Property
 */

import { ActionCentral, ActionDescription } from "@imbricate-hummingbird/interceptor-core";
import { ImbriScriptResult, ImbriScriptResultStatus } from "@imbricate-hummingbird/script-core";
import { ScriptManager } from "@imbricate-hummingbird/script-manager";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { OldDataCentral } from "../../data/old-data-central";
import { rootLogger } from "../../log/logger";
import { findProperty } from "../../property/utils/find-property";
import { findText } from "../../text/util/find-text";
import { createFindOriginForSandboxAction } from "../actions/find-origin-for-sandbox";

const logger = rootLogger.fork({
    scopes: [
        "Script",
        "Execute",
        "Property",
    ],
});

export const executeProperty = async (
    databaseUniqueIdentifier: string,
    documentUniqueIdentifier: string,
    propertyUniqueIdentifier: string,
    result: ImbriScriptResult,
    actionDescription: ActionDescription,
): Promise<ImbriScriptResult> => {

    result.updateStatus(
        ImbriScriptResultStatus.LOADING_IMBRISCRIPT,
    );

    const property = await findProperty(
        databaseUniqueIdentifier,
        documentUniqueIdentifier,
        propertyUniqueIdentifier,
    );

    if (typeof property === "symbol") {
        result.failExecution("Property not found");
        return result;
    }

    if (typeof property.documentProperty === "undefined") {
        result.failExecution("Property is empty");
        return result;
    }

    if (property.documentProperty.propertyType !== IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT) {
        result.failExecution("Property is not an ImbriScript");
        return result;
    }

    const origin = await ActionCentral.getInstance().executeAction(
        async (
            actionIdentifier: string,
            recordIdentifier: string,
        ) => {
            return await OldDataCentral.getInstance().wideOriginGetOrigin(
                databaseUniqueIdentifier,
                actionIdentifier,
                recordIdentifier,
            );
        },
        createFindOriginForSandboxAction(
            databaseUniqueIdentifier,
            {
                executer: import.meta.url,
            },
        ),
    );

    if (!origin) {
        result.failExecution("Origin not found");
        return result;
    }

    const text = await findText(
        origin.origin.uniqueIdentifier,
        property.documentProperty.propertyValue as string,
    );

    if (typeof text === "symbol") {
        result.failExecution("Text not found");
        return result;
    }

    logger.debug("Executing ImbriScript", {
        property: property.documentProperty.propertyKey,
    });

    result.updateStatus(
        ImbriScriptResultStatus.PENDING,
    );

    await ScriptManager.getInstance()
        .executeSelfManagedResultScript(
            result,
            text.textContent,
            {
                originUniqueIdentifier: origin.origin.uniqueIdentifier,
            },
            actionDescription,
        );

    return result;
};
