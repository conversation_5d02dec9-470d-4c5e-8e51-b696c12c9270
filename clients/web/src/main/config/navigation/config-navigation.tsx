/**
 * <AUTHOR>
 * @namespace Config_Navigation
 * @description Config Navigation
 */

import { isFormatLoading } from "@imbricate-hummingbird/internationalization";
import { UIScrollShadow } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { BiSolidNavigation } from "react-icons/bi";
import { FaBug, FaCog, FaHistory, FaInfo, FaPaintBrush } from "react-icons/fa";
import { FaCode, FaMarkdown, FaTrowelBricks } from "react-icons/fa6";
import { RiGuideFill, RiJavascriptFill } from "react-icons/ri";
import { useConfigFormat } from "../internationalization/hook";
import { CONFIG_PROFILE } from "../internationalization/profile";
import { ConfigNavigationButton } from "./config-navigation-button";
import { getConfigAboutRoute, getConfigAppearanceRoute, getConfigDebugRoute, getConfigEditorRoute, getConfigGeneralRoute, getConfigGuidanceRoute, getConfigImbriScriptRoute, getConfigImportExportRoute, getConfigMarkdownRoute, getConfigNavigationRoute, getConfigUsageHistoryRoute } from "./util/config-route";

export type ConfigNavigationProps = {

    readonly shouldDisplayDebugPanel: boolean;
};

export const ConfigNavigation: FC<ConfigNavigationProps> = (
    props: ConfigNavigationProps,
) => {

    const configFormat = useConfigFormat();

    if (isFormatLoading(configFormat)) {
        return null;
    }

    return (<UIScrollShadow
        className="flex flex-col gap-1 pt-2 min-w-[200px]"
        overflowYAuto
    >
        <ConfigNavigationButton
            label={configFormat.get(CONFIG_PROFILE.GENERAL)}
            icon={FaCog}
            targetUrl={getConfigGeneralRoute()}
        />
        <ConfigNavigationButton
            label={configFormat.get(CONFIG_PROFILE.APPEARANCE)}
            icon={FaPaintBrush}
            targetUrl={getConfigAppearanceRoute()}
        />
        <ConfigNavigationButton
            label={configFormat.get(CONFIG_PROFILE.NAVIGATION)}
            icon={BiSolidNavigation}
            targetUrl={getConfigNavigationRoute()}
        />
        <ConfigNavigationButton
            label={configFormat.get(CONFIG_PROFILE.EDITOR)}
            icon={FaCode}
            targetUrl={getConfigEditorRoute()}
        />
        <ConfigNavigationButton
            label={configFormat.get(CONFIG_PROFILE.MARKDOWN)}
            icon={FaMarkdown}
            targetUrl={getConfigMarkdownRoute()}
        />
        <ConfigNavigationButton
            label={configFormat.get(CONFIG_PROFILE.IMBRISCRIPT)}
            icon={RiJavascriptFill}
            targetUrl={getConfigImbriScriptRoute()}
        />
        <ConfigNavigationButton
            label={configFormat.get(CONFIG_PROFILE.GUIDANCE)}
            icon={RiGuideFill}
            targetUrl={getConfigGuidanceRoute()}
        />
        <ConfigNavigationButton
            label={configFormat.get(CONFIG_PROFILE.USAGE_HISTORY)}
            icon={FaHistory}
            targetUrl={getConfigUsageHistoryRoute()}
        />
        <ConfigNavigationButton
            label={configFormat.get(CONFIG_PROFILE.IMPORT_EXPORT)}
            icon={FaTrowelBricks}
            targetUrl={getConfigImportExportRoute()}
        />
        {props.shouldDisplayDebugPanel && (
            <ConfigNavigationButton
                label={configFormat.get(CONFIG_PROFILE.DEBUG)}
                icon={FaBug}
                targetUrl={getConfigDebugRoute()}
            />
        )}
        <ConfigNavigationButton
            label={configFormat.get(CONFIG_PROFILE.ABOUT)}
            icon={FaInfo}
            targetUrl={getConfigAboutRoute()}
        />
    </UIScrollShadow>);
};
