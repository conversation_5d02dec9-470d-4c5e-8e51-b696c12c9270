/**
 * <AUTHOR>
 * @package UI
 * @namespace Checkbox
 * @description Checkbox Group
 */

import { Checkbox, CheckboxGroup } from "@heroui/checkbox";
import type { FC, ReactNode } from "react";
import { UISize } from "../common/types";
import { UICheckboxGroupItem } from "./types";

export type UICheckboxGroupProps = {

    readonly className?: string;

    readonly isDisabled?: boolean;
    readonly isReadOnly?: boolean;
    readonly isRequired?: boolean;
    readonly isInvalid?: boolean;

    readonly size?: UISize;

    readonly label?: ReactNode;
    readonly description?: ReactNode;

    readonly items: UICheckboxGroupItem[];

    readonly selectedValues?: string[];
    readonly onSelectedValuesChange?: (newValues: string[]) => void;
};

export const UICheckboxGroup: FC<UICheckboxGroupProps> = (
    props: UICheckboxGroupProps,
) => {

    return (<CheckboxGroup
        className={props.className}

        isDisabled={props.isDisabled}
        isReadOnly={props.isReadOnly}
        isRequired={props.isRequired}
        isInvalid={props.isInvalid}

        size={props.size}

        label={props.label}
        description={props.description}

        value={props.selectedValues}
        onValueChange={props.onSelectedValuesChange}
    >
        {props.items.map((
            item: UICheckboxGroupItem,
        ) => {

            return (<Checkbox
                key={item.itemKey}

                className={item.className}

                color={item.color}

                isDisabled={item.isDisabled}
                isReadOnly={item.isReadOnly}

                value={item.itemKey}
            >
                {item.itemContent}
            </Checkbox>);
        })}
    </CheckboxGroup>);
};
