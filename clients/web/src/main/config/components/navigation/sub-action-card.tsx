/**
 * <AUTHOR>
 * @namespace Main_Config_Components_Navigation
 * @description Sub Action Card
 */

import { HUMMINGBIRD_PREFERENCE_ITEM, usePreferenceItem } from "@imbricate-hummingbird/configuration";
import { FormattedRadioGroup, FormattedText, StyledCard } from "@imbricate-hummingbird/react-components";
import { UICardBody, UICardHeader, UIDivider, UISpacer } from "@imbricate-hummingbird/ui";
import { FC } from "react";
import { FaSubscript } from "react-icons/fa6";
import { useConfigFormat } from "../../internationalization/hook";
import { CONFIG_PROFILE } from "../../internationalization/profile";

export type ConfigNavigationSubActionCardProps = {
};

export const ConfigNavigationSubActionCard: FC<ConfigNavigationSubActionCardProps> = (
    _props: ConfigNavigationSubActionCardProps,
) => {

    const configFormat = useConfigFormat();

    const navigationSubActionEnablement = usePreferenceItem(
        HUMMINGBIRD_PREFERENCE_ITEM.NAVIGATION_SUB_ACTION_ENABLEMENT,
    );

    return (<StyledCard>
        <UICardHeader>
            <FaSubscript />
            <UISpacer />
            <FormattedText
                usedFormats={[
                    configFormat,
                ]}
            >
                {configFormat.get(CONFIG_PROFILE.SUB_ACTION)}
            </FormattedText>
        </UICardHeader>
        <UIDivider />
        <UICardBody
            className="flex flex-col gap-2"
        >
            <div>
                <FormattedText
                    usedFormats={[
                        configFormat,
                    ]}
                    className="text-sm text-gray-500"
                >
                    {configFormat.get(CONFIG_PROFILE.NAVIGATION_SUB_ACTION_ENABLEMENT_DESCRIPTION)}
                </FormattedText>
            </div>
            <FormattedRadioGroup
                label={configFormat.get(CONFIG_PROFILE.NAVIGATION_SUB_ACTION_ENABLEMENT)}
                selected={navigationSubActionEnablement.preference.toString()}
                onSelectedChange={(value) => {

                    navigationSubActionEnablement.updatePreference(value === "true");
                }}
                format={configFormat}
                items={[
                    {
                        itemKey: "true",
                        itemProfileItem: CONFIG_PROFILE.ENABLED,
                    },
                    {
                        itemKey: "false",
                        itemProfileItem: CONFIG_PROFILE.DISABLED,
                    },
                ]}
            />
        </UICardBody>
    </StyledCard>);
};
