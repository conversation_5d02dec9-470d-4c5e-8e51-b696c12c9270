/**
 * <AUTHOR>
 * @package Internationalization
 * @namespace Hooks
 * @description Use Formatted Title
 */

import { ReactDependency, useAsyncTitle } from "@imbricate-hummingbird/react-common";
import { SudooFormat } from "@sudoo/internationalization";
import { isFormatLoading } from "../config";

export const useAsyncTitleWithAsyncFormat = (
    condition: () => boolean,
    format: SudooFormat,
    getTitle: () => string[],
    deps: ReactDependency[],
): void => {

    useAsyncTitle(
        () => {
            if (!condition()) {
                return false;
            }

            return !isFormatLoading(format);
        },
        getTitle,
        [...deps, isFormatLoading(format)],
    );
};

export const useTitleWithAsyncFormat = (
    format: SudooFormat,
    getTitle: () => string[],
    deps: ReactDependency[],
): void => {

    useAsyncTitle(
        () => !isFormatLoading(format),
        getTitle,
        [...deps, isFormatLoading(format)],
    );
};
