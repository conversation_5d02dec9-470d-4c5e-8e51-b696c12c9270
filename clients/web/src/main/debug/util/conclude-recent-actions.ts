/**
 * <AUTHOR>
 * @namespace Debug_Util
 * @description Conclude Recent Actions
 */

import { rootLogger } from "@/main/log/logger";
import { RECENT_ACTION_STATUS, RecentActionItem } from "@imbricate-hummingbird/interceptor-core";
import { WORKER_WEB_WORKER } from "@imbricate-hummingbird/web-worker-core";
import { DisplayRecentAction } from "../types/display-recent-action";

const logger = rootLogger.fork({
    scopes: [
        "Debug",
        "Util",
        "ConcludeRecentActions",
    ],
});

export const concludeRecentActions = (
    recentActions: RecentActionItem<WORKER_WEB_WORKER>[],
): DisplayRecentAction[] => {

    const displayRecentActionsMap: Map<string, DisplayRecentAction> = new Map();

    for (const recentAction of recentActions) {

        const displayRecentAction: DisplayRecentAction = {
            recentAction,
            subActions: [],
        };

        displayRecentActionsMap.set(recentAction.recentActionIdentifier, displayRecentAction);
    }

    const toBeRemovedRecentActions: string[] = [];

    for (const recentAction of recentActions) {

        const currentDisplayRecentAction = displayRecentActionsMap.get(recentAction.recentActionIdentifier)!;

        if (typeof recentAction.parentRecentActionIdentifier === "string") {

            const parentDisplayRecentAction =
                displayRecentActionsMap.get(recentAction.parentRecentActionIdentifier)!;

            if (!parentDisplayRecentAction) {
                logger.error(`Parent recent action not found: ${recentAction.recentActionIdentifier}`);
                continue;
            }

            parentDisplayRecentAction.subActions.push(currentDisplayRecentAction);
            toBeRemovedRecentActions.push(recentAction.recentActionIdentifier);
        }
    }

    for (const recentAction of toBeRemovedRecentActions) {
        displayRecentActionsMap.delete(recentAction);
    }

    const displayRecentActionsResult: DisplayRecentAction[] = Array.from(displayRecentActionsMap.values());

    const sortedDisplayRecentActions = displayRecentActionsResult.sort((a: DisplayRecentAction, b: DisplayRecentAction) => {

        if (a.recentAction.status === RECENT_ACTION_STATUS.PENDING) {
            return -1;
        }
        if (b.recentAction.status === RECENT_ACTION_STATUS.PENDING) {
            return 1;
        }
        if (a.recentAction.endAt && b.recentAction.endAt) {
            return b.recentAction.endAt - a.recentAction.endAt;
        }
        return b.recentAction.startAt - a.recentAction.startAt;
    });

    return sortedDisplayRecentActions;
};
