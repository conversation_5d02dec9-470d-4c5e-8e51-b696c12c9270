/**
 * <AUTHOR>
 * @namespace Internationalization_Internationalization_Hooks
 * @description Internationalization
 */

import { defaultEmptyFormat, useLocale } from "@imbricate-hummingbird/internationalization";
import { SudooFormat } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { useEffect, useState } from "react";
import { internationalizationInternationalization } from "../intl";

export const useInternationalizationFormat = (): SudooFormat<IETF_LOCALE> => {

    const locale = useLocale();

    const [format, setFormat] = useState<SudooFormat<IETF_LOCALE>>(defaultEmptyFormat);

    useEffect(() => {

        const execute = async () => {

            const format = await internationalizationInternationalization.format(locale);
            setFormat(format);
        };

        execute();
    }, [locale]);

    return format;
};
