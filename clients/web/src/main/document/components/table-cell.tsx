/**
 * <AUTHOR>
 * @namespace Document_Components
 * @description Table Cell
 */

import { TransferPropertyDraft, getDefaultTransferPropertyVariant } from "@imbricate-hummingbird/transfer-core";
import { TableCell } from "@imbricate-hummingbird/ui";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaProperty, ImbricateDatabaseSchemaPropertyOptionsBinary, ImbricateDatabaseSchemaPropertyOptionsLabel, ImbricateDatabaseSchemaPropertyOptionsReference, getImbricateDefaultValueOfProperty } from "@imbricate/core";
import { JSX } from "react";
import { DocumentEditingController, DocumentEditingControllerEditingDocument } from "../controller/editing-controller";
import { ArrangeDocumentsResultItem } from "../util/arrange-documents";
import { DocumentTableBinaryCell } from "./table-cells/binary-cell";
import { DocumentTableBooleanCell } from "./table-cells/boolean-cell";
import { DocumentTableDateCell } from "./table-cells/date-cell";
import { DocumentTableImbriscriptCell } from "./table-cells/imbriscript-cell";
import { DocumentTableJsonCell } from "./table-cells/json-cell";
import { DocumentTableLabelCell } from "./table-cells/label-cell";
import { DocumentTableMarkdownCell } from "./table-cells/markdown-cell";
import { DocumentTableNumberCell } from "./table-cells/number-cell";
import { DocumentTableReferenceCell } from "./table-cells/reference-cell";
import { DocumentTableStringCell } from "./table-cells/string-cell";

export type DocumentTableCellProps = {

    readonly originUniqueIdentifier: string;
    readonly databaseUniqueIdentifier: string;

    readonly propertyIdentifier: string;

    readonly propertyDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> | undefined;
    readonly schemaProperty: ImbricateDatabaseSchemaProperty<IMBRICATE_PROPERTY_TYPE>;

    readonly document: ArrangeDocumentsResultItem;
    readonly editingController: DocumentEditingController;
};

export const getDocumentTableCell = (
    props: DocumentTableCellProps,
): JSX.Element => {

    const propertyType: IMBRICATE_PROPERTY_TYPE = props.schemaProperty.propertyType;

    const getEditingProperty = (): TransferPropertyDraft<any> => {

        const editingDocument: DocumentEditingControllerEditingDocument | undefined =
            props.editingController.getEditingDocument(props.document.document);

        if (!editingDocument) {
            throw new Error("[Imbricate] Editing document not found");
        }

        const targetDraft: TransferPropertyDraft<any> | undefined =
            editingDocument.newDrafts.find((
                draft: TransferPropertyDraft<any>,
            ) => {
                return draft.key === props.propertyIdentifier;
            });

        const editingDraft: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE> =
            targetDraft
            ?? {
                key: props.propertyIdentifier,
                type: propertyType,
                value: props.propertyDraft
                    ? props.propertyDraft.value
                    : getImbricateDefaultValueOfProperty(propertyType),
                variant: props.propertyDraft
                    ? props.propertyDraft.variant
                    : getDefaultTransferPropertyVariant(propertyType),
            };

        return editingDraft;
    };

    const updateEditingProperty = (value: any) => {

        props.editingController.setUpdatingProperty(
            props.document.document,
            props.propertyIdentifier,
            value,
        );
    };

    // IMBRICATE_PROPERTY_TYPE SWITCH
    switch (propertyType) {

        case IMBRICATE_PROPERTY_TYPE.BINARY: {

            const options: ImbricateDatabaseSchemaPropertyOptionsBinary =
                props.schemaProperty.propertyOptions as ImbricateDatabaseSchemaPropertyOptionsBinary;

            return (<TableCell
                key={props.propertyIdentifier}
            >
                <DocumentTableBinaryCell
                    originUniqueIdentifier={props.originUniqueIdentifier}
                    propertyKey={props.propertyIdentifier}
                    property={props.propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY>}
                    getEditingProperty={getEditingProperty}
                    updateEditingProperty={updateEditingProperty}
                    editing={props.document.editing}
                    options={options}
                />
            </TableCell>);
        }
        case IMBRICATE_PROPERTY_TYPE.BOOLEAN: {

            return (<TableCell
                key={props.propertyIdentifier}
            >
                <DocumentTableBooleanCell
                    propertyKey={props.propertyIdentifier}
                    property={props.propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BOOLEAN>}
                    getEditingProperty={getEditingProperty}
                    updateEditingProperty={updateEditingProperty}
                    editing={props.document.editing}
                />
            </TableCell>);
        }
        case IMBRICATE_PROPERTY_TYPE.STRING: {

            return (<TableCell
                key={props.propertyIdentifier}
            >
                <DocumentTableStringCell
                    propertyKey={props.propertyIdentifier}
                    property={props.propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.STRING>}
                    getEditingProperty={getEditingProperty}
                    updateEditingProperty={updateEditingProperty}
                    editing={props.document.editing}
                />
            </TableCell>);
        }
        case IMBRICATE_PROPERTY_TYPE.NUMBER: {

            return (<TableCell
                key={props.propertyIdentifier}
            >
                <DocumentTableNumberCell
                    propertyKey={props.propertyIdentifier}
                    property={props.propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.NUMBER>}
                    getEditingProperty={getEditingProperty}
                    updateEditingProperty={updateEditingProperty}
                    editing={props.document.editing}
                />
            </TableCell>);
        }
        case IMBRICATE_PROPERTY_TYPE.DATE: {

            return (<TableCell
                key={props.propertyIdentifier}
            >
                <DocumentTableDateCell
                    propertyKey={props.propertyIdentifier}
                    property={props.propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.DATE>}
                    getEditingProperty={getEditingProperty}
                    updateEditingProperty={updateEditingProperty}
                    editing={props.document.editing}
                />
            </TableCell>);
        }
        case IMBRICATE_PROPERTY_TYPE.MARKDOWN: {

            return (<TableCell
                key={props.propertyIdentifier}
            >
                <DocumentTableMarkdownCell
                    originUniqueIdentifier={props.databaseUniqueIdentifier}
                    databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                    documentUniqueIdentifier={props.document.document.documentUniqueIdentifier}
                    propertyKey={props.propertyIdentifier}
                    property={props.propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.MARKDOWN>}
                    getEditingProperty={getEditingProperty}
                    updateEditingProperty={updateEditingProperty}
                    editing={props.document.editing}
                />
            </TableCell>);
        }
        case IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT: {

            return (<TableCell
                key={props.propertyIdentifier}
            >
                <DocumentTableImbriscriptCell
                    originUniqueIdentifier={props.databaseUniqueIdentifier}
                    databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                    documentUniqueIdentifier={props.document.document.documentUniqueIdentifier}
                    propertyKey={props.propertyIdentifier}
                    property={props.propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.IMBRISCRIPT>}
                    getEditingProperty={getEditingProperty}
                    updateEditingProperty={updateEditingProperty}
                    editing={props.document.editing}
                />
            </TableCell>);
        }
        case IMBRICATE_PROPERTY_TYPE.JSON: {

            return (<TableCell
                key={props.propertyIdentifier}
            >
                <DocumentTableJsonCell
                    originUniqueIdentifier={props.databaseUniqueIdentifier}
                    databaseUniqueIdentifier={props.databaseUniqueIdentifier}
                    documentUniqueIdentifier={props.document.document.documentUniqueIdentifier}
                    propertyKey={props.propertyIdentifier}
                    property={props.propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.JSON>}
                    getEditingProperty={getEditingProperty}
                    updateEditingProperty={updateEditingProperty}
                    editing={props.document.editing}
                />
            </TableCell>);
        }
        case IMBRICATE_PROPERTY_TYPE.LABEL: {

            const options: ImbricateDatabaseSchemaPropertyOptionsLabel =
                props.schemaProperty.propertyOptions as ImbricateDatabaseSchemaPropertyOptionsLabel;

            return (<TableCell
                key={props.propertyIdentifier}
                className="min-w-32"
            >
                <DocumentTableLabelCell
                    propertyKey={props.propertyIdentifier}
                    property={props.propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.LABEL>}
                    getEditingProperty={getEditingProperty}
                    updateEditingProperty={updateEditingProperty}
                    editing={props.document.editing}

                    options={options}
                />
            </TableCell>);
        }
        case IMBRICATE_PROPERTY_TYPE.REFERENCE: {

            const options: ImbricateDatabaseSchemaPropertyOptionsReference =
                props.schemaProperty.propertyOptions as ImbricateDatabaseSchemaPropertyOptionsReference;

            return (<TableCell
                key={props.propertyIdentifier}
                className="min-w-32"
            >
                <DocumentTableReferenceCell
                    propertyKey={props.propertyIdentifier}
                    property={props.propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.REFERENCE>}
                    getEditingProperty={getEditingProperty}
                    updateEditingProperty={updateEditingProperty}
                    editing={props.document.editing}

                    options={options}
                />
            </TableCell>);
        }
        default: {

            return (<TableCell
                key={props.propertyIdentifier}
            >
                <DocumentTableStringCell
                    propertyKey={props.propertyIdentifier}
                    property={props.propertyDraft as TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.STRING>}
                    getEditingProperty={getEditingProperty}
                    updateEditingProperty={updateEditingProperty}
                    editing={props.document.editing}
                />
            </TableCell>);
        }
    }
};
