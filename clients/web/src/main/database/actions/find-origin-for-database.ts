/**
 * <AUTHOR>
 * @namespace Database_Actions
 * @description Find Origin For Database
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";

export const createFindOriginForDatabaseAction = (
    originUniqueIdentifier: string,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Find Origin For Database",
        actionDescription: `Find the origin for [${originUniqueIdentifier}]`,
        executerMetadata: metadata,
    };
};
