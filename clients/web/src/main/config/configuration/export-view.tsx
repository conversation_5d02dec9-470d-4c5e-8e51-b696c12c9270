/**
 * <AUTHOR>
 * @namespace Config_Configuration
 * @description Export View
 */

import { useTitle } from "@imbricate-hummingbird/react-common";
import { StyledCard } from "@imbricate-hummingbird/react-components";
import { UIAlert, UIButton, UICardBody, UICardFooter, UICardHeader, UICheckboxGroup, UIDivider, UITextarea } from "@imbricate-hummingbird/ui";
import React, { FC } from "react";
import { FaCopy, FaExternalLinkSquareAlt } from "react-icons/fa";
import { ExportableConfiguration } from "../types/configuration";
import { readConfigurationStorage } from "../util/read-configuration";

export type ConfigConfigurationExportViewProps = {
};

// LAZY LOAD ONLY
const ConfigConfigurationExportView: FC<ConfigConfigurationExportViewProps> = (
    _props: ConfigConfigurationExportViewProps,
) => {

    useTitle([
        "Export Configuration",
        "Config",
    ], []);

    const [exportObjects, setExportObjects] = React.useState<string[]>([]);
    const [exportResults, setExportResults] = React.useState<ExportableConfiguration[]>([]);

    return (<div
        className="h-full min-h-full flex flex-col"
    >
        <div
            className="flex-1 min-h-0 min-w-0 flex flex-col pr-2 mt-2 gap-2 h-full"
        >
            <StyledCard>
                <UICardHeader>
                    Export Configuration
                </UICardHeader>
                <UIDivider />
                <UICardBody>
                    {exportObjects.includes("origins") && (<UIAlert
                        color="warning"
                        title="Authentication information will be included in Origin exports"
                        description="Please be cautious when sharing or storing exported Origin information."
                    />)}
                    <UICheckboxGroup
                        className="mt-2"
                        label="Export..."
                        selectedValues={exportObjects}
                        onSelectedValuesChange={(
                            newValues: string[],
                        ) => {
                            setExportObjects(newValues);
                        }}
                        items={[
                            {
                                itemKey: "lenses",
                                itemContent: "Lenses",
                            },
                            {
                                itemKey: "origins",
                                itemContent: "Origins",
                            },
                        ]}
                    />
                </UICardBody>
                {exportObjects.length > 0 && (<React.Fragment>
                    <UIDivider />
                    <UICardFooter>
                        <UIButton
                            startContent={<FaExternalLinkSquareAlt />}
                            variant="flat"
                            color="primary"
                            onPress={async () => {
                                const configuration: ExportableConfiguration =
                                    await readConfigurationStorage(exportObjects);

                                setExportResults([
                                    configuration,
                                    ...exportResults,
                                ].slice(0, 3));
                            }}
                        >
                            Export
                        </UIButton>
                    </UICardFooter>
                </React.Fragment>)}
            </StyledCard>
            {exportResults.map((result) => {
                return (<StyledCard
                    key={result.identifier}
                >
                    <UICardHeader
                        className="flex"
                    >
                        <div
                            className="flex-1"
                        >
                            <UIButton
                                startContent={<FaCopy />}
                                variant="flat"
                                color="primary"
                                onPress={() => {
                                    navigator.clipboard.writeText(JSON.stringify(result, null, 2));
                                }}
                            >
                                Copy
                            </UIButton>
                        </div>
                        <div>
                            Exported at {new Date(result.exportedAt).toLocaleString()}
                        </div>
                    </UICardHeader>
                    <UIDivider />
                    <UICardBody>
                        <UITextarea
                            isFullWidth
                            isReadOnly
                            label="Description"
                            labelPlacement="outside"
                            value={JSON.stringify(result, null, 2)}
                        />
                    </UICardBody>
                </StyledCard>);
            })}
        </div>
    </div >);
};
export default ConfigConfigurationExportView;
