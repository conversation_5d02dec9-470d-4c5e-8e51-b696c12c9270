/**
 * <AUTHOR>
 * @namespace Origin
 * @description Origins View Wrapper
 */

import { UtilityWrapper } from "@/common/components/utility-wrapper/utility-wrapper";
import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const OriginsViewApplication = LazyLoadComponent(
    () => import("./origins-view"),
    "Origins View Application",
);

export const OriginsViewWrapper: FC = () => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Origins View Application"
            fullHeight
        />}
    >
        <UtilityWrapper>
            <OriginsViewApplication />
        </UtilityWrapper>
    </React.Suspense>);
};
