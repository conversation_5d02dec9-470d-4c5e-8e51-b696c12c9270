/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyDisplay
 * @description Date Display
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { IMBRICATE_PROPERTY_TYPE } from "@imbricate/core";
import { FC } from "react";
import { useDocumentFormat } from "../../internationalization/hook";
import { DOCUMENT_PROFILE } from "../../internationalization/profile";

export type DocumentDateDisplayProps = {

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.DATE>;
};

export const DocumentDateDisplay: FC<DocumentDateDisplayProps> = (
    props: DocumentDateDisplayProps,
) => {

    const documentFormat = useDocumentFormat();

    const propertyDate = new Date(props.property.value);
    const isInvalidDate = isNaN(propertyDate.getTime());

    if (isInvalidDate) {

        return (<div>
            {documentFormat.get(DOCUMENT_PROFILE.INVALID_DATE)}
        </div>);
    }

    return (<div>
        {propertyDate.toLocaleString()}
    </div>);
};
