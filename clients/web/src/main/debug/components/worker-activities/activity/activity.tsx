/**
 * <AUTHOR>
 * @namespace Main_Debug_Components_WorkerActivities_Activity
 * @description Activity
 */

import { formatTime } from "@/main/common/util/format-time";
import { WORKER_ACTIVITY_INITIATOR, WorkerActivityItem } from "@imbricate-hummingbird/bridge-core";
import { UIButton, UIChip, UISpacer, UITextarea } from "@imbricate-hummingbird/ui";
import clsx from "clsx";
import React, { FC, useState } from "react";
import { FaEye, FaEyeSlash, FaPaintbrush, FaPersonRunning } from "react-icons/fa6";
import { WorkerActivitiesActivityHeaderChip } from "./header-chip";
import { WorkerActivitiesActivityMiddleArrows } from "./middle-arrows";

export type WorkerActivitiesActivityActivityProps = {

    activitySet: [WorkerActivityItem | null, WorkerActivityItem | null];
};

const getResponseDuration = (initiatorTime: number, responseTime: number): string => {

    const duration: number = responseTime - initiatorTime;
    return `${duration / 1000} seconds`;
};

export const WorkerActivitiesActivity: FC<WorkerActivitiesActivityActivityProps> = (
    props: WorkerActivitiesActivityActivityProps,
) => {

    const [showLeftPayload, setShowLeftPayload] = useState<boolean>(false);
    const [showRightPayload, setShowRightPayload] = useState<boolean>(false);

    const activitySet = props.activitySet;

    return (<React.Fragment>
        <div>
            {activitySet[0] && <div
                className={clsx(
                    "bg-primary-100 p-2 rounded-lg",
                    "flex flex-col gap-2",
                    activitySet[0].initiator === WORKER_ACTIVITY_INITIATOR.INITIATOR && "mb-4",
                    activitySet[0].initiator === WORKER_ACTIVITY_INITIATOR.RESPONSE && "mt-4",
                )}
            >
                <div
                    className="flex items-center gap-2"
                >
                    <UIChip
                        className="font-mono pl-2"
                        color="primary"
                        size="sm"
                        startContent={<FaPaintbrush />}
                    >
                        Main
                    </UIChip>
                    <WorkerActivitiesActivityHeaderChip
                        worker={activitySet[0].worker}
                        initiator={activitySet[0].initiator}
                    />
                    <UISpacer
                        isFlexFill
                    />
                    <UIButton
                        radius="lg"
                        variant="bordered"
                        color="primary"
                        size="sm"
                        onPress={() => setShowLeftPayload(!showLeftPayload)}
                        isIconOnly
                    >
                        {showLeftPayload ? <FaEyeSlash /> : <FaEye />}
                    </UIButton>
                </div>
                {typeof activitySet[0].payload.type === "string" && <div
                    className="text-tiny font-mono w-full"
                >
                    {activitySet[0].payload.type}
                </div>}
                <div>
                    {showLeftPayload && <UITextarea
                        className="font-mono w-full"
                        value={JSON.stringify(activitySet[0].payload, null, 2)}
                        maximumRows={6}
                        isReadOnly
                    />}
                </div>
                <div
                    className="text-tiny text-gray-500 font-mono w-full"
                >
                    {formatTime(activitySet[0].emitAt)}
                    {activitySet[0].initiator === WORKER_ACTIVITY_INITIATOR.RESPONSE
                        && activitySet[1] && <span>
                            &nbsp;({getResponseDuration(activitySet[1].emitAt, activitySet[0].emitAt)})
                        </span>}
                </div>
            </div>}
        </div>
        <div
            className="w-12"
        >
            <WorkerActivitiesActivityMiddleArrows
                activitySet={activitySet}
            />
        </div>
        <div>
            {activitySet[1] && <div
                className={clsx(
                    "bg-secondary-100 p-2 rounded-lg",
                    "flex flex-col gap-1",
                    activitySet[1].initiator === WORKER_ACTIVITY_INITIATOR.INITIATOR && "mb-4",
                    activitySet[1].initiator === WORKER_ACTIVITY_INITIATOR.RESPONSE && "mt-4",
                )}
            >
                <div
                    className="flex items-center gap-2"
                >
                    <UIChip
                        className="font-mono pl-2"
                        color="secondary"
                        size="sm"
                        startContent={<FaPersonRunning />}
                    >
                        {activitySet[1].worker}
                    </UIChip>
                    <WorkerActivitiesActivityHeaderChip
                        worker={activitySet[1].worker}
                        initiator={activitySet[1].initiator}
                    />
                    <UISpacer
                        isFlexFill
                    />
                    <UIButton
                        radius="lg"
                        variant="bordered"
                        color="primary"
                        size="sm"
                        onPress={() => setShowRightPayload(!showRightPayload)}
                        isIconOnly
                    >
                        {showRightPayload ? <FaEyeSlash /> : <FaEye />}
                    </UIButton>
                </div>
                {typeof activitySet[1].payload.type === "string" && <div
                    className="text-tiny font-mono w-full"
                >
                    {activitySet[1].payload.type}
                </div>}
                <div>
                    {showRightPayload && <UITextarea
                        className="font-mono w-full"
                        value={JSON.stringify(activitySet[1].payload, null, 2)}
                        maximumRows={6}
                        isReadOnly
                    />}
                </div>
                <div
                    className="text-tiny text-gray-500 font-mono w-full"
                >
                    {formatTime(activitySet[1].emitAt)}
                    {activitySet[1].initiator === WORKER_ACTIVITY_INITIATOR.RESPONSE
                        && activitySet[0] && <span>
                            &nbsp;({getResponseDuration(activitySet[0].emitAt, activitySet[1].emitAt)})
                        </span>}
                </div>
            </div>}
        </div>
    </React.Fragment>);
};
