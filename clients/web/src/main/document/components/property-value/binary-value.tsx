/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyValue
 * @description Binary Value
 */

import { TransferPropertyDraft } from "@imbricate-hummingbird/transfer-core";
import { IMBRICATE_PROPERTY_TYPE, ImbricateDatabaseSchemaPropertyOptionsBinary } from "@imbricate/core";
import { FC } from "react";
import { logger } from "workbox-core/_private";
import { DocumentBinaryValueBinaryPopover } from "./binary/binary-popover";
import { DocumentBinaryValueSelectedReference } from "./binary/selected-reference";

export type DocumentBinaryValueProps = {

    readonly originUniqueIdentifier: string;

    readonly propertyKey: string;
    readonly property: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY>;
    readonly updateProperty: (
        value: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY>,
    ) => void;

    readonly options: ImbricateDatabaseSchemaPropertyOptionsBinary;
};

export const DocumentBinaryValue: FC<DocumentBinaryValueProps> = (
    props: DocumentBinaryValueProps,
) => {

    return (<div
        className="flex gap-1 w-full"
    >
        <div
            className="flex-1 flex flex-col gap-1"
        >
            {props.property.value.map((item: string) => {

                return (<DocumentBinaryValueSelectedReference
                    key={item}
                    radius="sm"
                    originUniqueIdentifier={props.originUniqueIdentifier}
                    reference={item}
                    onDelete={() => {

                        logger.debug("Delete Binary", item);

                        const newValue: string[] =
                            props.property.value.filter((value) => {
                                return value !== item;
                            });

                        const fixedPropertyValue: TransferPropertyDraft<IMBRICATE_PROPERTY_TYPE.BINARY> = {
                            key: props.property.key,
                            type: props.property.type,
                            value: newValue,
                            variant: props.property.variant,
                        };

                        props.updateProperty(fixedPropertyValue);
                    }}
                />);
            })}
        </div>
        <div>
            <DocumentBinaryValueBinaryPopover
                radius="sm"
                originUniqueIdentifier={props.originUniqueIdentifier}
                propertyKey={props.propertyKey}
                currentProperty={{
                    key: props.propertyKey,
                    type: props.property
                        ? props.property.type
                        : IMBRICATE_PROPERTY_TYPE.BINARY,
                    value: props.property.value,
                    variant: props.property.variant,
                }}
                updateProperty={(newProperty) => {
                    props.updateProperty({
                        ...props.property,
                        value: newProperty.value,
                    });
                }}
                options={props.options}
            />
        </div>
    </div>);
};
