/**
 * <AUTHOR>
 * @namespace Configuration_Internationalization_DismissedGuidance
 * @description Intl
 */

import { HUMMINGBIRD_DISMISSED_GUIDANCE_ITEM } from "@imbricate-hummingbird/configuration";
import { DEFAULT_LOCALE } from "@imbricate-hummingbird/internationalization";
import { SudooInternationalization } from "@sudoo/internationalization";
import { IETF_LOCALE } from "@sudoo/locale";
import { enUSConfigurationDismissedGuidanceProfile } from "./locale/en-US";
import { jaJPConfigurationDismissedGuidanceProfile } from "./locale/ja-JP";
import { zhCNConfigurationDismissedGuidanceProfile } from "./locale/zh-CN";

export const configurationDismissedGuidanceInternationalization: SudooInternationalization<HUMMINGBIRD_DISMISSED_GUIDANCE_ITEM> =
    SudooInternationalization.create<HUMMINGBIRD_DISMISSED_GUIDANCE_ITEM>(
        DEFAULT_LOCALE,
    );

configurationDismissedGuidanceInternationalization.merge(
    IETF_LOCALE.ENGLISH_UNITED_STATES,
    enUSConfigurationDismissedGuidanceProfile,
);

configurationDismissedGuidanceInternationalization.merge(
    IETF_LOCALE.JAPANESE_JAPAN,
    jaJPConfigurationDismissedGuidanceProfile,
);

configurationDismissedGuidanceInternationalization.merge(
    IETF_LOCALE.CHINESE_SIMPLIFIED,
    zhCNConfigurationDismissedGuidanceProfile,
);
