/**
 * <AUTHOR>
 * @package UI
 * @namespace Keyboard
 * @description Keyboard
 */

import { Kbd } from "@heroui/kbd";
import { UIKeyboardKey } from "./types";

export type UIKeyboardProps = {

    readonly className?: string;

    readonly keys: UIKeyboardKey[];

    readonly children?: React.ReactNode;
};

export const UIKeyboard: React.FC<UIKeyboardProps> = (
    props: UIKeyboardProps,
) => {

    return (<Kbd
        className={props.className}
        keys={props.keys}
    >
        {props.children}
    </Kbd>);
};
