/**
 * <AUTHOR>
 * @namespace Store_Types
 * @description Disconnect
 */

import { OriginInstance } from "@imbricate-hummingbird/transfer-core";

export enum DISCONNECT_TYPE {

    NETWORK_ERROR = "NETWORK_ERROR",
    NOT_AUTHORIZED = "NOT_AUTHORIZED",
}

export const getDisconnectType = (statusCode: number): DISCONNECT_TYPE => {

    if (statusCode === 401) {
        return DISCONNECT_TYPE.NOT_AUTHORIZED;
    }

    return DISCONNECT_TYPE.NETWORK_ERROR;
};

export type DisconnectedItem = {

    readonly type: DISCONNECT_TYPE;

    readonly originUniqueIdentifier: string;
    readonly originInstance: OriginInstance;
};

export type DisconnectConfig = {

    readonly items: DisconnectedItem[];
};
