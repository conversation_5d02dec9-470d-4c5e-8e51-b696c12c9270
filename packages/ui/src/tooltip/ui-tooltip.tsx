/**
 * <AUTHOR>
 * @package UI
 * @namespace Tooltip
 * @description Tooltip
 */

import { Tooltip } from "@heroui/tooltip";
import type { FC, ReactNode } from "react";
import { UITooltipPlacement } from "./types";

export type UITooltipProps = {

    readonly className?: string;

    readonly content?: ReactNode;
    readonly placement?: UITooltipPlacement;

    readonly delay?: number;

    readonly children?: ReactNode;
};

export const UITooltip: FC<UITooltipProps> = (
    props: UITooltipProps,
) => {

    return (<Tooltip
        className={props.className}

        content={props.content}
        placement={props.placement}

        delay={props.delay}
    >
        {props.children}
    </Tooltip>);
};
