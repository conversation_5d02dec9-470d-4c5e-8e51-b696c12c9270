/**
 * <AUTHOR>
 * @namespace Main_Debug_Components_WorkerActivities_Util
 * @description Conclude Activities
 */

import { rootLogger } from "@/main/log/logger";
import { WORKER_ACTIVITY_DIRECTION, WORKER_ACTIVITY_INITIATOR, WorkerActivityItem } from "@imbricate-hummingbird/bridge-core";

const logger = rootLogger.fork({
    scopes: [
        "Main",
        "Debug",
        "Components",
    ],
});

const calculateInitiatorTime = (
    activitySet: [WorkerActivityItem | null, WorkerActivityItem | null],
): number => {

    if (activitySet[0]?.initiator === WORKER_ACTIVITY_INITIATOR.INITIATOR
        || activitySet[0]?.initiator === WORKER_ACTIVITY_INITIATOR.NOTIFICATION
    ) {

        return activitySet[0].emitAt;
    }

    if (activitySet[1]?.initiator === WORKER_ACTIVITY_INITIATOR.INITIATOR
        || activitySet[1]?.initiator === WORKER_ACTIVITY_INITIATOR.NOTIFICATION
    ) {

        return activitySet[1].emitAt;
    }

    return 0;
};

export type ConcludeWorkerActivitiesResult = {

    readonly result: [WorkerActivityItem | null, WorkerActivityItem | null][];
    readonly hangingActivities: WorkerActivityItem[];
}

export const concludeWorkerActivities = (
    activities: WorkerActivityItem[],
): ConcludeWorkerActivitiesResult => {

    const pendingToGoActivities: Set<WorkerActivityItem> = new Set();

    for (let i = 0; i < activities.length; i++) {
        pendingToGoActivities.add(activities[i]);
    }

    const result: [WorkerActivityItem | null, WorkerActivityItem | null][] = [];
    const hangingActivities: WorkerActivityItem[] = [];

    activities
        .filter((activity) => {
            return activity.initiator === WORKER_ACTIVITY_INITIATOR.INITIATOR
                || activity.initiator === WORKER_ACTIVITY_INITIATOR.NOTIFICATION;
        })
        .forEach((activity) => {

            pendingToGoActivities.delete(activity);

            if (activity.direction === WORKER_ACTIVITY_DIRECTION.RENDERER_TO_WORKER) {

                result.push([activity, null]);
            } else {

                result.push([null, activity]);
            }
        });

    const responseActivities = activities
        .filter((activity) => {
            return activity.initiator === WORKER_ACTIVITY_INITIATOR.RESPONSE;
        });

    outer: for (const activity of responseActivities) {

        if (activity.direction === WORKER_ACTIVITY_DIRECTION.WORKER_TO_RENDERER) {

            inner: for (const item of result) {

                const initiatorItem = item[0];
                if (!initiatorItem) {
                    continue inner;
                }

                if (initiatorItem.responseIdentifier === activity.responseIdentifier) {
                    item[1] = activity;
                    pendingToGoActivities.delete(activity);
                    continue outer;
                }
            }

            logger.warning("No renderer to worker item found for activity", {
                activity,
            });
        } else {

            inner: for (const item of result) {

                const responseItem = item[1];
                if (!responseItem) {
                    continue inner;
                }

                if (responseItem.responseIdentifier === activity.responseIdentifier) {
                    item[0] = activity;
                    pendingToGoActivities.delete(activity);
                    continue outer;
                }
            }

            logger.warning("No worker to renderer item found for activity", {
                activity,
            });
        }
    }

    const sortedResult = result.sort((a, b) => {

        const aInitiatorTime = calculateInitiatorTime(a);
        const bInitiatorTime = calculateInitiatorTime(b);

        return aInitiatorTime - bInitiatorTime;
    });

    for (const activity of pendingToGoActivities) {
        hangingActivities.push(activity);
    }

    return {
        result: sortedResult,
        hangingActivities,
    };
};
