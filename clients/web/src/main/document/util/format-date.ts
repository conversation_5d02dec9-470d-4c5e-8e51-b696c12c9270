/**
 * <AUTHOR>
 * @namespace Document_Utils
 * @description Format Date
 */

export const buildYearMonthDate = (date: Date): string => {

    const paddedYear: string = date.getFullYear().toString().padStart(4, "0");
    const paddedMonth: string = (date.getMonth() + 1).toString().padStart(2, "0");
    const paddedDate: string = date.getDate().toString().padStart(2, "0");

    return `${paddedYear}-${paddedMonth}-${paddedDate}`;
};

export const buildHourMinuteSecond = (date: Date): string => {

    const paddedHour: string = date.getHours().toString().padStart(2, "0");
    const paddedMinute: string = date.getMinutes().toString().padStart(2, "0");
    const paddedSecond: string = date.getSeconds().toString().padStart(2, "0");

    return `${paddedHour}:${paddedMinute}:${paddedSecond}`;
};
