/**
 * <AUTHOR>
 * @namespace Favorite_Util
 * @description Get Favorite Description
 */

import { FAVORITE_TYPE, FavoritePayloadSwitch } from "../types/favorite-definition";

export const getFavoriteDescription = <T extends FAVORITE_TYPE>(
    type: T,
    payload: FavoritePayloadSwitch<T>,
): string => {

    // FAVORITE_TYPE SWITCH
    switch (type) {
        case FAVORITE_TYPE.LENS: {
            const lensPayload: FavoritePayloadSwitch<FAVORITE_TYPE.LENS> =
                payload as FavoritePayloadSwitch<FAVORITE_TYPE.LENS>;

            return lensPayload.lensIdentifier;
        }
        case FAVORITE_TYPE.DATABASE: {
            const databasePayload: FavoritePayloadSwitch<FAVORITE_TYPE.DATABASE> =
                payload as FavoritePayloadSwitch<FAVORITE_TYPE.DATABASE>;

            return databasePayload.databaseUniqueIdentifier;
        }
    }

    return "";
};
