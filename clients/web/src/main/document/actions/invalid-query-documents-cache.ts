/**
 * <AUTHOR>
 * @namespace Document_Actions
 * @description Invalid Query Documents Cache
 */

import { ActionDescription, ActionDescriptionExecuterMetadata } from "@imbricate-hummingbird/interceptor-core";
import { ImbricateDocumentQuery } from "@imbricate/core";

export const createInvalidQueryDocumentsCacheAction = (
    originUniqueIdentifier: string,
    databaseUniqueIdentifier: string,
    query: ImbricateDocumentQuery,
    metadata: ActionDescriptionExecuterMetadata,
): ActionDescription => {

    return {
        actionName: "Invalid Query Documents Cache",
        actionDescription: `Invalidate query documents cache for origin ${originUniqueIdentifier} and database ${databaseUniqueIdentifier}`,
        actionPayload: {
            originUniqueIdentifier,
            databaseUniqueIdentifier,
            query,
        },
        executerMetadata: metadata,
    };
};
