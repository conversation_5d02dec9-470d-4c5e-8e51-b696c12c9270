/**
 * <AUTHOR>
 * @namespace Config_Util
 * @description Validate Configuration
 */

import { ExportableConfiguration } from "../types/configuration";

export const validateExportableConfiguration = (
    configuration: ExportableConfiguration,
): boolean => {

    if (typeof configuration !== "object") {
        return false;
    }

    if (typeof configuration.version !== "string") {
        return false;
    }
    if (typeof configuration.identifier !== "string") {
        return false;
    }

    if (typeof configuration.exportedAt !== "number") {
        return false;
    }

    if (configuration.lenses !== null && typeof configuration.lenses !== "object") {
        return false;
    }
    if (configuration.origins !== null && typeof configuration.origins !== "object") {
        return false;
    }

    return true;
};
