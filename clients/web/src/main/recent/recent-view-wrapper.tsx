/**
 * <AUTHOR>
 * @namespace Recent
 * @description Recent View Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const RecentView = LazyLoadComponent(
    () => import("./recent-view"),
    "Recent View",
);

export const RecentViewWrapper: FC = () => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Recent View"
            fullHeight
        />}
    >
        <RecentView />
    </React.Suspense>);
};
