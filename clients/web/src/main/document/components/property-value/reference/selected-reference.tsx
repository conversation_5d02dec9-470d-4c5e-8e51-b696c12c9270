/**
 * <AUTHOR>
 * @namespace Document_Components_PropertyValue_Reference
 * @description Selected Reference
 */

import { getRouteDocumentView } from "@imbricate-hummingbird/navigation-core";
import { LoadingWrapper } from "@imbricate-hummingbird/react-components";
import { UIButton } from "@imbricate-hummingbird/ui";
import { IImbricateDatabase, IImbricateDocument, ImbricatePropertyValueObjectReference } from "@imbricate/core";
import { FC } from "react";
import { FaDatabase, FaExternalLinkAlt } from "react-icons/fa";
import { MdDelete } from "react-icons/md";
import { Link } from "react-router-dom";
import { UseDatabaseResponseSymbol, useDatabase } from "../../../../database/hooks/use-database";
import { rootLogger } from "../../../../log/logger";
import { UseDocumentResponseSymbol, useOldDocument } from "../../../hooks/use-document";
import { getDocumentPrimary } from "../../../util/primary";

const logger = rootLogger.fork({
    scopes: [
        "Document",
        "Components",
        "PropertyValue",
        "Reference",
        "SelectedReference",
    ],
});

export type DocumentReferenceValueSelectedReferenceProps = {

    readonly reference: ImbricatePropertyValueObjectReference;
    readonly onDelete?: () => void;
};

export const DocumentReferenceValueSelectedReference: FC<DocumentReferenceValueSelectedReferenceProps> = (
    props: DocumentReferenceValueSelectedReferenceProps,
) => {

    const database: IImbricateDatabase | UseDatabaseResponseSymbol =
        useDatabase(props.reference.databaseUniqueIdentifier);

    const document: IImbricateDocument | UseDocumentResponseSymbol =
        useOldDocument(
            props.reference.databaseUniqueIdentifier,
            props.reference.documentUniqueIdentifier,
        );

    if (typeof document === "symbol" || typeof database === "symbol") {

        return (<LoadingWrapper
            debugDescription="Selected Reference"
            size="md"
            color="default"
        />);
    }

    const properties = document.getProperties();

    if (typeof properties === "symbol") {

        logger.error("Properties is symbol", {
            databaseUniqueIdentifier: props.reference.databaseUniqueIdentifier,
            documentUniqueIdentifier: props.reference.documentUniqueIdentifier,
        });
        return (<LoadingWrapper
            debugDescription="Selected Reference"
            size="md"
            color="default"
        />);
    }

    const primaryKey = getDocumentPrimary(
        database.schema,
        properties.properties,
    );

    const displayKey = primaryKey ?? document.uniqueIdentifier;

    return (<div
        className="flex items-center gap-2 w-full"
    >
        <div
            className="flex-1"
        >
            <div
                className="flex gap-2 items-center"
            >
                <Link
                    className="hover:cursor-pointer"
                    to={getRouteDocumentView(
                        props.reference.databaseUniqueIdentifier,
                        props.reference.documentUniqueIdentifier,
                    )}
                >
                    {displayKey}
                </Link>
                <Link
                    className="hover:cursor-pointer"
                    to={getRouteDocumentView(
                        props.reference.databaseUniqueIdentifier,
                        props.reference.documentUniqueIdentifier,
                    )}
                    target="_blank"
                >
                    <FaExternalLinkAlt
                        className="text-small"
                    />
                </Link>
            </div>
            <div
                className="text-tiny text-gray-600 flex gap-1 items-center"
            >
                <FaDatabase />
                {database.databaseName}
            </div>
        </div>
        {typeof props.onDelete === "function" && <UIButton
            color="danger"
            variant="flat"
            isIconOnly
            radius="sm"
            onPress={props.onDelete}
        >
            <MdDelete
                className="text-large"
            />
        </UIButton>}
    </div>);
};
