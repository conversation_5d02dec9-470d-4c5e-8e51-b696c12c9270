/**
 * <AUTHOR>
 * @namespace Config_Types
 * @description Configuration
 */

import { LensConfig } from "@imbricate-hummingbird/configuration";
import { OriginStorageInstance } from "@imbricate-hummingbird/origin-central";

export type ExportableConfiguration = {

    readonly version: string;
    readonly identifier: string;

    readonly exportedAt: number;

    readonly lenses: LensConfig | null;
    readonly origins: OriginStorageInstance | null;
};
