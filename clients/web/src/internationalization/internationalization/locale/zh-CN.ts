/**
 * <AUTHOR>
 * @namespace Internationalization_Internationalization_Locale
 * @description Zh-CN
 */

import { IETF_LOCALE } from "@sudoo/locale";

// spell-checker: disable
export const zhCNInternationalizationProfile: Record<IETF_LOCALE, string> = {

    [IETF_LOCALE.CHINESE_SIMPLIFIED]: "简体中文",
    [IETF_LOCALE.CHINESE_TRADITIONAL]: "繁体中文",
    [IETF_LOCALE.ENGLISH_CANADA]: "加拿大英语",
    [IETF_LOCALE.ENGLISH_UNITED_KINGDOM]: "英国英语",
    [IETF_LOCALE.ENGLISH_UNITED_STATES]: "美式英语",
    [IETF_LOCALE.FRENCH_CANADA]: "加拿大法语",
    [IETF_LOCALE.FRENCH_FRANCE]: "法国法语",
    [IETF_LOCALE.JAPANESE_JAPAN]: "日语",
    [IETF_LOCALE.KOREAN_KOREA]: "韩语",
    [IETF_LOCALE.MAORI_NEW_ZEALAND]: "毛利语",
    [IETF_LOCALE.RUSSIAN_RUSSIA]: "俄语",
    [IETF_LOCALE.SPANISH_MEXICO]: "墨西哥西班牙语",
    [IETF_LOCALE.SPANISH_SPAIN]: "西班牙语",
};
// spell-checker: enable
