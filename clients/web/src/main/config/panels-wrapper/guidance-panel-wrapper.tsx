/**
 * <AUTHOR>
 * @namespace Config_Panels_Wrapper
 * @description Guidance Panel Wrapper
 */

import { LazyLoadComponent, LoadingWrapper } from "@imbricate-hummingbird/react-components";
import React, { FC } from "react";

const ConfigGuidancePanel = LazyLoadComponent(
    () => import("../panels/guidance-panel"),
    "Config Guidance Panel",
);

export type ConfigGuidancePanelWrapperProps = {
};

export const ConfigGuidancePanelWrapper: FC<ConfigGuidancePanelWrapperProps> = (
    _props: ConfigGuidancePanelWrapperProps,
) => {

    return (<React.Suspense
        fallback={<LoadingWrapper
            debugDescription="Config Guidance Panel"
            fullHeight
        />}
    >
        <ConfigGuidancePanel />
    </React.Suspense>);
};
